# Threats to Validity (Run 20251001_190703)

This section summarises potential threats to the validity of the reported results and the steps taken to mitigate them.

## Internal validity
- Data leakage: The split is performed on original samples before any balancing or feature construction; test data are never used for model selection. Nested cross-validation is available for unbiased performance estimation.
- Final test gating: Final test evaluation is only performed when cross-validation is valid or when explicitly enabled. This prevents optimistic leakage into the test set.
- Calibration: Post-hoc calibration using isotonic regression was applied using 5-fold CV on the training set to improve probability estimates.

## Construct validity
- Label space and grouping: Grouping of error types is enabled; this trades finer granularity for improved sample efficiency. An ablation without grouping is available via CLI and reported separately.
- Class balance: Inclusion of synthetic/structural 'NoError' samples is disabled; this can affect decision thresholds. An ablation without NoError augmentation is available.

## External validity
- Dataset representativeness: The dataset originates from controlled generations and may not capture the full variability of real-world code errors. Distribution shift to new sources, languages, or styles may degrade performance.
- Generalisation risk: Token and structural features may overfit to dataset-specific artefacts; future work should validate on independent corpora.

## Statistical conclusion validity
- Randomness sensitivity: A seed sensitivity study can be enabled to quantify variability across random initialisations.
- Cross-validation: K-fold CV is used for model selection; optional nested CV provides an unbiased outer estimate. Bootstrap confidence intervals are computed for key test metrics when the final test is run.

## Calibration validity
- Probability calibration: Pre- and optional post-hoc calibration (isotonic or sigmoid) are evaluated via Brier score and reliability diagrams. Miscalibration can affect decision-making and downstream tooling.

## Implementation and reproducibility
- Reproducibility: Random states are centrally controlled via configuration and CLI. Runs are logged with configuration, seeds, and artefacts packaged for provenance.
- Complexity and efficiency: Training/inference times and parameter counts are recorded per model to contextualise performance vs cost.

## Residual risks
- Preprocessing assumptions (e.g., tokenisation, AST parsing) may introduce biases. Edge cases in code formatting or unparsable snippets can affect feature extraction and downstream predictions.
- Class boundary ambiguity (e.g., SyntaxError vs ValueError) can limit ceiling performance; confusion analysis highlights these cases.
