{"metadata": {"model_name": "Neural Network", "run_id": "20251001_212858", "timestamp": "2025-10-01T21:45:37.836543+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.030255852430850248, "std_abs_shap": 0.045194288445954085, "max_abs_shap": 0.29892874839625316, "feature_index": 55}, {"rank": 2, "feature_name": "feat_function_calls", "mean_abs_shap": 0.023751811483057626, "std_abs_shap": 0.03908138101710062, "max_abs_shap": 0.3458410126764901, "feature_index": 25}, {"rank": 3, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.023065305174337355, "std_abs_shap": 0.04763659724666012, "max_abs_shap": 0.4333011520987425, "feature_index": 49}, {"rank": 4, "feature_name": "feat_return_statements", "mean_abs_shap": 0.021401537187958466, "std_abs_shap": 0.05754007381944843, "max_abs_shap": 0.6486681387557697, "feature_index": 58}, {"rank": 5, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.021269262888687843, "std_abs_shap": 0.03254339403844429, "max_abs_shap": 0.2357540422446802, "feature_index": 64}, {"rank": 6, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.018139705489823226, "std_abs_shap": 0.026808455029566997, "max_abs_shap": 0.23443236493584477, "feature_index": 44}, {"rank": 7, "feature_name": "feat_colon_count", "mean_abs_shap": 0.015612041414668354, "std_abs_shap": 0.04592971274949694, "max_abs_shap": 0.4783594829459092, "feature_index": 15}, {"rank": 8, "feature_name": "feat_assignments", "mean_abs_shap": 0.012590763524281012, "std_abs_shap": 0.023409145901007514, "max_abs_shap": 0.20481373495405344, "feature_index": 2}, {"rank": 9, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.012585229888438416, "std_abs_shap": 0.032091932143039195, "max_abs_shap": 0.392058252433071, "feature_index": 16}, {"rank": 10, "feature_name": "feat_line_count", "mean_abs_shap": 0.012308800502114798, "std_abs_shap": 0.04115447946770487, "max_abs_shap": 0.43540363415860606, "feature_index": 45}, {"rank": 11, "feature_name": "feat_function_defs", "mean_abs_shap": 0.012253098491988832, "std_abs_shap": 0.02976381821621672, "max_abs_shap": 0.5146992104027815, "feature_index": 26}, {"rank": 12, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.010907710053774581, "std_abs_shap": 0.025724988088041873, "max_abs_shap": 0.29407491961495485, "feature_index": 3}, {"rank": 13, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.010193839256303669, "std_abs_shap": 0.028153454863205807, "max_abs_shap": 0.3110103039610289, "feature_index": 12}, {"rank": 14, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.010180417596535176, "std_abs_shap": 0.019767900141163496, "max_abs_shap": 0.1785485093484505, "feature_index": 63}, {"rank": 15, "feature_name": "feat_has_syntax_error", "mean_abs_shap": 0.009609109728572486, "std_abs_shap": 0.023499972086094407, "max_abs_shap": 0.1809047682308393, "feature_index": 29}, {"rank": 16, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.008970964642641046, "std_abs_shap": 0.018313590469692505, "max_abs_shap": 0.13459300966523602, "feature_index": 48}, {"rank": 17, "feature_name": "feat_if_statements", "mean_abs_shap": 0.008276739627184672, "std_abs_shap": 0.021737303246613437, "max_abs_shap": 0.22507212712288785, "feature_index": 31}, {"rank": 18, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.007448132112404239, "std_abs_shap": 0.01877659748386908, "max_abs_shap": 0.24336404848882606, "feature_index": 51}, {"rank": 19, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.006811897710885248, "std_abs_shap": 0.02081631569867965, "max_abs_shap": 0.2852104940638803, "feature_index": 0}, {"rank": 20, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.006603631208801745, "std_abs_shap": 0.013835956068586891, "max_abs_shap": 0.10638666185230129, "feature_index": 52}], "total_importance": 0.3488724283601051, "top_10_importance_ratio": 0.5474216202235621}, "feature_statistics": {"mean_importance": 0.004913696174085987, "median_importance": 0.0018493834128353892, "std_importance": 0.006843900042416212, "max_importance": 0.030255852430850248, "min_importance": 0.0, "importance_concentration": 0.34323081857673304}, "ast_feature_analysis": {"structural_features": [{"name": "feat_nodes_with_children", "importance": 0.030255852430850248, "rank": 55}, {"name": "feat_tree_depth", "importance": 0.021269262888687843, "rank": 64}, {"name": "feat_leaf_nodes", "importance": 0.018139705489823226, "rank": 44}, {"name": "feat_total_nodes", "importance": 0.010180417596535176, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.006603631208801745, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.008276739627184672, "rank": 31}, {"name": "feat_for_loops", "importance": 0.004956946677699135, "rank": 24}, {"name": "feat_keyword_if_count", "importance": 0.0021012525509966832, "rank": 37}, {"name": "feat_keyword_for_count", "importance": 0.0020508639814558706, "rank": 36}, {"name": "feat_if_else_chains", "importance": 0.0018500718705754156, "rank": 30}, {"name": "feat_nested_ifs", "importance": 0.0016636815691913804, "rank": 53}, {"name": "feat_while_loops", "importance": 0.0011009261264073667, "rank": 68}, {"name": "feat_keyword_while_count", "importance": 0.0010445113505775973, "rank": 41}, {"name": "feat_string_formatting", "importance": 0.00025833500523619895, "rank": 61}, {"name": "feat_nested_loops", "importance": 0.0001251303143740795, "rank": 54}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.008970964642641046, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.004404584640801329, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.003630832123801697, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.003539118605979668, "rank": 50}, {"name": "feat_avg_branching_factor", "importance": 0.002072920921496383, "rank": 5}], "syntactic_features": [{"name": "feat_function_calls", "importance": 0.023751811483057626, "rank": 25}, {"name": "feat_max_call_args", "importance": 0.023065305174337355, "rank": 49}, {"name": "feat_assignments", "importance": 0.012590763524281012, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.010907710053774581, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.010193839256303669, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0019091727772272888, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.021401537187958466, "rank": 58}, {"name": "feat_colon_count", "importance": 0.015612041414668354, "rank": 15}, {"name": "feat_comparison_ops", "importance": 0.012585229888438416, "rank": 16}, {"name": "feat_line_count", "importance": 0.012308800502114798, "rank": 45}, {"name": "feat_function_defs", "importance": 0.012253098491988832, "rank": 26}, {"name": "feat_has_syntax_error", "importance": 0.009609109728572486, "rank": 29}, {"name": "feat_max_function_params", "importance": 0.007448132112404239, "rank": 51}, {"name": "feat_arithmetic_ops", "importance": 0.006811897710885248, "rank": 0}, {"name": "feat_keyword_def_count", "importance": 0.006420052142125024, "rank": 34}, {"name": "feat_subscript_access", "importance": 0.00629732654138388, "rank": 62}, {"name": "feat_import_statements", "importance": 0.0025937569270830605, "rank": 32}, {"name": "feat_lambda_functions", "importance": 0.002550750144352631, "rank": 42}, {"name": "feat_paren_count", "importance": 0.0020173545553777562, "rank": 56}, {"name": "feat_lambda_usage", "importance": 0.0019991506477321365, "rank": 43}, {"name": "feat_equals_count", "importance": 0.0018493834128353892, "rank": 23}, {"name": "feat_keyword_class_count", "importance": 0.0015333795974277432, "rank": 33}, {"name": "feat_char_count", "importance": 0.0015282684835510576, "rank": 13}, {"name": "feat_boolean_ops", "importance": 0.0014460644838032095, "rank": 8}, {"name": "feat_bracket_count", "importance": 0.0014175117945803872, "rank": 10}, {"name": "feat_word_count", "importance": 0.0008572002415192248, "rank": 70}, {"name": "feat_keyword_import_count", "importance": 0.0008344200897352134, "rank": 38}, {"name": "feat_class_defs", "importance": 0.0007229029506373004, "rank": 14}, {"name": "feat_list_comprehensions", "importance": 0.0006378733015265449, "rank": 46}, {"name": "feat_keyword_return_count", "importance": 0.0005965611319555766, "rank": 39}, {"name": "feat_with_statements", "importance": 0.0005443985778844423, "rank": 69}, {"name": "feat_brace_count", "importance": 0.0005304519519205261, "rank": 9}, {"name": "feat_generator_expressions", "importance": 0.0005181116498616834, "rank": 27}, {"name": "feat_context_managers", "importance": 0.0004452238283453035, "rank": 17}, {"name": "feat_list_comps", "importance": 0.00040408591970001877, "rank": 47}, {"name": "feat_generator_exps", "importance": 0.0001840010576377842, "rank": 28}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.08644886961469823, "control_flow_features": 0.0234284590736984, "complexity_features": 0.02261842093472012, "syntactic_features": 0.08241860226898154, "other_features": 0.13395807646800673}}}, "visualisations_generated": ["shap_summary_neural_network_20251001_212858.png", "shap_bar_neural_network_20251001_212858.png", "shap_waterfall_neural_network_sample1_20251001_212858.png", "shap_waterfall_neural_network_sample2_20251001_212858.png", "shap_waterfall_neural_network_sample3_20251001_212858.png", "shap_dependence_neural_network_feat1_20251001_212858.png", "shap_dependence_neural_network_feat2_20251001_212858.png"]}