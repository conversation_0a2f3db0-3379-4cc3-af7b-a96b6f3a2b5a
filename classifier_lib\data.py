"""Data extraction and feature creation utilities for the classifiers"""

import ast
from typing import Dict, Any
import numpy as np


def create_enhanced_test_cases():
    """Create more comprehensive test cases including edge cases that models struggle with."""
    test_cases = [
        {
            # VariableError: undefined variable - enhanced with context
            "code": "print(unknown_var)",
            "correct_code": "unknown_var = 'hello'\nprint(unknown_var)",
            "ast_message": "name 'unknown_var' is not defined",
            "error_description": "Variable not defined",
        },
        {
            # SyntaxError: missing colon
            "code": 'if x > 5\n    print("greater")',
            "correct_code": 'if x > 5:\n    print("greater")',
            "ast_message": "expected ':'",
            "error_description": "Missing colon in if statement",
        },
        {
            # TypeError: string concatenation with int - enhanced
            "code": 'result = "Number: " + 25',
            "correct_code": 'result = "Number: " + str(25)',
            "ast_message": 'can only concatenate str (not "int") to str',
            "error_description": "Type mismatch in concatenation",
        },
        {
            # NoError: correct code
            "code": 'def greet(name):\n    return f"Hello, {name}!"\n\nprint(greet("World"))',
            "correct_code": "",  # No correct version needed
            "ast_message": "",
            "error_description": "No errors found",
        },
        {
            # IndexError: list index out of range
            "code": "my_list = [1, 2, 3]\nprint(my_list[5])",
            "correct_code": "my_list = [1, 2, 3]\nprint(my_list[2])",
            "ast_message": "list index out of range",
            "error_description": "Index out of range",
        },
        {
            # ArithmeticError: division by zero
            "code": "result = 10 / 0",
            "correct_code": "result = 10 / 2",
            "ast_message": "division by zero",
            "error_description": "Division by zero",
        },
        {
            # LogicalError: off by one
            "code": "for i in range(1, 11):\n    print(i)",
            "correct_code": "for i in range(1, 10):\n    print(i)",
            "ast_message": "",
            "error_description": "Off by one error in range",
        },
    ]
    return test_cases


def create_test_cases():
    """Create test cases for evaluating the model predictions."""
    test_cases = [
        {
            # VariableError: undefined variable
            "code": "print(unknown_var)",
            "correct_code": "unknown_var = 'hello'\nprint(unknown_var)",
            "ast_message": "name 'unknown_var' is not defined",
            "error_description": "Variable not defined",
        },
        {
            # SyntaxError: missing colon
            "code": 'if x > 5\n    print("greater")',
            "correct_code": 'if x > 5:\n    print("greater")',
            "ast_message": "expected ':'",
            "error_description": "Missing colon in if statement",
        },
        {
            # TypeError: string concatenation with int
            "code": 'result = "Number: " + 25',
            "correct_code": 'result = "Number: " + str(25)',
            "ast_message": 'can only concatenate str (not "int") to str',
            "error_description": "Type mismatch in concatenation",
        },
        {
            # NoError: correct code
            "code": 'def greet(name):\n    return f"Hello, {name}!"\n\nprint(greet("World"))',
            "correct_code": "",  # No correct version needed
            "ast_message": "",
            "error_description": "No errors found",
        },
    ]
    return test_cases


def create_feature_text(row):
    """
    Creates feature representation based on AST analysis of buggy code only.
    """
    # Get the buggy code
    buggy_code = str(row.get("code", ""))

    if not buggy_code.strip():
        return get_minimal_features()

    # Extract AST-based features
    ast_features = extract_ast_features(buggy_code)

    return ast_features


def extract_ast_features(code: str) -> Dict[str, Any]:
    """
    Extract structural features from the AST of Python code.
    Returns a dictionary of numerical and categorical features.
    """
    features = {}
    try:
        tree = ast.parse(code)
        features.update(extract_node_counts(tree))
        features.update(extract_tree_structure(tree))
        features.update(extract_control_flow_features(tree))
        features.update(extract_complexity_features(tree))
        features.update(extract_syntactic_patterns(tree))
    except SyntaxError:
        features.update(extract_fallback_features(code))
        features["has_syntax_error"] = 1
    except Exception:
        features.update(get_minimal_features())
        features["parse_error"] = 1
    return features


def extract_node_counts(tree: ast.AST) -> Dict[str, int]:
    """Extract counts of different AST node types."""
    node_counts = {}

    # Count all nodes by type
    node_type_counts = {}
    for node in ast.walk(tree):
        node_type = type(node).__name__
        node_type_counts[node_type] = node_type_counts.get(node_type, 0) + 1

    # Map to meaningful feature names
    node_counts["total_nodes"] = sum(node_type_counts.values())
    node_counts["function_defs"] = node_type_counts.get("FunctionDef", 0)
    node_counts["class_defs"] = node_type_counts.get("ClassDef", 0)
    node_counts["if_statements"] = node_type_counts.get("If", 0)
    node_counts["for_loops"] = node_type_counts.get("For", 0)
    node_counts["while_loops"] = node_type_counts.get("While", 0)
    node_counts["try_statements"] = node_type_counts.get("Try", 0)
    node_counts["with_statements"] = node_type_counts.get("With", 0)
    node_counts["function_calls"] = node_type_counts.get("Call", 0)
    node_counts["assignments"] = node_type_counts.get("Assign", 0)
    node_counts["aug_assignments"] = node_type_counts.get("AugAssign", 0)
    node_counts["return_statements"] = node_type_counts.get("Return", 0)
    node_counts["import_statements"] = node_type_counts.get(
        "Import", 0
    ) + node_type_counts.get("ImportFrom", 0)
    node_counts["list_comps"] = node_type_counts.get("ListComp", 0)
    node_counts["dict_comps"] = node_type_counts.get("DictComp", 0)
    node_counts["set_comps"] = node_type_counts.get("SetComp", 0)
    node_counts["generator_exps"] = node_type_counts.get("GeneratorExp", 0)
    node_counts["lambda_functions"] = node_type_counts.get("Lambda", 0)
    node_counts["raise_statements"] = node_type_counts.get("Raise", 0)
    node_counts["assert_statements"] = node_type_counts.get("Assert", 0)
    node_counts["break_statements"] = node_type_counts.get("Break", 0)
    node_counts["continue_statements"] = node_type_counts.get("Continue", 0)

    return node_counts


def extract_tree_structure(tree: ast.AST) -> Dict[str, int]:
    """Extract structural features about the AST tree."""
    features = {}

    # Calculate tree depth
    def get_depth(node, current_depth=0):
        max_depth = current_depth
        for child in ast.iter_child_nodes(node):
            child_depth = get_depth(child, current_depth + 1)
            max_depth = max(max_depth, child_depth)
        return max_depth

    features["tree_depth"] = get_depth(tree)

    # Calculate branching factor statistics
    branching_factors = []
    for node in ast.walk(tree):
        children = list(ast.iter_child_nodes(node))
        if children:
            branching_factors.append(len(children))

    if branching_factors:
        features["max_branching_factor"] = max(branching_factors)
        features["avg_branching_factor"] = int(np.mean(branching_factors))
        features["nodes_with_children"] = len(branching_factors)
    else:
        features["max_branching_factor"] = 0
        features["avg_branching_factor"] = 0
        features["nodes_with_children"] = 0

    # Count leaf nodes (nodes with no children)
    leaf_count = 0
    for node in ast.walk(tree):
        if not list(ast.iter_child_nodes(node)):
            leaf_count += 1
    features["leaf_nodes"] = leaf_count

    return features


def extract_control_flow_features(tree: ast.AST) -> Dict[str, int]:
    """Extract features related to control flow structures."""
    features = {}

    # Count nested control structures
    max_nesting_depth = 0

    def analyse_nesting(node, depth=0):
        nonlocal max_nesting_depth
        max_nesting_depth = max(max_nesting_depth, depth)

        # Control flow nodes that increase nesting
        control_flow_types = (
            ast.If,
            ast.For,
            ast.While,
            ast.Try,
            ast.With,
            ast.FunctionDef,
            ast.ClassDef,
        )
        if isinstance(node, control_flow_types):
            depth += 1
            max_nesting_depth = max(max_nesting_depth, depth)

        for child in ast.iter_child_nodes(node):
            analyse_nesting(child, depth)

    analyse_nesting(tree)
    features["max_nesting_depth"] = max_nesting_depth

    # Count specific control flow patterns
    features["nested_loops"] = 0
    features["nested_ifs"] = 0
    features["if_else_chains"] = 0

    for node in ast.walk(tree):
        if isinstance(node, (ast.For, ast.While)):
            # Check for nested loops
            for child in ast.walk(node):
                if child != node and isinstance(child, (ast.For, ast.While)):
                    features["nested_loops"] += 1
                    break

        elif isinstance(node, ast.If):
            # Check for nested ifs
            for child in ast.walk(node):
                if child != node and isinstance(child, ast.If):
                    features["nested_ifs"] += 1
                    break

            # Count if-elif-else chains
            elif_count = len(node.orelse) if hasattr(node, "orelse") else 0
            if elif_count > 0:
                features["if_else_chains"] += 1

    # Exception handling patterns
    features["try_except_blocks"] = 0
    features["try_finally_blocks"] = 0
    features["bare_except"] = 0

    for node in ast.walk(tree):
        if isinstance(node, ast.Try):
            if node.handlers:
                features["try_except_blocks"] += 1
                # Check for bare except clauses
                for handler in node.handlers:
                    if handler.type is None:
                        features["bare_except"] += 1
            if node.finalbody:
                features["try_finally_blocks"] += 1

    return features


def extract_complexity_features(tree: ast.AST) -> Dict[str, int]:
    """Extract features related to code complexity."""
    features = {}

    # Cyclomatic complexity approximation
    complexity_nodes = 0
    for node in ast.walk(tree):
        if isinstance(
            node,
            (
                ast.If,
                ast.For,
                ast.While,
                ast.Try,
                ast.ExceptHandler,
                ast.BoolOp,
                ast.Compare,
            ),
        ):
            complexity_nodes += 1
    features["cyclomatic_complexity"] = complexity_nodes + 1  # +1 for base path

    # Function complexity
    function_complexities = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            func_complexity = 1
            for child in ast.walk(node):
                if isinstance(
                    child, (ast.If, ast.For, ast.While, ast.Try, ast.ExceptHandler)
                ):
                    func_complexity += 1
            function_complexities.append(func_complexity)

    if function_complexities:
        features["max_function_complexity"] = max(function_complexities)
        features["avg_function_complexity"] = int(np.mean(function_complexities))
    else:
        features["max_function_complexity"] = 0
        features["avg_function_complexity"] = 0

    # Parameter and argument complexity
    features["max_function_params"] = 0
    features["max_call_args"] = 0

    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            param_count = len(node.args.args)
            if node.args.vararg:
                param_count += 1
            if node.args.kwarg:
                param_count += 1
            features["max_function_params"] = max(
                features["max_function_params"], param_count
            )

        elif isinstance(node, ast.Call):
            arg_count = len(node.args) + len(node.keywords)
            features["max_call_args"] = max(features["max_call_args"], arg_count)

    return features


def extract_syntactic_patterns(tree: ast.AST) -> Dict[str, int]:
    """Extract features related to syntactic patterns and idioms."""
    features = {}

    # Common Python patterns
    features["list_comprehensions"] = 0
    features["dict_comprehensions"] = 0
    features["generator_expressions"] = 0
    features["lambda_usage"] = 0
    features["decorator_usage"] = 0
    features["context_managers"] = 0
    features["string_formatting"] = 0

    for node in ast.walk(tree):
        if isinstance(node, ast.ListComp):
            features["list_comprehensions"] += 1
        elif isinstance(node, ast.DictComp):
            features["dict_comprehensions"] += 1
        elif isinstance(node, ast.GeneratorExp):
            features["generator_expressions"] += 1
        elif isinstance(node, ast.Lambda):
            features["lambda_usage"] += 1
        elif isinstance(node, ast.With):
            features["context_managers"] += 1
        elif isinstance(node, ast.FunctionDef) and node.decorator_list:
            features["decorator_usage"] += len(node.decorator_list)
        elif isinstance(node, ast.Call):
            # Check for string formatting patterns
            if (
                isinstance(node.func, ast.Attribute)
                and isinstance(node.func.attr, str)
                and node.func.attr in ["format", "join"]
            ):
                features["string_formatting"] += 1

    # Variable and name patterns
    features["builtin_calls"] = 0
    features["attribute_access"] = 0
    features["subscript_access"] = 0

    builtin_names = {
        "print",
        "len",
        "range",
        "str",
        "int",
        "float",
        "list",
        "dict",
        "set",
        "tuple",
    }

    for node in ast.walk(tree):
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            if node.func.id in builtin_names:
                features["builtin_calls"] += 1
        elif isinstance(node, ast.Attribute):
            features["attribute_access"] += 1
        elif isinstance(node, ast.Subscript):
            features["subscript_access"] += 1

    # Operator usage
    features["arithmetic_ops"] = 0
    features["comparison_ops"] = 0
    features["boolean_ops"] = 0

    for node in ast.walk(tree):
        if isinstance(node, ast.BinOp):
            if isinstance(
                node.op, (ast.Add, ast.Sub, ast.Mult, ast.Div, ast.Mod, ast.Pow)
            ):
                features["arithmetic_ops"] += 1
        elif isinstance(node, ast.Compare):
            features["comparison_ops"] += 1
        elif isinstance(node, ast.BoolOp):
            features["boolean_ops"] += 1

    return features


def extract_fallback_features(code: str) -> Dict[str, int]:
    """Extract basic features when AST parsing fails due to syntax errors."""
    features = {}

    # Basic text-based analysis
    lines = [line.strip() for line in code.split("\n") if line.strip()]
    features["line_count"] = len(lines)
    features["char_count"] = len(code)
    features["word_count"] = len(code.split())

    # Count basic keywords (even with syntax errors)
    keywords = [
        "def",
        "class",
        "if",
        "for",
        "while",
        "try",
        "except",
        "import",
        "return",
    ]
    for keyword in keywords:
        count = code.count(f" {keyword} ") + code.count(f"{keyword} ")
        features[f"keyword_{keyword}_count"] = count

    # Count basic symbols
    features["paren_count"] = code.count("(")
    features["bracket_count"] = code.count("[")
    features["brace_count"] = code.count("{")
    features["colon_count"] = code.count(":")
    features["semicolon_count"] = code.count(";")
    features["equals_count"] = code.count("=")

    # Set all other features to 0
    for key in [
        "total_nodes",
        "tree_depth",
        "max_nesting_depth",
        "cyclomatic_complexity",
    ]:
        features[key] = 0

    return features


def get_minimal_features() -> Dict[str, int]:
    """Return minimal feature set when all parsing fails."""
    return {
        "total_nodes": 0,
        "line_count": 0,
        "char_count": 0,
        "tree_depth": 0,
        "max_nesting_depth": 0,
        "cyclomatic_complexity": 0,
        "function_defs": 0,
        "class_defs": 0,
        "if_statements": 0,
        "for_loops": 0,
        "while_loops": 0,
    }
