{"metadata": {"model_name": "XGBoost", "run_id": "20251001_204942", "timestamp": "2025-10-01T21:24:34.646041+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.022564051171660177, "std_abs_shap": 0.05594779232207453, "max_abs_shap": 0.49123310948445315, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.018413296306449178, "std_abs_shap": 0.06076820496570306, "max_abs_shap": 0.6698046195183932, "feature_index": 58}, {"rank": 3, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.013770521878692644, "std_abs_shap": 0.024195480654835, "max_abs_shap": 0.21065063305677872, "feature_index": 44}, {"rank": 4, "feature_name": "feat_assignments", "mean_abs_shap": 0.010804002252251794, "std_abs_shap": 0.02426148641010457, "max_abs_shap": 0.1692363504464459, "feature_index": 2}, {"rank": 5, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.009101906449408611, "std_abs_shap": 0.019303258571030824, "max_abs_shap": 0.18349896827938286, "feature_index": 55}, {"rank": 6, "feature_name": "feat_function_calls", "mean_abs_shap": 0.008327203402620163, "std_abs_shap": 0.019252039545169845, "max_abs_shap": 0.28952425046605623, "feature_index": 25}, {"rank": 7, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.008171616933404659, "std_abs_shap": 0.021093374114942536, "max_abs_shap": 0.18295223244071548, "feature_index": 48}, {"rank": 8, "feature_name": "feat_if_statements", "mean_abs_shap": 0.007811478583100929, "std_abs_shap": 0.025361048883032096, "max_abs_shap": 0.2600533648829235, "feature_index": 31}, {"rank": 9, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.007782259750898146, "std_abs_shap": 0.028390618339096712, "max_abs_shap": 0.328976487417731, "feature_index": 5}, {"rank": 10, "feature_name": "feat_line_count", "mean_abs_shap": 0.007530765653289917, "std_abs_shap": 0.03122957922231738, "max_abs_shap": 0.40404425322071935, "feature_index": 45}, {"rank": 11, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.0071713647737379396, "std_abs_shap": 0.01796194251764333, "max_abs_shap": 0.18909322823218208, "feature_index": 19}, {"rank": 12, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.007087148903107964, "std_abs_shap": 0.0152398628745811, "max_abs_shap": 0.11813913470345204, "feature_index": 63}, {"rank": 13, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.006595510287063434, "std_abs_shap": 0.0166519690914296, "max_abs_shap": 0.15478781193919897, "feature_index": 64}, {"rank": 14, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.006594265250796543, "std_abs_shap": 0.028390005834771623, "max_abs_shap": 0.3313429212167943, "feature_index": 34}, {"rank": 15, "feature_name": "feat_colon_count", "mean_abs_shap": 0.005888707850461852, "std_abs_shap": 0.024303985399642743, "max_abs_shap": 0.2731618163722027, "feature_index": 15}, {"rank": 16, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.005721599238879016, "std_abs_shap": 0.021560851492950513, "max_abs_shap": 0.3141731285735099, "feature_index": 51}, {"rank": 17, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.005402205787360541, "std_abs_shap": 0.011740914610449524, "max_abs_shap": 0.1083706308920996, "feature_index": 0}, {"rank": 18, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.005210418155981293, "std_abs_shap": 0.014504634000189661, "max_abs_shap": 0.12178255654089008, "feature_index": 12}, {"rank": 19, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.004112681924768488, "std_abs_shap": 0.013269118142657906, "max_abs_shap": 0.15928350385770773, "feature_index": 3}, {"rank": 20, "feature_name": "feat_char_count", "mean_abs_shap": 0.0034439502224112547, "std_abs_shap": 0.016488582239571024, "max_abs_shap": 0.203995571490309, "feature_index": 13}], "total_importance": 0.19790980961241503, "top_10_importance_ratio": 0.5774201016391031}, "feature_statistics": {"mean_importance": 0.002787462107217113, "median_importance": 0.0002823224393276941, "std_importance": 0.0044286300890699895, "max_importance": 0.022564051171660177, "min_importance": 0.0, "importance_concentration": 0.37721110542556613}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.013770521878692644, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.009101906449408611, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.007087148903107964, "rank": 63}, {"name": "feat_tree_depth", "importance": 0.006595510287063434, "rank": 64}, {"name": "feat_max_nesting_depth", "importance": 0.0029706748985153532, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.007811478583100929, "rank": 31}, {"name": "feat_for_loops", "importance": 0.0015394234251931887, "rank": 24}, {"name": "feat_string_formatting", "importance": 0.0012183634401476233, "rank": 61}, {"name": "feat_if_else_chains", "importance": 0.0008147793163662915, "rank": 30}, {"name": "feat_keyword_if_count", "importance": 0.00017110029456265997, "rank": 37}, {"name": "feat_nested_loops", "importance": 9.712293652509674e-05, "rank": 54}, {"name": "feat_keyword_for_count", "importance": 9.079744605922914e-05, "rank": 36}, {"name": "feat_keyword_while_count", "importance": 8.770518154253093e-05, "rank": 41}, {"name": "feat_while_loops", "importance": 3.9279834519877485e-05, "rank": 68}, {"name": "feat_nested_ifs", "importance": 2.6653302487392438e-05, "rank": 53}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.008171616933404659, "rank": 48}, {"name": "feat_avg_branching_factor", "importance": 0.007782259750898146, "rank": 5}, {"name": "feat_cyclomatic_complexity", "importance": 0.0071713647737379396, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.002093913116933966, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.0007037512824376347, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.022564051171660177, "rank": 49}, {"name": "feat_assignments", "importance": 0.010804002252251794, "rank": 2}, {"name": "feat_function_calls", "importance": 0.008327203402620163, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.005210418155981293, "rank": 12}, {"name": "feat_attribute_access", "importance": 0.004112681924768488, "rank": 3}, {"name": "feat_aug_assignments", "importance": 0.00011793482682335945, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.018413296306449178, "rank": 58}, {"name": "feat_line_count", "importance": 0.007530765653289917, "rank": 45}, {"name": "feat_keyword_def_count", "importance": 0.006594265250796543, "rank": 34}, {"name": "feat_colon_count", "importance": 0.005888707850461852, "rank": 15}, {"name": "feat_max_function_params", "importance": 0.005721599238879016, "rank": 51}, {"name": "feat_arithmetic_ops", "importance": 0.005402205787360541, "rank": 0}, {"name": "feat_char_count", "importance": 0.0034439502224112547, "rank": 13}, {"name": "feat_comparison_ops", "importance": 0.003232320272314669, "rank": 16}, {"name": "feat_paren_count", "importance": 0.0028866305232223117, "rank": 56}, {"name": "feat_subscript_access", "importance": 0.002638141254811096, "rank": 62}, {"name": "feat_import_statements", "importance": 0.0017675006271184713, "rank": 32}, {"name": "feat_keyword_return_count", "importance": 0.0010630224182665415, "rank": 39}, {"name": "feat_function_defs", "importance": 0.001036624057262524, "rank": 26}, {"name": "feat_equals_count", "importance": 0.0009958133281958324, "rank": 23}, {"name": "feat_word_count", "importance": 0.0006383905234484649, "rank": 70}, {"name": "feat_has_syntax_error", "importance": 0.00032286018475138434, "rank": 29}, {"name": "feat_lambda_functions", "importance": 0.0002823224393276941, "rank": 42}, {"name": "feat_list_comprehensions", "importance": 0.00027783421732720926, "rank": 46}, {"name": "feat_generator_expressions", "importance": 0.00023268858239938208, "rank": 27}, {"name": "feat_boolean_ops", "importance": 0.00017490593609124344, "rank": 8}, {"name": "feat_brace_count", "importance": 0.00014531143711161095, "rank": 9}, {"name": "feat_class_defs", "importance": 0.00013609155808134174, "rank": 14}, {"name": "feat_lambda_usage", "importance": 0.00012777624189384888, "rank": 43}, {"name": "feat_keyword_class_count", "importance": 0.00012138942828044633, "rank": 33}, {"name": "feat_bracket_count", "importance": 0.00011783314149276506, "rank": 10}, {"name": "feat_decorator_usage", "importance": 0.00010970909098230977, "rank": 20}, {"name": "feat_generator_exps", "importance": 8.852093259274092e-05, "rank": 28}, {"name": "feat_list_comps", "importance": 3.2974310018057144e-05, "rank": 47}, {"name": "feat_raise_statements", "importance": 4.695028966350701e-06, "rank": 57}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_keyword_import_count", "importance": 0.0, "rank": 38}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.039525762416788006, "control_flow_features": 0.011896703760504819, "complexity_features": 0.025922905857412346, "syntactic_features": 0.05113629173410528, "other_features": 0.0694281458436046}}}, "visualisations_generated": ["shap_summary_xgboost_20251001_204942.png", "shap_bar_xgboost_20251001_204942.png", "shap_waterfall_xgboost_sample1_20251001_204942.png", "shap_waterfall_xgboost_sample2_20251001_204942.png", "shap_waterfall_xgboost_sample3_20251001_204942.png", "shap_dependence_xgboost_feat1_20251001_204942.png", "shap_dependence_xgboost_feat2_20251001_204942.png"]}