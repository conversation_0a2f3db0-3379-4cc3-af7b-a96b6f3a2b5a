{"metadata": {"model_name": "XGBoost", "run_id": "20251001_190703", "timestamp": "2025-10-01T19:15:27.510936+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.038677400108721546, "std_abs_shap": 0.07277506200640181, "max_abs_shap": 0.4951479845559828, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.027380510941115547, "std_abs_shap": 0.08113074890562672, "max_abs_shap": 0.7064141333691504, "feature_index": 58}, {"rank": 3, "feature_name": "feat_assignments", "mean_abs_shap": 0.01623677915532476, "std_abs_shap": 0.030214031705507335, "max_abs_shap": 0.1519566676559207, "feature_index": 2}, {"rank": 4, "feature_name": "feat_line_count", "mean_abs_shap": 0.014879309646515276, "std_abs_shap": 0.03584505933847632, "max_abs_shap": 0.2783353322143923, "feature_index": 45}, {"rank": 5, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.013622191798678249, "std_abs_shap": 0.037025944112456834, "max_abs_shap": 0.31223753447351016, "feature_index": 34}, {"rank": 6, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.012094927866709727, "std_abs_shap": 0.02059824560931372, "max_abs_shap": 0.13489900169666363, "feature_index": 44}, {"rank": 7, "feature_name": "feat_function_calls", "mean_abs_shap": 0.009461560381213098, "std_abs_shap": 0.020358369717592317, "max_abs_shap": 0.2039595283587218, "feature_index": 25}, {"rank": 8, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.009318629162302096, "std_abs_shap": 0.01639351020495576, "max_abs_shap": 0.15767758636482207, "feature_index": 19}, {"rank": 9, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.009136836659256764, "std_abs_shap": 0.025805032128791063, "max_abs_shap": 0.3359879688431743, "feature_index": 16}, {"rank": 10, "feature_name": "feat_if_statements", "mean_abs_shap": 0.008923605591632267, "std_abs_shap": 0.02567713890494707, "max_abs_shap": 0.2640405770834141, "feature_index": 31}, {"rank": 11, "feature_name": "feat_colon_count", "mean_abs_shap": 0.00850833065077155, "std_abs_shap": 0.036549319234668684, "max_abs_shap": 0.40529331292429743, "feature_index": 15}, {"rank": 12, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.008431939127537216, "std_abs_shap": 0.016823559192963983, "max_abs_shap": 0.16501740369788603, "feature_index": 64}, {"rank": 13, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.007957430048077236, "std_abs_shap": 0.02618495148250558, "max_abs_shap": 0.427691679167764, "feature_index": 62}, {"rank": 14, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.007732489269550485, "std_abs_shap": 0.020190630685667853, "max_abs_shap": 0.15981018139113023, "feature_index": 5}, {"rank": 15, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.007575386784141308, "std_abs_shap": 0.02496640564843089, "max_abs_shap": 0.401256862276357, "feature_index": 0}, {"rank": 16, "feature_name": "feat_char_count", "mean_abs_shap": 0.006633966444824952, "std_abs_shap": 0.02026469866653177, "max_abs_shap": 0.26364749137501936, "feature_index": 13}, {"rank": 17, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006624864138800575, "std_abs_shap": 0.01552843726002013, "max_abs_shap": 0.12630556237188306, "feature_index": 63}, {"rank": 18, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.006604530021942701, "std_abs_shap": 0.013990076665205286, "max_abs_shap": 0.10477506191123892, "feature_index": 55}, {"rank": 19, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.005854084302002812, "std_abs_shap": 0.013239380971157583, "max_abs_shap": 0.1305544739754488, "feature_index": 48}, {"rank": 20, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.005738262726371378, "std_abs_shap": 0.017437198109458627, "max_abs_shap": 0.20311626493332238, "feature_index": 12}], "total_importance": 0.2628625101660662, "top_10_importance_ratio": 0.6076627329266432}, "feature_statistics": {"mean_importance": 0.0037022888755783973, "median_importance": 0.00034946623684238164, "std_importance": 0.00652943796114545, "max_importance": 0.038677400108721546, "min_importance": 0.0, "importance_concentration": 0.42149864421654765}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.012094927866709727, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.008431939127537216, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.006624864138800575, "rank": 63}, {"name": "feat_nodes_with_children", "importance": 0.006604530021942701, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.001964260014776009, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.008923605591632267, "rank": 31}, {"name": "feat_keyword_for_count", "importance": 0.002048702439031333, "rank": 36}, {"name": "feat_for_loops", "importance": 0.0010561366325831994, "rank": 24}, {"name": "feat_if_else_chains", "importance": 0.0009318564045992805, "rank": 30}, {"name": "feat_keyword_if_count", "importance": 0.0007180854195714586, "rank": 37}, {"name": "feat_string_formatting", "importance": 0.0002611197734773644, "rank": 61}, {"name": "feat_nested_ifs", "importance": 7.787890275424641e-05, "rank": 53}, {"name": "feat_while_loops", "importance": 3.0211859228890602e-05, "rank": 68}, {"name": "feat_nested_loops", "importance": 1.6926895470662665e-05, "rank": 54}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_cyclomatic_complexity", "importance": 0.009318629162302096, "rank": 19}, {"name": "feat_avg_branching_factor", "importance": 0.007732489269550485, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.005854084302002812, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0028287076962218083, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00047867144662550954, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.038677400108721546, "rank": 49}, {"name": "feat_assignments", "importance": 0.01623677915532476, "rank": 2}, {"name": "feat_function_calls", "importance": 0.009461560381213098, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.005738262726371378, "rank": 12}, {"name": "feat_attribute_access", "importance": 0.004527477807034709, "rank": 3}, {"name": "feat_aug_assignments", "importance": 0.000308023052118143, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.027380510941115547, "rank": 58}, {"name": "feat_line_count", "importance": 0.014879309646515276, "rank": 45}, {"name": "feat_keyword_def_count", "importance": 0.013622191798678249, "rank": 34}, {"name": "feat_comparison_ops", "importance": 0.009136836659256764, "rank": 16}, {"name": "feat_colon_count", "importance": 0.00850833065077155, "rank": 15}, {"name": "feat_subscript_access", "importance": 0.007957430048077236, "rank": 62}, {"name": "feat_arithmetic_ops", "importance": 0.007575386784141308, "rank": 0}, {"name": "feat_char_count", "importance": 0.006633966444824952, "rank": 13}, {"name": "feat_word_count", "importance": 0.0036298941106305107, "rank": 70}, {"name": "feat_import_statements", "importance": 0.002996345898727704, "rank": 32}, {"name": "feat_max_function_params", "importance": 0.0019711129956602627, "rank": 51}, {"name": "feat_paren_count", "importance": 0.0018719199124498367, "rank": 56}, {"name": "feat_equals_count", "importance": 0.0018143519523220136, "rank": 23}, {"name": "feat_keyword_return_count", "importance": 0.0016064631207844805, "rank": 39}, {"name": "feat_lambda_functions", "importance": 0.00040206319514614407, "rank": 42}, {"name": "feat_keyword_import_count", "importance": 0.00034946623684238164, "rank": 38}, {"name": "feat_function_defs", "importance": 0.0003145298043781028, "rank": 26}, {"name": "feat_decorator_usage", "importance": 0.0002930094275188744, "rank": 20}, {"name": "feat_bracket_count", "importance": 0.00021706490566657095, "rank": 10}, {"name": "feat_keyword_class_count", "importance": 0.00021283562447947698, "rank": 33}, {"name": "feat_boolean_ops", "importance": 0.00013775928524992323, "rank": 8}, {"name": "feat_list_comprehensions", "importance": 0.00013620151712860282, "rank": 46}, {"name": "feat_lambda_usage", "importance": 9.938371620681652e-05, "rank": 43}, {"name": "feat_brace_count", "importance": 9.542776964588024e-05, "rank": 9}, {"name": "feat_generator_expressions", "importance": 2.7007266195814565e-05, "rank": 27}, {"name": "feat_list_comps", "importance": 2.6054357472552815e-05, "rank": 47}, {"name": "feat_has_syntax_error", "importance": 1.48204673124456e-05, "rank": 29}, {"name": "feat_class_defs", "importance": 3.128540660748402e-06, "rank": 14}, {"name": "feat_generator_exps", "importance": 2.5768926048380596e-06, "rank": 28}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.03572052116976623, "control_flow_features": 0.014064523918348702, "complexity_features": 0.02621258187670271, "syntactic_features": 0.07494950323078363, "other_features": 0.11191537997046486}}}, "visualisations_generated": ["shap_summary_xgboost_20251001_190703.png", "shap_bar_xgboost_20251001_190703.png", "shap_waterfall_xgboost_sample1_20251001_190703.png", "shap_waterfall_xgboost_sample2_20251001_190703.png", "shap_waterfall_xgboost_sample3_20251001_190703.png", "shap_dependence_xgboost_feat1_20251001_190703.png", "shap_dependence_xgboost_feat2_20251001_190703.png"]}