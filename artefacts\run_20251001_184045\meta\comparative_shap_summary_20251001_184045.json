{"metadata": {"analysis_type": "comparative_shap", "run_id": "20251001_184045", "timestamp": "2025-10-01T18:51:37.760257+00:00", "models_analysed": [{"name": "Grad<PERSON>", "cv_score": 0.6225042944439751}, {"name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "cv_score": 0.6218800747186318}, {"name": "XGBoost", "cv_score": 0.6181159828409977}], "files_validated": true, "reports_found": 3, "models_with_visualisations": 3}, "comparison_notes": {"purpose": "Compare feature importance patterns across top-performing models", "methodology": "SHAP (SHapley Additive exPlanations) analysis on test set samples", "interpretation": "Higher |SHAP| values indicate greater feature importance for predictions", "note": "Only models with successfully generated SHAP files are included"}, "model_reports": ["shap_analysis_report_gradient_boosting_20251001_184045.json", "shap_analysis_report_histgradient_boosting_20251001_184045.json", "shap_analysis_report_xgboost_20251001_184045.json"], "visualisations": {"Gradient Boosting": ["shap_bar_gradient_boosting_20251001_184045.png", "shap_waterfall_gradient_boosting_sample1_20251001_184045.png", "shap_waterfall_gradient_boosting_sample2_20251001_184045.png", "shap_waterfall_gradient_boosting_sample3_20251001_184045.png"], "HistGradient Boosting": ["shap_bar_histgradient_boosting_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample1_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample2_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample3_20251001_184045.png"], "XGBoost": ["shap_bar_xgboost_20251001_184045.png", "shap_waterfall_xgboost_sample1_20251001_184045.png", "shap_waterfall_xgboost_sample2_20251001_184045.png", "shap_waterfall_xgboost_sample3_20251001_184045.png"]}}