train_samples,train_score,test_score,scoring
200,1.0,0.4038787699920029,balanced_accuracy
400,0.9984848484848485,0.4364877548313615,balanced_accuracy
600,0.999043062200957,0.4458874017191578,balanced_accuracy
800,1.0,0.47078871111566784,balanced_accuracy
1000,0.9979294582337933,0.4920849948635777,balanced_accuracy
1200,0.9984455931587106,0.4893666050831722,balanced_accuracy
1400,0.9981357823463086,0.4771195600696695,balanced_accuracy
1600,0.9979516258168375,0.4963771123200649,balanced_accuracy
1800,0.9954983374704305,0.5068742750516572,balanced_accuracy
2000,0.9983797781183162,0.5074625261443697,balanced_accuracy
2200,0.9940123989382775,0.495998872377063,balanced_accuracy
2400,0.9940490712319957,0.5086534243991003,balanced_accuracy
2600,0.9948094431306387,0.4990711350067153,balanced_accuracy
2800,0.9945350688950962,0.527395340825091,balanced_accuracy
3000,0.9928751150037126,0.5045778593531849,balanced_accuracy
3200,0.9941927302319264,0.49971848793262574,balanced_accuracy
3400,0.9943935807431739,0.5172996333994103,balanced_accuracy
3600,0.994647891777367,0.5260499635642223,balanced_accuracy
3800,0.9921379490487513,0.5051267335905355,balanced_accuracy
4000,0.9923391821139216,0.5182500480434769,balanced_accuracy
