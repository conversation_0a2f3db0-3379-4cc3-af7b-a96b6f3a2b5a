model,fit_time_sec,cv_time_sec,inference_time_sec,inference_time_per_sample_ms,param_count
Decision Tree,0.006513900007121265,,0.0009676999179646373,0.024192497949115932,
<PERSON><PERSON><PERSON>,2.0182329000672325,,0.002144599915482104,0.053614997887052596,
<PERSON><PERSON><PERSON><PERSON><PERSON>,0.48932890000287443,,0.014339799992740154,0.35849499981850386,
K-Nearest Neighbors,0.002757800044491887,,0.08885770000051707,2.2214425000129268,
Logistic Regression,0.01147810008842498,,0.0007760999724268913,0.019402499310672283,936
<PERSON><PERSON>,0.0029788000974804163,,0.0007400000467896461,0.018500001169741154,
Neural Network,0.08891209994908422,,0.0009204999078065157,0.023012497695162892,
Random Forest,0.14733079995494336,,0.025221999967470765,0.6305499991867691,
SVM (Linear),0.015155000030063093,,0.0007097000489011407,0.017742501222528517,5616
SVM (RBF),0.028009100002236664,,0.0013962999219074845,0.03490749804768711,287
XGBoost,0.8576824000338092,,0.010553699918091297,0.26384249795228243,
