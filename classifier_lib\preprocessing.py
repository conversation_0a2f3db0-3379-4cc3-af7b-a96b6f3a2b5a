import os
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from typing import Optional


def cfg_seed(cfg: dict | None, fallback: int = 25) -> int:
    """Extract random seed from config with fallback."""
    try:
        return int((cfg or {}).get("general", {}).get("random_state", fallback))
    except (TypeError, ValueError, AttributeError):
        return int(fallback)


def cv_splitter_seed(cfg: dict | None, default_seed: int) -> Optional[int]:
    """Extract CV-specific random seed from config with fallback."""
    try:
        # Check for CV-specific random state first
        cv_seed = (
            (cfg or {})
            .get("evaluation", {})
            .get("cv", {})
            .get("cv_random_state", default_seed)
        )
        if cv_seed is not None:
            return int(cv_seed)
        # Fall back to global random state
        return int(default_seed)
    except (TypeError, ValueError, AttributeError):
        return int(default_seed)


def get_cfg():
    try:
        from classifier_lib.config import get_config as _get

        return _get()
    except Exception:  # pylint: disable=broad-except
        return {
            "general": {"random_state": 25},
            "class_balance": {"report_distributions": True},
        }


def maybe_seed_process(cfg: dict | None):
    """Seed Python and NumPy once per process if configured."""
    if getattr(maybe_seed_process, "_seeded", False):
        return
    try:
        do_seed = bool((cfg or {}).get("general", {}).get("set_process_seeds", True))
        if not do_seed:
            setattr(maybe_seed_process, "_seeded", True)
            return
        seed = cfg_seed(cfg, 25)
        # Set PYTHONHASHSEED for child processes;
        # current process cannot change hashing already initialised, but set env for downstream tools
        os.environ.setdefault("PYTHONHASHSEED", str(seed))
        import random as _random  # pylint: disable=import-outside-toplevel

        _random.seed(seed)
        try:
            np.random.seed(seed)
        except (TypeError, ValueError):
            pass
    finally:
        setattr(maybe_seed_process, "_seeded", True)


def maybe_report(label_series: pd.Series, title: str):
    """Report class distribution if configured."""
    try:
        cfg = get_cfg()
        if not bool(cfg.get("class_balance", {}).get("report_distributions", True)):
            return
    except Exception:  # pylint: disable=broad-except
        pass
    vc = label_series.value_counts(dropna=False)
    total = int(vc.sum()) if hasattr(vc, "sum") else len(label_series)
    print(f"\n[{title}] Class distribution (n={total}):")
    try:
        print(vc)
    except Exception:  # pylint: disable=broad-except
        # fallback simple counts
        from collections import Counter  # pylint: disable=import-outside-toplevel

        print(Counter(label_series))


def prune_rare_classes(df: pd.DataFrame, label_col: str) -> pd.DataFrame:
    """
    Removes classes with fewer than 2 samples to avoid stratification / CV errors.
    """
    counts = df[label_col].value_counts()
    rare = counts[counts < 2]
    if len(rare) > 0:
        to_drop_idx = df[df[label_col].isin(rare.index)].index
        print(
            f"\n[Pruning] Removing {len(to_drop_idx)} samples from "
            f"{len(rare)} class(es) with <2 samples (cannot stratify / CV): "
            + ", ".join(f"{k} (n={v})" for k, v in rare.items())
        )
        df.drop(index=to_drop_idx, inplace=True)
        df.reset_index(drop=True, inplace=True)
        maybe_report(df[label_col], "Post-singleton-removal")
        # Recompute counts
        counts = df[label_col].value_counts()
        if counts.min() < 2:
            print(
                "WARNING: After pruning, a class still has <2 samples; stratification will be disabled."
            )
    return df


def convert_features_to_dataframe(
    df: pd.DataFrame, features_series: pd.Series
) -> pd.DataFrame:
    """Convert a series of feature dictionaries to a dataframe with feature columns."""
    # Extract all feature dictionaries
    feature_dicts = features_series.tolist()

    # Get all unique feature names
    all_feature_names = set()
    for feature_dict in feature_dicts:
        if isinstance(feature_dict, dict):
            all_feature_names.update(feature_dict.keys())

    # Create feature columns
    feature_df = pd.DataFrame(index=df.index)
    for feature_name in sorted(all_feature_names):
        feature_df[f"feat_{feature_name}"] = [
            feature_dict.get(feature_name, 0) if isinstance(feature_dict, dict) else 0
            for feature_dict in feature_dicts
        ]

    # Combine with original dataframe
    result_df = pd.concat(
        [df.reset_index(drop=True), feature_df.reset_index(drop=True)], axis=1
    )
    return result_df


def prepare_features_and_split(
    df: pd.DataFrame,
    *,
    leakage_safe: bool = True,
    test_size: float = 0.2,
    random_state: int | None = None,
    include_no_error: bool = True,
):
    """Prepare features and split data into training and testing sets.
    NOTE: Automatically removes classes with fewer than 2 samples to avoid
    stratification / CV errors (sklearn requires >=2 per class).

    If leakage_safe True, first split the ORIGINAL grouped dataframe, then create
    datasets (optionally with NoError augmentation) separately for train and test to avoid
    the same (buggy_code, correct_code) pair contributing its correct_code to one
    set and buggy variant to the other (pair-level leakage).

    include_no_error controls the inclusion of NoError structural samples for an ablation run.

    Returns:
      - X_train, X_test, y_train, y_test (Series of feature_text/labels)
      - X_all, y_all, df_all (concatenated balanced dataset)
      - train_balanced_df, test_balanced_df (balanced DataFrames with original metadata for slice analysis)
    """
    from classifier_lib.data import (
        create_feature_text,
    )  # pylint: disable=import-outside-toplevel

    # Resolve seed from config if not provided
    if random_state is None:
        try:
            from classifier_lib.config import get_config as get_cfg

            _cfg = get_cfg()
            random_state = int(_cfg.get("general", {}).get("random_state", 25))
        except Exception:
            random_state = 25

    # Report initial class distribution (grouped label)
    label_col = (
        "grouped_error_type"
        if "grouped_error_type" in df.columns
        else "intended_error_type"
    )
    maybe_report(df[label_col], "Original (pre-split)")

    # prune singleton (or rare) classes (<2 samples) before any split to avoid stratification errors
    df = prune_rare_classes(df, label_col)

    if leakage_safe:
        print(
            "Performing leakage-safe split at original sample level before balancing..."
        )
        # Use grouped error type (or fallback) for stratification if possible
        stratify_col = None
        if "grouped_error_type" in df.columns:
            value_counts = df["grouped_error_type"].value_counts()
            if (value_counts >= 2).all():
                stratify_col = df["grouped_error_type"]
        elif "intended_error_type" in df.columns:
            value_counts = df["intended_error_type"].value_counts()
            if (value_counts >= 2).all():
                stratify_col = df["intended_error_type"]

        base_train, base_test = train_test_split(
            df,
            test_size=test_size,
            random_state=random_state,
            stratify=(
                df["grouped_error_type"]
                if "grouped_error_type" in df.columns
                and (df["grouped_error_type"].value_counts() >= 2).all()
                else (
                    df["intended_error_type"]
                    if "intended_error_type" in df.columns
                    and (df["intended_error_type"].value_counts() >= 2).all()
                    else None
                )
            ),
        )

        maybe_report(base_train[label_col], "Base Train (post-split, pre-balance)")
        maybe_report(base_test[label_col], "Base Test (post-split, pre-balance)")

        # Create datasets separately (optionally include NoError augmentation)
        print(
            "Creating balanced training subset..."
            if include_no_error
            else "Creating training subset without NoError augmentation..."
        )
        train_balanced = create_balanced_dataset(
            base_train, include_no_error=include_no_error
        )
        print(
            "Creating balanced test subset..."
            if include_no_error
            else "Creating test subset without NoError augmentation..."
        )
        test_balanced = create_balanced_dataset(
            base_test, include_no_error=include_no_error
        )

        # Report distributions after dataset construction (label col is 'label' in balanced frames)
        maybe_report(
            train_balanced["label"],
            "Train Balanced" if include_no_error else "Train (no NoError)",
        )
        maybe_report(
            test_balanced["label"],
            "Test Balanced" if include_no_error else "Test (no NoError)",
        )

        # Feature extraction per subset (now returns numerical features)
        print("Creating AST features for training subset...")
        train_features = train_balanced.apply(create_feature_text, axis=1)
        train_balanced = convert_features_to_dataframe(train_balanced, train_features)

        print("Creating AST features for test subset...")
        test_features = test_balanced.apply(create_feature_text, axis=1)
        test_balanced = convert_features_to_dataframe(test_balanced, test_features)

        # Show samples
        for subset_name, subset_df in [
            ("Train", train_balanced),
            ("Test", test_balanced),
        ]:
            print(f"Sample AST features examples ({subset_name}):")
            feature_cols = [col for col in subset_df.columns if col.startswith("feat_")]
            for i in range(min(2, len(subset_df))):
                sample_features = {
                    col: subset_df.iloc[i][col] for col in feature_cols[:5]
                }
                print(
                    f"  {subset_name} Sample {i+1} -> Label: {subset_df.iloc[i]['label']} | Features: {sample_features}..."
                )
            print("-" * 50)

        # Extract feature columns
        feature_cols = [
            col for col in train_balanced.columns if col.startswith("feat_")
        ]

        X_train = train_balanced[feature_cols]
        y_train = train_balanced["label"]
        X_test = test_balanced[feature_cols]
        y_test = test_balanced["label"]

        # Return ALSO the training full set (used for CV) but NOT test to avoid leakage in CV
        df_all = pd.concat([train_balanced, test_balanced], axis=0).reset_index(
            drop=True
        )
        return (
            X_train,
            X_test,
            y_train,
            y_test,
            df_all[feature_cols],
            df_all["label"],
            df_all,
            train_balanced.reset_index(drop=True),
            test_balanced.reset_index(drop=True),
        )

    # ================= Existing (non leakage-safe) path retained for comparison ================= #
    print(
        "[Warning] Using legacy balancing THEN splitting (potential leakage). Set leakage_safe=True to avoid."
    )
    msg = (
        "Creating balanced dataset with correct code samples..."
        if include_no_error
        else "Creating dataset without NoError augmentation..."
    )
    print(msg)
    df = create_balanced_dataset(df, include_no_error=include_no_error)

    print("Creating AST features from analysis results...")
    features_series = df.apply(create_feature_text, axis=1)
    df = convert_features_to_dataframe(df, features_series)

    print("Sample AST features examples:")
    feature_cols = [col for col in df.columns if col.startswith("feat_")]
    for i in range(min(3, len(df))):
        print(f"\nSample {i+1}:")
        print(f"Error Type: {df.iloc[i]['label']}")
        sample_features = {col: df.iloc[i][col] for col in feature_cols[:5]}
        print(f"Features: {sample_features}...")
    print("-" * 50)

    # Define features (X) and target (y)
    X = df[feature_cols]
    y = df["label"]

    # Check for any rows with all zero features (equivalent to empty features)
    zero_features = (X == 0).all(axis=1)
    if zero_features.any():
        print(
            f"Warning: {zero_features.sum()} samples have all zero features. Removing them."
        )
        df = df[~zero_features].reset_index(drop=True)
        X = df[feature_cols]
        y = df["label"]

    print(f"Final dataset size: {len(df)} samples")

    # Check minimum samples per class for stratification
    min_samples_per_class = y.value_counts().min()
    if min_samples_per_class < 2:
        print("WARNING: Some classes have only 1 sample - cannot stratify split")
        stratify = None
    else:
        stratify = y

    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=stratify
    )

    maybe_report(y_train, "Train (legacy path)")
    maybe_report(y_test, "Test (legacy path)")

    print(f"Training set: {len(X_train)} samples")
    print(f"Test set: {len(X_test)} samples")

    # Build train/test balanced DataFrames for downstream slice analysis
    try:
        train_balanced = df.loc[X_train.index].reset_index(drop=True)
        test_balanced = df.loc[X_test.index].reset_index(drop=True)
    except Exception:
        train_balanced = df.copy().reset_index(drop=True)
        test_balanced = df.copy().reset_index(drop=True)

    return X_train, X_test, y_train, y_test, X, y, df, train_balanced, test_balanced


def load_and_analyse_dataset(file_path: str, cfg: dict | None = None) -> pd.DataFrame:
    """Load the dataset and perform initial analysis."""
    print("Loading dataset...")
    df = pd.read_csv(file_path)

    print(f"Dataset loaded: {len(df)} samples")
    print("Error type distribution:")
    error_counts = df["intended_error_type"].value_counts()
    print(error_counts)
    if cfg.get("general", {}).get("sample_size_limit") is not None:
        limit = int(cfg["general"]["sample_size_limit"])
        if len(df) > limit:
            print(f"Limiting dataset to first {limit} samples...")
            df = df.iloc[:limit].reset_index(drop=True)
            print(f"Dataset size after limiting: {len(df)} samples")
    return df


def create_balanced_dataset(df, include_no_error: bool = True):
    """
    Creates a dataset including buggy code error samples and (optionally)
    NoError samples from correct code. Uses diff-based features to
    understand actual code differences.
    """
    # Create error samples from buggy code with correct code reference
    error_samples = []
    for _, row in df.iterrows():
        error_samples.append(
            {
                "code": row["buggy_code"],
                "correct_code": row[
                    "correct_code"
                ],  # Include for diff analysis (legacy)
                "ast_message": row["ast_message"],
                "error_description": row["error_description"],
                "label": row.get("grouped_error_type", row["intended_error_type"]),
            }
        )

    # Create "NoError" samples from correct code
    correct_samples = []
    if include_no_error:
        for _, row in df.iterrows():
            correct_samples.append(
                {
                    "code": row["correct_code"],
                    "correct_code": "",  # No reference code
                    "ast_message": "",  # No AST errors
                    "error_description": "No errors found",
                    "label": "NoError",
                }
            )

    # Combine and create dataset
    all_samples = error_samples + correct_samples
    balanced_df = pd.DataFrame(all_samples)

    print(
        "Created balanced dataset:"
        if include_no_error
        else "Created dataset (errors only):"
    )
    print(f" Error samples: {len(error_samples)}")
    if include_no_error:
        print(f" NoError samples: {len(correct_samples)}")
    else:
        print(" NoError samples: 0 (disabled)")
    print(f" Total: {len(all_samples)}")

    return balanced_df


def group_error_types(df):
    """
    Groups similar error types into broader categories to improve classification performance.
    This reduces the number of classes and increases samples per class.
    """
    # Create a mapping from specific errors to broader categories
    error_grouping = {
        # Syntax Errors
        "SyntaxError_UnclosedParen": "SyntaxError",
        "SyntaxError_MissingColon": "SyntaxError",
        "IndentationError": "SyntaxError",
        # Runtime Errors - Variable/Name Issues
        "NameError": "VariableError",
        "UnboundLocalError": "VariableError",
        "AttributeError": "VariableError",
        # Runtime Errors - Data Access Issues
        "IndexError": "DataAccessError",
        "KeyError": "DataAccessError",
        "FileNotFoundError": "DataAccessError",
        # Runtime Errors - Type Issues
        "TypeError": "TypeError",
        "ValueError": "ValueError",
        "ZeroDivisionError": "ArithmeticError",
        "RecursionError": "ArithmeticError",
        # Logical Errors
        "LogicalError_OffByOne": "LogicalError",
        "LogicalError_WrongOperator": "LogicalError",
        "LogicalError_InfiniteLoop": "LogicalError",
        # Import Issues
        "ImportError": "ImportError",
    }

    # Apply the grouping
    df["grouped_error_type"] = df["intended_error_type"].map(error_grouping)

    # Handle any unmapped errors (keep original)
    df["grouped_error_type"] = df["grouped_error_type"].fillna(
        df["intended_error_type"]
    )

    print("\n=== ERROR TYPE GROUPING ===")
    print("Grouped error distribution:")
    grouped_counts = df["grouped_error_type"].value_counts()
    print(grouped_counts)
    print(
        f"""Reduced from {df['intended_error_type'].nunique()} 
            to {df['grouped_error_type'].nunique()} classes"""
    )

    return df
