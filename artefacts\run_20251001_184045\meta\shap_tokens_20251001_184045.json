{"model": "GradientBoostingClassifier", "top_n": 20, "classes": ["ArithmeticError", "DataAccessError", "LogicErrorComparison", "LogicErrorNegation", "RecursionErrorPotential", "SyntaxErrorMismatchedParen", "SyntaxErrorMissingColon", "TypeErrorArity", "TypeErrorBadAdd", "TypeErrorBadKwarg", "VariableError"], "global_top_tokens": [{"token": "f10", "mean_abs_shap": 0.013309704324896284}, {"token": "f6", "mean_abs_shap": 0.01033888867979049}, {"token": "f7", "mean_abs_shap": 0.009651236959836678}, {"token": "f5", "mean_abs_shap": 0.008407903705295624}, {"token": "f4", "mean_abs_shap": 0.0064236076960997035}, {"token": "f3", "mean_abs_shap": 0.004668144289432801}, {"token": "f2", "mean_abs_shap": 0.004073710344732454}, {"token": "f0", "mean_abs_shap": 0.0037254693713825366}, {"token": "f1", "mean_abs_shap": 0.0014716439594813036}, {"token": "f8", "mean_abs_shap": 0.0009081267234365133}, {"token": "f9", "mean_abs_shap": 0.0008080876425022464}], "per_class_top_tokens": {"ArithmeticError": [{"token": "f0", "mean_abs_shap": 0.03999315074611911}, {"token": "f7", "mean_abs_shap": 0.03096560532229653}, {"token": "f10", "mean_abs_shap": 0.023360988755134125}, {"token": "f6", "mean_abs_shap": 0.017487926061133106}, {"token": "f2", "mean_abs_shap": 0.017425275837865028}, {"token": "f3", "mean_abs_shap": 0.009128333362262894}, {"token": "f5", "mean_abs_shap": 0.007937061650660942}, {"token": "f8", "mean_abs_shap": 0.007469804097219543}, {"token": "f4", "mean_abs_shap": 0.004403292296383962}, {"token": "f1", "mean_abs_shap": 0.00393414747515311}, {"token": "f9", "mean_abs_shap": 0.0009757108528841859}], "DataAccessError": [{"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "LogicErrorComparison": [{"token": "f10", "mean_abs_shap": 0.1121326848440744}, {"token": "f6", "mean_abs_shap": 0.03329063513815061}, {"token": "f7", "mean_abs_shap": 0.025623410746399573}, {"token": "f5", "mean_abs_shap": 0.022033473251317586}, {"token": "f2", "mean_abs_shap": 0.01342667570980261}, {"token": "f4", "mean_abs_shap": 0.011676652157717259}, {"token": "f1", "mean_abs_shap": 0.010270496497234865}, {"token": "f3", "mean_abs_shap": 0.009435660286022158}, {"token": "f0", "mean_abs_shap": 0.004254700651622701}, {"token": "f9", "mean_abs_shap": 0.0021220431776510906}, {"token": "f8", "mean_abs_shap": 0.0009935123182946237}], "LogicErrorNegation": [{"token": "f10", "mean_abs_shap": 0.025471172877899715}, {"token": "f7", "mean_abs_shap": 0.014186671551555391}, {"token": "f6", "mean_abs_shap": 0.003946895731320744}, {"token": "f3", "mean_abs_shap": 0.0034010935922563425}, {"token": "f5", "mean_abs_shap": 0.0033766136720839307}, {"token": "f2", "mean_abs_shap": 0.002519319853725647}, {"token": "f4", "mean_abs_shap": 0.0022065614359735156}, {"token": "f1", "mean_abs_shap": 0.001175097356809664}, {"token": "f0", "mean_abs_shap": 0.0008321940391277278}, {"token": "f9", "mean_abs_shap": 0.0005801030686531859}, {"token": "f8", "mean_abs_shap": 0.00017427193591536922}], "RecursionErrorPotential": [{"token": "f2", "mean_abs_shap": 0.007793323588886698}, {"token": "f10", "mean_abs_shap": 0.005346198807784085}, {"token": "f7", "mean_abs_shap": 0.0044778041632084955}, {"token": "f3", "mean_abs_shap": 0.003924416090025634}, {"token": "f4", "mean_abs_shap": 0.0029664625032764505}, {"token": "f8", "mean_abs_shap": 0.002393087318268538}, {"token": "f1", "mean_abs_shap": 0.0022737198775606587}, {"token": "f6", "mean_abs_shap": 0.0007117089262662986}, {"token": "f5", "mean_abs_shap": 0.00051461769429906}, {"token": "f0", "mean_abs_shap": 0.0002581368591355174}, {"token": "f9", "mean_abs_shap": 0.00010131653630287017}], "SyntaxErrorMismatchedParen": [{"token": "f5", "mean_abs_shap": 0.006795314930502148}, {"token": "f10", "mean_abs_shap": 0.0031672336569247834}, {"token": "f0", "mean_abs_shap": 0.0028699139117114585}, {"token": "f6", "mean_abs_shap": 0.002726356966156131}, {"token": "f7", "mean_abs_shap": 0.002222700888114182}, {"token": "f9", "mean_abs_shap": 0.0005068633823627144}, {"token": "f3", "mean_abs_shap": 0.00042140050959449865}, {"token": "f2", "mean_abs_shap": 0.0002838998355122075}, {"token": "f1", "mean_abs_shap": 0.0001622123682671288}, {"token": "f4", "mean_abs_shap": 0.0001400402604793377}, {"token": "f8", "mean_abs_shap": 7.516905921765577e-05}], "SyntaxErrorMissingColon": [{"token": "f10", "mean_abs_shap": 0.0059055419979708946}, {"token": "f4", "mean_abs_shap": 0.0035884523204715393}, {"token": "f7", "mean_abs_shap": 0.0035472850118118873}, {"token": "f3", "mean_abs_shap": 0.0034877989110218563}, {"token": "f2", "mean_abs_shap": 0.0023131874571242386}, {"token": "f1", "mean_abs_shap": 0.0021031551314597964}, {"token": "f6", "mean_abs_shap": 0.0015422794564365434}, {"token": "f5", "mean_abs_shap": 0.0011054157693128257}, {"token": "f8", "mean_abs_shap": 0.0009819258460788047}, {"token": "f0", "mean_abs_shap": 0.0008072029271307732}, {"token": "f9", "mean_abs_shap": 0.00013520435054499645}], "TypeErrorArity": [{"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorBadAdd": [{"token": "f3", "mean_abs_shap": 0.00026477054751683753}, {"token": "f2", "mean_abs_shap": 0.00023729262697560815}, {"token": "f10", "mean_abs_shap": 8.034185803292425e-05}, {"token": "f7", "mean_abs_shap": 5.605902202580393e-05}, {"token": "f4", "mean_abs_shap": 4.400146635222466e-06}, {"token": "f8", "mean_abs_shap": 3.834318705927698e-06}, {"token": "f6", "mean_abs_shap": 1.6882255590886745e-06}, {"token": "f1", "mean_abs_shap": 1.1779483291424856e-06}, {"token": "f9", "mean_abs_shap": 2.3659059084574006e-07}, {"token": "f5", "mean_abs_shap": 2.0240027522340797e-10}, {"token": "f0", "mean_abs_shap": 1.2429229720337708e-10}], "TypeErrorBadKwarg": [{"token": "f6", "mean_abs_shap": 0.0067308459502991}, {"token": "f5", "mean_abs_shap": 0.005773239203737976}, {"token": "f10", "mean_abs_shap": 0.0011808224967003378}, {"token": "f7", "mean_abs_shap": 0.0004052982551960784}, {"token": "f2", "mean_abs_shap": 0.00018076481940828857}, {"token": "f4", "mean_abs_shap": 0.00015819911463192253}, {"token": "f0", "mean_abs_shap": 0.00015593862900508063}, {"token": "f3", "mean_abs_shap": 8.0750370267999e-05}, {"token": "f9", "mean_abs_shap": 3.777854107692996e-05}, {"token": "f1", "mean_abs_shap": 2.7992836238255836e-05}, {"token": "f8", "mean_abs_shap": 2.10760858542081e-05}], "VariableError": [{"token": "f6", "mean_abs_shap": 0.006404103895123963}, {"token": "f5", "mean_abs_shap": 0.005420535404547331}, {"token": "f7", "mean_abs_shap": 0.0011999592317211629}, {"token": "f10", "mean_abs_shap": 0.0008545712409682108}, {"token": "f0", "mean_abs_shap": 0.0004723228816536449}, {"token": "f2", "mean_abs_shap": 0.00024199778011211296}, {"token": "f4", "mean_abs_shap": 0.00016257341437460356}, {"token": "f3", "mean_abs_shap": 0.0001584283060063405}, {"token": "f8", "mean_abs_shap": 6.857446415881568e-05}, {"token": "f1", "mean_abs_shap": 4.454043338062952e-05}, {"token": "f9", "mean_abs_shap": 1.3641395210426692e-05}]}, "n_samples_used": 150}