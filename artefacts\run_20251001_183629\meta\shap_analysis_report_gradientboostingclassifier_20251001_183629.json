{"metadata": {"model_name": "GradientBoostingClassifier", "run_id": "20251001_183629", "timestamp": "2025-10-01T18:37:43.326249+00:00", "n_samples": 40, "n_features": 71, "shap_values_shape": [40, 71, 13], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_49", "mean_abs_shap": 0.0377345337706169, "std_abs_shap": 0.0778908771483099, "max_abs_shap": 0.5559641616358828, "feature_index": 49}, {"rank": 2, "feature_name": "feat_58", "mean_abs_shap": 0.03495567555819007, "std_abs_shap": 0.08180571030623861, "max_abs_shap": 0.7498279116242509, "feature_index": 58}, {"rank": 3, "feature_name": "feat_2", "mean_abs_shap": 0.026682224556107054, "std_abs_shap": 0.061015392287701815, "max_abs_shap": 0.4216196189664774, "feature_index": 2}, {"rank": 4, "feature_name": "feat_63", "mean_abs_shap": 0.02562745642278262, "std_abs_shap": 0.05135006640861546, "max_abs_shap": 0.3052532000622994, "feature_index": 63}, {"rank": 5, "feature_name": "feat_64", "mean_abs_shap": 0.022087667722923573, "std_abs_shap": 0.04633521903259314, "max_abs_shap": 0.32712035467932626, "feature_index": 64}, {"rank": 6, "feature_name": "feat_44", "mean_abs_shap": 0.021482166364307897, "std_abs_shap": 0.04458328418293037, "max_abs_shap": 0.3252207321144109, "feature_index": 44}, {"rank": 7, "feature_name": "feat_45", "mean_abs_shap": 0.018372190303269316, "std_abs_shap": 0.04778031440703518, "max_abs_shap": 0.4111449824926197, "feature_index": 45}, {"rank": 8, "feature_name": "feat_70", "mean_abs_shap": 0.017743088863134436, "std_abs_shap": 0.050497691472438336, "max_abs_shap": 0.6813161982121041, "feature_index": 70}, {"rank": 9, "feature_name": "feat_3", "mean_abs_shap": 0.01688271644801868, "std_abs_shap": 0.04126937026856469, "max_abs_shap": 0.45414625319167606, "feature_index": 3}, {"rank": 10, "feature_name": "feat_31", "mean_abs_shap": 0.01637504775594251, "std_abs_shap": 0.04406242392117619, "max_abs_shap": 0.4137223588659572, "feature_index": 31}, {"rank": 11, "feature_name": "feat_13", "mean_abs_shap": 0.01433727783933633, "std_abs_shap": 0.03912982241090399, "max_abs_shap": 0.3624588448112667, "feature_index": 13}, {"rank": 12, "feature_name": "feat_62", "mean_abs_shap": 0.01253098149737703, "std_abs_shap": 0.031013582820752433, "max_abs_shap": 0.21806733404514214, "feature_index": 62}, {"rank": 13, "feature_name": "feat_19", "mean_abs_shap": 0.01226976030598402, "std_abs_shap": 0.02926110503959446, "max_abs_shap": 0.3491726705476265, "feature_index": 19}, {"rank": 14, "feature_name": "feat_56", "mean_abs_shap": 0.0107884763092424, "std_abs_shap": 0.04020818770869836, "max_abs_shap": 0.4346038846117989, "feature_index": 56}, {"rank": 15, "feature_name": "feat_55", "mean_abs_shap": 0.010392694913989206, "std_abs_shap": 0.0226480417825946, "max_abs_shap": 0.20144891628617928, "feature_index": 55}, {"rank": 16, "feature_name": "feat_51", "mean_abs_shap": 0.008936368218564321, "std_abs_shap": 0.037451383976347646, "max_abs_shap": 0.42196919582189535, "feature_index": 51}, {"rank": 17, "feature_name": "feat_25", "mean_abs_shap": 0.008484801356197199, "std_abs_shap": 0.02600397722569945, "max_abs_shap": 0.24623497056068336, "feature_index": 25}, {"rank": 18, "feature_name": "feat_48", "mean_abs_shap": 0.008094522739516738, "std_abs_shap": 0.024284049927920208, "max_abs_shap": 0.26716066430239366, "feature_index": 48}, {"rank": 19, "feature_name": "feat_52", "mean_abs_shap": 0.007972796621787476, "std_abs_shap": 0.026892103510764084, "max_abs_shap": 0.3388606402080321, "feature_index": 52}, {"rank": 20, "feature_name": "feat_0", "mean_abs_shap": 0.007689704768606873, "std_abs_shap": 0.021116697725885297, "max_abs_shap": 0.21394499925608906, "feature_index": 0}], "total_importance": 0.39989599322961067, "top_10_importance_ratio": 0.5950116325088359}, "feature_statistics": {"mean_importance": 0.005632337932811418, "median_importance": 0.0009604022366060485, "std_importance": 0.008617555146610528, "max_importance": 0.0377345337706169, "min_importance": 0.0, "importance_concentration": 0.3678145330807705}, "ast_feature_analysis": {"structural_features": [], "control_flow_features": [], "complexity_features": [], "syntactic_features": [], "other_features": [{"name": "feat_49", "importance": 0.0377345337706169, "rank": 49}, {"name": "feat_58", "importance": 0.03495567555819007, "rank": 58}, {"name": "feat_2", "importance": 0.026682224556107054, "rank": 2}, {"name": "feat_63", "importance": 0.02562745642278262, "rank": 63}, {"name": "feat_64", "importance": 0.022087667722923573, "rank": 64}, {"name": "feat_44", "importance": 0.021482166364307897, "rank": 44}, {"name": "feat_45", "importance": 0.018372190303269316, "rank": 45}, {"name": "feat_70", "importance": 0.017743088863134436, "rank": 70}, {"name": "feat_3", "importance": 0.01688271644801868, "rank": 3}, {"name": "feat_31", "importance": 0.01637504775594251, "rank": 31}, {"name": "feat_13", "importance": 0.01433727783933633, "rank": 13}, {"name": "feat_62", "importance": 0.01253098149737703, "rank": 62}, {"name": "feat_19", "importance": 0.01226976030598402, "rank": 19}, {"name": "feat_56", "importance": 0.0107884763092424, "rank": 56}, {"name": "feat_55", "importance": 0.010392694913989206, "rank": 55}, {"name": "feat_51", "importance": 0.008936368218564321, "rank": 51}, {"name": "feat_25", "importance": 0.008484801356197199, "rank": 25}, {"name": "feat_48", "importance": 0.008094522739516738, "rank": 48}, {"name": "feat_52", "importance": 0.007972796621787476, "rank": 52}, {"name": "feat_0", "importance": 0.007689704768606873, "rank": 0}, {"name": "feat_12", "importance": 0.006891083602742598, "rank": 12}, {"name": "feat_50", "importance": 0.006858715233985065, "rank": 50}, {"name": "feat_34", "importance": 0.006607744966103817, "rank": 34}, {"name": "feat_15", "importance": 0.006167816760078411, "rank": 15}, {"name": "feat_47", "importance": 0.005308072813501766, "rank": 47}, {"name": "feat_9", "importance": 0.004647544266794521, "rank": 9}, {"name": "feat_46", "importance": 0.003777369746627752, "rank": 46}, {"name": "feat_6", "importance": 0.003518160885153728, "rank": 6}, {"name": "feat_30", "importance": 0.002593352418998328, "rank": 30}, {"name": "feat_24", "importance": 0.00221978813715549, "rank": 24}, {"name": "feat_14", "importance": 0.0017092574722133572, "rank": 14}, {"name": "feat_54", "importance": 0.001644788524979275, "rank": 54}, {"name": "feat_16", "importance": 0.0013537917361743754, "rank": 16}, {"name": "feat_68", "importance": 0.0013048803011152044, "rank": 68}, {"name": "feat_10", "importance": 0.0011075786650381761, "rank": 10}, {"name": "feat_39", "importance": 0.0009604022366060485, "rank": 39}, {"name": "feat_4", "importance": 0.0009421354712138329, "rank": 4}, {"name": "feat_23", "importance": 0.0008648577283971491, "rank": 23}, {"name": "feat_26", "importance": 0.0007129485687414185, "rank": 26}, {"name": "feat_37", "importance": 0.000305496099961779, "rank": 37}, {"name": "feat_28", "importance": 0.00021117080320120102, "rank": 28}, {"name": "feat_36", "importance": 0.00016920497440753413, "rank": 36}, {"name": "feat_27", "importance": 0.00016518309088740452, "rank": 27}, {"name": "feat_38", "importance": 0.00014115944354571022, "rank": 38}, {"name": "feat_53", "importance": 0.00011878964678448082, "rank": 53}, {"name": "feat_29", "importance": 5.9983519420366044e-05, "rank": 29}, {"name": "feat_32", "importance": 4.524697780486477e-05, "rank": 32}, {"name": "feat_5", "importance": 3.932198846722421e-05, "rank": 5}, {"name": "feat_33", "importance": 9.994813615143751e-06, "rank": 33}, {"name": "feat_1", "importance": 0.0, "rank": 1}, {"name": "feat_7", "importance": 0.0, "rank": 7}, {"name": "feat_8", "importance": 0.0, "rank": 8}, {"name": "feat_11", "importance": 0.0, "rank": 11}, {"name": "feat_17", "importance": 0.0, "rank": 17}, {"name": "feat_18", "importance": 0.0, "rank": 18}, {"name": "feat_20", "importance": 0.0, "rank": 20}, {"name": "feat_21", "importance": 0.0, "rank": 21}, {"name": "feat_22", "importance": 0.0, "rank": 22}, {"name": "feat_35", "importance": 0.0, "rank": 35}, {"name": "feat_40", "importance": 0.0, "rank": 40}, {"name": "feat_41", "importance": 0.0, "rank": 41}, {"name": "feat_42", "importance": 0.0, "rank": 42}, {"name": "feat_43", "importance": 0.0, "rank": 43}, {"name": "feat_57", "importance": 0.0, "rank": 57}, {"name": "feat_59", "importance": 0.0, "rank": 59}, {"name": "feat_60", "importance": 0.0, "rank": 60}, {"name": "feat_61", "importance": 0.0, "rank": 61}, {"name": "feat_65", "importance": 0.0, "rank": 65}, {"name": "feat_66", "importance": 0.0, "rank": 66}, {"name": "feat_67", "importance": 0.0, "rank": 67}, {"name": "feat_69", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0, "control_flow_features": 0, "complexity_features": 0, "syntactic_features": 0, "other_features": 0.39989599322961067}}}, "visualisations_generated": ["shap_summary_gradientboostingclassifier_20251001_183629.png", "shap_bar_gradientboostingclassifier_20251001_183629.png", "shap_waterfall_gradientboostingclassifier_sample1_20251001_183629.png", "shap_waterfall_gradientboostingclassifier_sample2_20251001_183629.png", "shap_waterfall_gradientboostingclassifier_sample3_20251001_183629.png", "shap_dependence_gradientboostingclassifier_feat1_20251001_183629.png", "shap_dependence_gradientboostingclassifier_feat2_20251001_183629.png"]}