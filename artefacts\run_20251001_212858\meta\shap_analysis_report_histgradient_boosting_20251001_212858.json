{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251001_212858", "timestamp": "2025-10-01T21:44:58.021838+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.03856118247315146, "std_abs_shap": 0.06604405183758837, "max_abs_shap": 0.49710209837901637, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.028304547190930042, "std_abs_shap": 0.0728771626217931, "max_abs_shap": 0.8065772622455145, "feature_index": 58}, {"rank": 3, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.02494466998109995, "std_abs_shap": 0.038058056614797234, "max_abs_shap": 0.25310297862390674, "feature_index": 44}, {"rank": 4, "feature_name": "feat_assignments", "mean_abs_shap": 0.02084536699370876, "std_abs_shap": 0.04101565499407554, "max_abs_shap": 0.2912790447826494, "feature_index": 2}, {"rank": 5, "feature_name": "feat_paren_count", "mean_abs_shap": 0.017166990539803934, "std_abs_shap": 0.04261256830713812, "max_abs_shap": 0.29291946282510983, "feature_index": 56}, {"rank": 6, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.016323207232785866, "std_abs_shap": 0.04039101296129931, "max_abs_shap": 0.3743159075830366, "feature_index": 16}, {"rank": 7, "feature_name": "feat_line_count", "mean_abs_shap": 0.01336322058866962, "std_abs_shap": 0.039556737129309676, "max_abs_shap": 0.33240330389349687, "feature_index": 45}, {"rank": 8, "feature_name": "feat_if_statements", "mean_abs_shap": 0.013317429015249682, "std_abs_shap": 0.03453995550046837, "max_abs_shap": 0.31224051905590683, "feature_index": 31}, {"rank": 9, "feature_name": "feat_colon_count", "mean_abs_shap": 0.01317844017025327, "std_abs_shap": 0.04388549743201042, "max_abs_shap": 0.44198166474318734, "feature_index": 15}, {"rank": 10, "feature_name": "feat_function_calls", "mean_abs_shap": 0.012021843779024802, "std_abs_shap": 0.02510516611968029, "max_abs_shap": 0.2192075828403502, "feature_index": 25}, {"rank": 11, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.011910304292221443, "std_abs_shap": 0.0377940643519707, "max_abs_shap": 0.42728749501930396, "feature_index": 34}, {"rank": 12, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.010795498955226171, "std_abs_shap": 0.022953576178998857, "max_abs_shap": 0.2722539070635559, "feature_index": 64}, {"rank": 13, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.009466512922119386, "std_abs_shap": 0.01793900682147899, "max_abs_shap": 0.12383078210796647, "feature_index": 55}, {"rank": 14, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.007904148830835959, "std_abs_shap": 0.020279825452355328, "max_abs_shap": 0.22158580125304325, "feature_index": 3}, {"rank": 15, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.0066304196959490235, "std_abs_shap": 0.019770593198546415, "max_abs_shap": 0.3189488724527306, "feature_index": 51}, {"rank": 16, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.00644728545013598, "std_abs_shap": 0.0181360993671276, "max_abs_shap": 0.16863544912171818, "feature_index": 62}, {"rank": 17, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.0051424931388561875, "std_abs_shap": 0.013256463245317358, "max_abs_shap": 0.16650471174713682, "feature_index": 63}, {"rank": 18, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.005088565106606229, "std_abs_shap": 0.013612774801771811, "max_abs_shap": 0.15026214386469303, "feature_index": 0}, {"rank": 19, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.004915013662186407, "std_abs_shap": 0.011885952818976971, "max_abs_shap": 0.16916089707031146, "feature_index": 52}, {"rank": 20, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.004787043952909236, "std_abs_shap": 0.01350665162138979, "max_abs_shap": 0.10867008805703306, "feature_index": 12}], "total_importance": 0.2988428562555678, "top_10_importance_ratio": 0.6626455805097998}, "feature_statistics": {"mean_importance": 0.004209054313458701, "median_importance": 0.00047094132230518303, "std_importance": 0.007477613786779989, "max_importance": 0.03856118247315146, "min_importance": 0.0, "importance_concentration": 0.4344181380319524}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.02494466998109995, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.010795498955226171, "rank": 64}, {"name": "feat_nodes_with_children", "importance": 0.009466512922119386, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.0051424931388561875, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.004915013662186407, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.013317429015249682, "rank": 31}, {"name": "feat_for_loops", "importance": 0.0017961847514652415, "rank": 24}, {"name": "feat_keyword_if_count", "importance": 0.0014385357033717436, "rank": 37}, {"name": "feat_keyword_for_count", "importance": 0.0012147193738908334, "rank": 36}, {"name": "feat_if_else_chains", "importance": 0.00024027891106396707, "rank": 30}, {"name": "feat_while_loops", "importance": 0.00015365979061939173, "rank": 68}, {"name": "feat_keyword_while_count", "importance": 9.064538236742232e-05, "rank": 41}, {"name": "feat_nested_loops", "importance": 8.731957741493888e-05, "rank": 54}, {"name": "feat_nested_ifs", "importance": 3.45861044073861e-05, "rank": 53}, {"name": "feat_string_formatting", "importance": 2.4806211017491284e-05, "rank": 61}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.004171923995076234, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.0017406807940362561, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.001089398981081622, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00034928579179738375, "rank": 50}, {"name": "feat_avg_branching_factor", "importance": 0.0001874197667944378, "rank": 5}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.03856118247315146, "rank": 49}, {"name": "feat_assignments", "importance": 0.02084536699370876, "rank": 2}, {"name": "feat_function_calls", "importance": 0.012021843779024802, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.007904148830835959, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.004787043952909236, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0006812583531290704, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.028304547190930042, "rank": 58}, {"name": "feat_paren_count", "importance": 0.017166990539803934, "rank": 56}, {"name": "feat_comparison_ops", "importance": 0.016323207232785866, "rank": 16}, {"name": "feat_line_count", "importance": 0.01336322058866962, "rank": 45}, {"name": "feat_colon_count", "importance": 0.01317844017025327, "rank": 15}, {"name": "feat_keyword_def_count", "importance": 0.011910304292221443, "rank": 34}, {"name": "feat_max_function_params", "importance": 0.0066304196959490235, "rank": 51}, {"name": "feat_subscript_access", "importance": 0.00644728545013598, "rank": 62}, {"name": "feat_arithmetic_ops", "importance": 0.005088565106606229, "rank": 0}, {"name": "feat_import_statements", "importance": 0.002604135911919907, "rank": 32}, {"name": "feat_char_count", "importance": 0.00230776866389063, "rank": 13}, {"name": "feat_function_defs", "importance": 0.0020018299018644448, "rank": 26}, {"name": "feat_equals_count", "importance": 0.001453063264512706, "rank": 23}, {"name": "feat_word_count", "importance": 0.0013453360178007922, "rank": 70}, {"name": "feat_lambda_functions", "importance": 0.001274362151799738, "rank": 42}, {"name": "feat_bracket_count", "importance": 0.00077821181204219, "rank": 10}, {"name": "feat_class_defs", "importance": 0.0004893886577517183, "rank": 14}, {"name": "feat_keyword_import_count", "importance": 0.00047094132230518303, "rank": 38}, {"name": "feat_boolean_ops", "importance": 0.0002671325969931917, "rank": 8}, {"name": "feat_list_comprehensions", "importance": 0.0002656808332498684, "rank": 46}, {"name": "feat_keyword_return_count", "importance": 0.0002430261240274157, "rank": 39}, {"name": "feat_keyword_class_count", "importance": 0.00023548376651862048, "rank": 33}, {"name": "feat_context_managers", "importance": 0.00022349580797509063, "rank": 17}, {"name": "feat_generator_expressions", "importance": 0.00017539307298445562, "rank": 27}, {"name": "feat_brace_count", "importance": 0.00014537180113911473, "rank": 9}, {"name": "feat_lambda_usage", "importance": 4.326845479728839e-05, "rank": 43}, {"name": "feat_generator_exps", "importance": 3.762540268191701e-05, "rank": 28}, {"name": "feat_has_syntax_error", "importance": 3.2457455327111563e-05, "rank": 29}, {"name": "feat_list_comps", "importance": 2.3812746785271688e-05, "rank": 47}, {"name": "feat_with_statements", "importance": 1.0183029944273798e-05, "rank": 69}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.0552641886594881, "control_flow_features": 0.018398164820868097, "complexity_features": 0.007538709328785934, "syntactic_features": 0.0848008443827593, "other_features": 0.13284094906366634}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251001_212858.png", "shap_bar_histgradient_boosting_20251001_212858.png", "shap_waterfall_histgradient_boosting_sample1_20251001_212858.png", "shap_waterfall_histgradient_boosting_sample2_20251001_212858.png", "shap_waterfall_histgradient_boosting_sample3_20251001_212858.png", "shap_dependence_histgradient_boosting_feat1_20251001_212858.png", "shap_dependence_histgradient_boosting_feat2_20251001_212858.png"]}