{"run_id": "20251001_212858", "dataset_hash": "fac653cad117af439d7634caae219103d3ea4e9749ca26c55b8431f76f6876ae", "experiment": {"grouping": true, "include_no_error": false, "label_space": "grouped", "balance_mode": "errors-only"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.5453503093903952, "Random Forest": 0.6428352487566238, "SVM (RBF)": 0.5870475607940898, "SVM (Linear)": 0.5970666401789606, "Naive Bayes": 0.20692062571910494, "Neural Network": 0.6841661860376975, "Gradient Boosting": 0.6504021071715332, "K-Nearest Neighbors": 0.577236013108669, "Decision Tree": 0.5497343940705192, "XGBoost": 0.7158441129210389, "HistGradient Boosting": 0.7166792568250457}, "cv_means": {"Logistic Regression": 0.5453503093903952, "Random Forest": 0.6428352487566238, "SVM (RBF)": 0.5870475607940898, "SVM (Linear)": 0.5970666401789606, "Naive Bayes": 0.20692062571910494, "Neural Network": 0.6841661860376975, "Gradient Boosting": 0.6504021071715332, "K-Nearest Neighbors": 0.577236013108669, "Decision Tree": 0.5497343940705192, "XGBoost": 0.7158441129210389, "HistGradient Boosting": 0.7166792568250457}, "cv_stds": {"Logistic Regression": 0.0031317459033008547, "Random Forest": 0.006014487006088164, "SVM (RBF)": 0.004197145088502341, "SVM (Linear)": 0.007470028622191268, "Naive Bayes": 0.00729622854330234, "Neural Network": 0.007402023402361184, "Gradient Boosting": 0.006182852531158087, "K-Nearest Neighbors": 0.003187152033491637, "Decision Tree": 0.016631911559279015, "XGBoost": 0.00551454673193809, "HistGradient Boosting": 0.003784643206261115}, "times_sec": {"Logistic Regression": 4.207513800007291, "Random Forest": 0.31202399998437613, "SVM (RBF)": 41.45704189990647, "SVM (Linear)": 80.8248960999772, "Naive Bayes": 0.04727310000453144, "Neural Network": 9.199878699961118, "Gradient Boosting": 15.845452000037767, "K-Nearest Neighbors": 0.027828400023281574, "Decision Tree": 0.11187829996924847, "XGBoost": 1.2086716999765486, "HistGradient Boosting": 3.1321556998882443}, "best_model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5453503093903952, "cv_std_accuracy": 0.0031317459033008547, "cv_fold_scores": [0.5428929242329368, 0.5444583594239198, 0.5430629502035703, 0.5482154038822793, 0.5432060112711333, 0.5502662073285312], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 4.207513800007291, "cv_time_sec": 6.728409499977715, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6428352487566238, "cv_std_accuracy": 0.006014487006088164, "cv_fold_scores": [0.6418284283030683, 0.6340012523481527, 0.6526777325399311, 0.6446462116468378, 0.6418284283030683, 0.6420294393986846], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.31202399998437613, "cv_time_sec": 1.4945263999979943, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5870475607940898, "cv_std_accuracy": 0.004197145088502341, "cv_fold_scores": [0.5867251095804634, 0.5904821540388228, 0.589727528969621, 0.5864120225422668, 0.5792110206637445, 0.589727528969621], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 41.45704189990647, "cv_time_sec": 23.54479159996845, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5970666401789606, "cv_std_accuracy": 0.007470028622191268, "cv_fold_scores": [0.5992485911083281, 0.5926737633061991, 0.5887879736924523, 0.6105197244834064, 0.5948653725735754, 0.5963044159098027], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 80.8248960999772, "cv_time_sec": 41.054497099947184, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.20692062571910494, "cv_std_accuracy": 0.00729622854330234, "cv_fold_scores": [0.2031934877896055, 0.2031934877896055, 0.20513623551518947, 0.20381966186599876, 0.204445835942392, 0.2217350454118384], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.04727310000453144, "cv_time_sec": 0.961382000008598, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6841661860376975, "cv_std_accuracy": 0.007402023402361184, "cv_fold_scores": [0.684721352536005, 0.6844082654978084, 0.6836830566865018, 0.6775203506574827, 0.6975579211020664, 0.6771061697463201], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 9.199878699961118, "cv_time_sec": 10.168165300041437, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6504021071715332, "cv_std_accuracy": 0.006182852531158087, "cv_fold_scores": [0.6493425172197871, 0.638697557921102, 0.6551832132790479, 0.6534126487163432, 0.6549780839073263, 0.6507986219855935], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 15.845452000037767, "cv_time_sec": 11.716657100012526, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.15, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 50, "n_iter_no_change": null, "random_state": 25, "subsample": 0.8, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.577236013108669, "cv_std_accuracy": 0.003187152033491637, "cv_fold_scores": [0.5801502817783344, 0.5726361928616155, 0.5746946445349201, 0.5801502817783344, 0.5795241077019412, 0.5762605699968681], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.027828400023281574, "cv_time_sec": 1.6883301000343636, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5497343940705192, "cv_std_accuracy": 0.016631911559279015, "cv_fold_scores": [0.5551033187226049, 0.5259862241703194, 0.5671782023175697, 0.5679398872886663, 0.539448966812774, 0.5427497651111807], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.11187829996924847, "cv_time_sec": 0.9486726999748498, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.7158441129210389, "cv_std_accuracy": 0.00551454673193809, "cv_fold_scores": [0.7182216656230432, 0.7113337507827175, 0.7250234888819292, 0.7113337507827175, 0.7175954915466499, 0.7115565299091763], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 1.2086716999765486, "cv_time_sec": 3.410973699996248, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.8, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.2, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=6, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=150, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.7166792568250457, "cv_std_accuracy": 0.003784643206261115, "cv_fold_scores": [0.7182216656230432, 0.7135253600500939, 0.7218916379580332, 0.7119599248591109, 0.715090795241077, 0.7193861572189164], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 3.1321556998882443, "cv_time_sec": 4.892405900056474, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.2, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 31, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.03370539995376021, "per_sample_latency_sec": 1.4067362251152006e-05, "training_time_sec_by_model": {"Logistic Regression": 4.207513800007291, "Random Forest": 0.31202399998437613, "SVM (RBF)": 41.45704189990647, "SVM (Linear)": 80.8248960999772, "Naive Bayes": 0.04727310000453144, "Neural Network": 9.199878699961118, "Gradient Boosting": 15.845452000037767, "K-Nearest Neighbors": 0.027828400023281574, "Decision Tree": 0.11187829996924847, "XGBoost": 1.2086716999765486, "HistGradient Boosting": 3.1321556998882443}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 9581, "n_test": 2396}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.0, 0.16666666666666666, 0.3333333333333333, 0.42777777777777776, 0.5240641711229946, 0.5260416666666666, 0.6041666666666666, 0.8759372869802318], "calibration_mean_pred": [0.26182480106856176, 0.36388815676724634, 0.45749711914319807, 0.555489487848196, 0.6521122279368657, 0.7522447429856323, 0.8564757261558826, 0.982118632926044], "brier_score": 0.17815321592023606, "ece": 0.13790954042337553, "mce": 0.26182480106856176, "brier_reliability": 0.021886401305825524, "brier_resolution": 0.03517492848219643, "brier_uncertainty": 0.19517835095219915, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 1, 18, 63, 180, 187, 192, 288, 1467], "bin_acc": [null, null, 0.0, 0.16666666666666666, 0.3333333333333333, 0.42777777777777776, 0.5240641711229946, 0.5260416666666666, 0.6041666666666666, 0.8759372869802318], "bin_conf": [null, null, 0.26182480106856176, 0.36388815676724645, 0.45749711914319807, 0.5554894878481961, 0.6521122279368655, 0.7522447429856323, 0.856475726155882, 0.9821186329260414], "plot_frac_pos": [0.0, 0.16666666666666666, 0.3333333333333333, 0.42777777777777776, 0.5240641711229946, 0.5260416666666666, 0.6041666666666666, 0.8759372869802318], "plot_mean_pred": [0.26182480106856176, 0.36388815676724645, 0.45749711914319807, 0.5554894878481961, 0.6521122279368655, 0.7522447429856323, 0.856475726155882, 0.9821186329260414]}, "post": {"calibration_frac_pos": [0.23255813953488372, 0.3583815028901734, 0.5057915057915058, 0.6148648648648649, 0.7940379403794038, 0.8217522658610272, 0.9125475285171103, 0.9824902723735408], "calibration_mean_pred": [0.271416817964145, 0.3567371882602305, 0.4546204681154257, 0.5490958810282507, 0.6489603108237563, 0.7482792060965731, 0.8491119487388569, 0.9631771986931364], "brier_score": 0.15264404655634647, "ece": 0.06213431662206896, "mce": 0.14507762955564674, "brier_reliability": 0.005620847231682641, "brier_resolution": 0.04119376308210339, "brier_uncertainty": 0.18977441534443884, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 43, 173, 259, 444, 369, 331, 263, 514], "bin_acc": [null, null, 0.23255813953488372, 0.3583815028901734, 0.5057915057915058, 0.6148648648648649, 0.7940379403794038, 0.8217522658610272, 0.9125475285171103, 0.9824902723735408], "bin_conf": [null, null, 0.27141681796414496, 0.3567371882602306, 0.45462046811542595, 0.5490958810282506, 0.648960310823757, 0.7482792060965726, 0.8491119487388565, 0.963177198693135], "plot_frac_pos": [0.23255813953488372, 0.3583815028901734, 0.5057915057915058, 0.6148648648648649, 0.7940379403794038, 0.8217522658610272, 0.9125475285171103, 0.9824902723735408], "plot_mean_pred": [0.27141681796414496, 0.3567371882602306, 0.45462046811542595, 0.5490958810282506, 0.648960310823757, 0.7482792060965726, 0.8491119487388565, 0.963177198693135]}, "plots": {"pre": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting.png", "post": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting_(pre_vs_post).png"}}, "bootstrap_ci": {"accuracy_ci95": [0.7132721202003339, 0.752952838063439], "macro_f1_ci95": [0.5438199329014277, 0.5995709066810031], "balanced_accuracy_ci95": [0.5442938760397681, 0.6003861746330473]}, "artefact": {"path": "artefacts\\run_20251001_212858\\models\\histgradient_boosting_20251001_212858_2025-10-01T21-36-44.024704+00-00.joblib", "size_bytes": 4211360, "size_bytes_by_model": {"Logistic Regression": 223854, "Random Forest": 72878211, "SVM (RBF)": 14060382, "SVM (Linear)": 13935278, "Naive Bayes": 232002, "Neural Network": 534015, "Gradient Boosting": 1942495, "K-Nearest Neighbors": 15418321, "Decision Tree": 333339, "XGBoost": 4514819, "HistGradient Boosting": 4211360}}, "best_model_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.2, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 31, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "pr_curves_plot": "artefacts\\run_20251001_212858\\figures\\pr_curves_pr_curves_-_histgradient_boosting.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251001_212858\\tables\\per_class_metrics_20251001_212858.csv", "confusions_csv": "artefacts\\run_20251001_212858\\tables\\top_confusions_20251001_212858.csv", "misclassifications_csv": "artefacts\\run_20251001_212858\\tables\\misclassifications_20251001_212858.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251001_212858\\tables\\slices_20251001_212858.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-01T21:36:44.489859+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "argparse": null, "seaborn": "0.13.2"}}}