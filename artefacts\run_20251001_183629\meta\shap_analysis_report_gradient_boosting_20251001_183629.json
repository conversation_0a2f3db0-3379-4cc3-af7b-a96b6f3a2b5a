{"metadata": {"model_name": "Grad<PERSON>", "run_id": "20251001_183629", "timestamp": "2025-10-01T18:39:25.869241+00:00", "n_samples": 40, "n_features": 71, "shap_values_shape": [40, 71, 13], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_return_statements", "mean_abs_shap": 0.03338565906985035, "std_abs_shap": 0.08368507916712939, "max_abs_shap": 0.713047986726627, "feature_index": 58}, {"rank": 2, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.032014102326274795, "std_abs_shap": 0.06777031715593854, "max_abs_shap": 0.5179052624007013, "feature_index": 49}, {"rank": 3, "feature_name": "feat_assignments", "mean_abs_shap": 0.022186520284658586, "std_abs_shap": 0.047769128330292523, "max_abs_shap": 0.28163211459039994, "feature_index": 2}, {"rank": 4, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.019794117428080425, "std_abs_shap": 0.035207582448949885, "max_abs_shap": 0.22510393229796447, "feature_index": 63}, {"rank": 5, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.017061890285436535, "std_abs_shap": 0.03545791943424666, "max_abs_shap": 0.20140113776114488, "feature_index": 64}, {"rank": 6, "feature_name": "feat_line_count", "mean_abs_shap": 0.015081376824350831, "std_abs_shap": 0.04306505241472276, "max_abs_shap": 0.46779614734675257, "feature_index": 45}, {"rank": 7, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.01439886545319497, "std_abs_shap": 0.030257003530562064, "max_abs_shap": 0.23372865132513054, "feature_index": 44}, {"rank": 8, "feature_name": "feat_if_statements", "mean_abs_shap": 0.014001149932375516, "std_abs_shap": 0.04107540930255595, "max_abs_shap": 0.375953331995417, "feature_index": 31}, {"rank": 9, "feature_name": "feat_char_count", "mean_abs_shap": 0.012475346638822054, "std_abs_shap": 0.035753065799874605, "max_abs_shap": 0.2884623456583804, "feature_index": 13}, {"rank": 10, "feature_name": "feat_word_count", "mean_abs_shap": 0.012413065215238064, "std_abs_shap": 0.040358536711659176, "max_abs_shap": 0.5217482342587193, "feature_index": 70}, {"rank": 11, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.012209009619552555, "std_abs_shap": 0.033014662180493155, "max_abs_shap": 0.3097053554655045, "feature_index": 3}, {"rank": 12, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.009579253671618803, "std_abs_shap": 0.025035162310772106, "max_abs_shap": 0.24094827422272821, "feature_index": 19}, {"rank": 13, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.00876143289749387, "std_abs_shap": 0.024143280414914474, "max_abs_shap": 0.2165867973531049, "feature_index": 62}, {"rank": 14, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.006566641951385962, "std_abs_shap": 0.013364806984520161, "max_abs_shap": 0.09454686584932058, "feature_index": 55}, {"rank": 15, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.006203173242455166, "std_abs_shap": 0.024487042170388502, "max_abs_shap": 0.2851203101000012, "feature_index": 51}, {"rank": 16, "feature_name": "feat_paren_count", "mean_abs_shap": 0.005992876571095322, "std_abs_shap": 0.026826359834854367, "max_abs_shap": 0.35491174678700177, "feature_index": 56}, {"rank": 17, "feature_name": "feat_max_function_complexity", "mean_abs_shap": 0.004820747769907454, "std_abs_shap": 0.019563274252698916, "max_abs_shap": 0.22581103444085357, "feature_index": 50}, {"rank": 18, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.004764825801821188, "std_abs_shap": 0.0233297097830987, "max_abs_shap": 0.3417691318495768, "feature_index": 34}, {"rank": 19, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.004446581553733798, "std_abs_shap": 0.011968838472014949, "max_abs_shap": 0.11105149749207187, "feature_index": 48}, {"rank": 20, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.004137373400781635, "std_abs_shap": 0.014430653494039124, "max_abs_shap": 0.15453398961642248, "feature_index": 52}], "total_importance": 0.28923213689627625, "top_10_importance_ratio": 0.6666344049016514}, "feature_statistics": {"mean_importance": 0.004073692068961637, "median_importance": 0.00021507928195730453, "std_importance": 0.007191603415864593, "max_importance": 0.03338565906985035, "min_importance": 0.0, "importance_concentration": 0.43025056181404864}, "ast_feature_analysis": {"structural_features": [{"name": "feat_total_nodes", "importance": 0.019794117428080425, "rank": 63}, {"name": "feat_tree_depth", "importance": 0.017061890285436535, "rank": 64}, {"name": "feat_leaf_nodes", "importance": 0.01439886545319497, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.006566641951385962, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.004137373400781635, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.014001149932375516, "rank": 31}, {"name": "feat_if_else_chains", "importance": 0.0015179785990710939, "rank": 30}, {"name": "feat_while_loops", "importance": 0.0006622461565842892, "rank": 68}, {"name": "feat_for_loops", "importance": 0.000493183179174293, "rank": 24}, {"name": "feat_nested_loops", "importance": 0.00033533011613153446, "rank": 54}, {"name": "feat_keyword_if_count", "importance": 4.47692126490984e-05, "rank": 37}, {"name": "feat_nested_ifs", "importance": 4.468177240370198e-05, "rank": 53}, {"name": "feat_keyword_for_count", "importance": 2.758611843604953e-05, "rank": 36}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_string_formatting", "importance": 0.0, "rank": 61}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_cyclomatic_complexity", "importance": 0.009579253671618803, "rank": 19}, {"name": "feat_max_function_complexity", "importance": 0.004820747769907454, "rank": 50}, {"name": "feat_max_branching_factor", "importance": 0.004446581553733798, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0011725307327905949, "rank": 6}, {"name": "feat_avg_branching_factor", "importance": 1.458882181791492e-05, "rank": 5}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.032014102326274795, "rank": 49}, {"name": "feat_assignments", "importance": 0.022186520284658586, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.012209009619552555, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.0037295631986429844, "rank": 12}, {"name": "feat_function_calls", "importance": 0.003649886907181108, "rank": 25}, {"name": "feat_aug_assignments", "importance": 0.0002264461148388566, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.03338565906985035, "rank": 58}, {"name": "feat_line_count", "importance": 0.015081376824350831, "rank": 45}, {"name": "feat_char_count", "importance": 0.012475346638822054, "rank": 13}, {"name": "feat_word_count", "importance": 0.012413065215238064, "rank": 70}, {"name": "feat_subscript_access", "importance": 0.00876143289749387, "rank": 62}, {"name": "feat_max_function_params", "importance": 0.006203173242455166, "rank": 51}, {"name": "feat_paren_count", "importance": 0.005992876571095322, "rank": 56}, {"name": "feat_keyword_def_count", "importance": 0.004764825801821188, "rank": 34}, {"name": "feat_colon_count", "importance": 0.0034630702174331405, "rank": 15}, {"name": "feat_arithmetic_ops", "importance": 0.0034504806622629954, "rank": 0}, {"name": "feat_brace_count", "importance": 0.0029172958836000524, "rank": 9}, {"name": "feat_list_comps", "importance": 0.0027556949997988296, "rank": 47}, {"name": "feat_list_comprehensions", "importance": 0.001948714376950444, "rank": 46}, {"name": "feat_class_defs", "importance": 0.00117999462792231, "rank": 14}, {"name": "feat_comparison_ops", "importance": 0.00031429385615207935, "rank": 16}, {"name": "feat_keyword_return_count", "importance": 0.00021507928195730453, "rank": 39}, {"name": "feat_bracket_count", "importance": 0.0001445948591485709, "rank": 10}, {"name": "feat_generator_expressions", "importance": 0.0001367349914321126, "rank": 27}, {"name": "feat_generator_exps", "importance": 0.00011426284581488907, "rank": 28}, {"name": "feat_equals_count", "importance": 0.0001122756126627704, "rank": 23}, {"name": "feat_function_defs", "importance": 0.00010653210219874327, "rank": 26}, {"name": "feat_import_statements", "importance": 5.55010326537679e-05, "rank": 32}, {"name": "feat_keyword_import_count", "importance": 4.903173074209425e-05, "rank": 38}, {"name": "feat_keyword_class_count", "importance": 3.369811022330517e-05, "rank": 33}, {"name": "feat_has_syntax_error", "importance": 2.2080837473436213e-05, "rank": 29}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_boolean_ops", "importance": 0.0, "rank": 8}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_lambda_functions", "importance": 0.0, "rank": 42}, {"name": "feat_lambda_usage", "importance": 0.0, "rank": 43}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.061958888518879526, "control_flow_features": 0.017126925086825576, "complexity_features": 0.020033702549868564, "syntactic_features": 0.07401552845114888, "other_features": 0.1160970922895537}}}, "visualisations_generated": ["shap_summary_gradient_boosting_20251001_183629.png", "shap_bar_gradient_boosting_20251001_183629.png", "shap_waterfall_gradient_boosting_sample1_20251001_183629.png", "shap_waterfall_gradient_boosting_sample2_20251001_183629.png", "shap_waterfall_gradient_boosting_sample3_20251001_183629.png", "shap_dependence_gradient_boosting_feat1_20251001_183629.png", "shap_dependence_gradient_boosting_feat2_20251001_183629.png"]}