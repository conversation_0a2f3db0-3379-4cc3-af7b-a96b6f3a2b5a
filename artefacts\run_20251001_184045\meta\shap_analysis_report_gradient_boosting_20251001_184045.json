{"metadata": {"model_name": "Grad<PERSON>", "run_id": "20251001_184045", "timestamp": "2025-10-01T18:50:23.306462+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.036671643354895034, "std_abs_shap": 0.07664345860472628, "max_abs_shap": 0.6239902113861938, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.028548027442709494, "std_abs_shap": 0.07936763035641105, "max_abs_shap": 0.7730275145424504, "feature_index": 58}, {"rank": 3, "feature_name": "feat_colon_count", "mean_abs_shap": 0.0194708931722777, "std_abs_shap": 0.0598786172668125, "max_abs_shap": 0.50085628776895, "feature_index": 15}, {"rank": 4, "feature_name": "feat_assignments", "mean_abs_shap": 0.017424153900218408, "std_abs_shap": 0.03396470285791269, "max_abs_shap": 0.27635162977855116, "feature_index": 2}, {"rank": 5, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.017316817126870557, "std_abs_shap": 0.033436916686973295, "max_abs_shap": 0.18050355520858008, "feature_index": 64}, {"rank": 6, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.016186671144348064, "std_abs_shap": 0.041200737145061984, "max_abs_shap": 0.4529543652570736, "feature_index": 34}, {"rank": 7, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.015629931005563615, "std_abs_shap": 0.03179713618750919, "max_abs_shap": 0.3370934604230714, "feature_index": 44}, {"rank": 8, "feature_name": "feat_line_count", "mean_abs_shap": 0.01493841388622968, "std_abs_shap": 0.03222161767815973, "max_abs_shap": 0.2525889112946385, "feature_index": 45}, {"rank": 9, "feature_name": "feat_if_statements", "mean_abs_shap": 0.011755774196815416, "std_abs_shap": 0.03474868308375946, "max_abs_shap": 0.3969220973946464, "feature_index": 31}, {"rank": 10, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.01100707324261374, "std_abs_shap": 0.023790429243501287, "max_abs_shap": 0.2902661886730264, "feature_index": 0}, {"rank": 11, "feature_name": "feat_keyword_if_count", "mean_abs_shap": 0.009254855072145012, "std_abs_shap": 0.03144264791593025, "max_abs_shap": 0.42183386478977336, "feature_index": 37}, {"rank": 12, "feature_name": "feat_paren_count", "mean_abs_shap": 0.008290403606928308, "std_abs_shap": 0.022941957889920556, "max_abs_shap": 0.20250579830748228, "feature_index": 56}, {"rank": 13, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.007436122736629812, "std_abs_shap": 0.022676209462885424, "max_abs_shap": 0.28276069443652624, "feature_index": 16}, {"rank": 14, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.007114207117036723, "std_abs_shap": 0.013857887673345731, "max_abs_shap": 0.11093730977462178, "feature_index": 63}, {"rank": 15, "feature_name": "feat_char_count", "mean_abs_shap": 0.007044580140989152, "std_abs_shap": 0.02904530097592612, "max_abs_shap": 0.3498411591688533, "feature_index": 13}, {"rank": 16, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.006295823546758819, "std_abs_shap": 0.020528939880447915, "max_abs_shap": 0.35554670061150595, "feature_index": 19}, {"rank": 17, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.006100353732865875, "std_abs_shap": 0.015442635299279178, "max_abs_shap": 0.13129947667602487, "feature_index": 55}, {"rank": 18, "feature_name": "feat_function_calls", "mean_abs_shap": 0.005876873636128395, "std_abs_shap": 0.014603858977793859, "max_abs_shap": 0.1372613533767423, "feature_index": 25}, {"rank": 19, "feature_name": "feat_keyword_for_count", "mean_abs_shap": 0.003942509863054688, "std_abs_shap": 0.014899641926614984, "max_abs_shap": 0.17643573779510288, "feature_index": 36}, {"rank": 20, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.003866295927312528, "std_abs_shap": 0.016602127133492756, "max_abs_shap": 0.3054231936062798, "feature_index": 62}], "total_importance": 0.2873039372507672, "top_10_importance_ratio": 0.6576637977210219}, "feature_statistics": {"mean_importance": 0.0040465343274755945, "median_importance": 0.0006784659916353265, "std_importance": 0.007019553335654353, "max_importance": 0.036671643354895034, "min_importance": 0.0, "importance_concentration": 0.4156975227691638}, "ast_feature_analysis": {"structural_features": [{"name": "feat_tree_depth", "importance": 0.017316817126870557, "rank": 64}, {"name": "feat_leaf_nodes", "importance": 0.015629931005563615, "rank": 44}, {"name": "feat_total_nodes", "importance": 0.007114207117036723, "rank": 63}, {"name": "feat_nodes_with_children", "importance": 0.006100353732865875, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.00174954672109538, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.011755774196815416, "rank": 31}, {"name": "feat_keyword_if_count", "importance": 0.009254855072145012, "rank": 37}, {"name": "feat_keyword_for_count", "importance": 0.003942509863054688, "rank": 36}, {"name": "feat_for_loops", "importance": 0.0021034173736385843, "rank": 24}, {"name": "feat_string_formatting", "importance": 0.00029392235617766707, "rank": 61}, {"name": "feat_nested_loops", "importance": 9.165925066866383e-05, "rank": 54}, {"name": "feat_if_else_chains", "importance": 5.778201838592626e-05, "rank": 30}, {"name": "feat_nested_ifs", "importance": 2.0413793162589928e-05, "rank": 53}, {"name": "feat_while_loops", "importance": 9.16479720140447e-06, "rank": 68}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_cyclomatic_complexity", "importance": 0.006295823546758819, "rank": 19}, {"name": "feat_max_branching_factor", "importance": 0.0035736704278321258, "rank": 48}, {"name": "feat_max_function_complexity", "importance": 0.0007298550731407106, "rank": 50}, {"name": "feat_avg_branching_factor", "importance": 0.0006784659916353265, "rank": 5}, {"name": "feat_avg_function_complexity", "importance": 0.0005535373776468633, "rank": 6}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.036671643354895034, "rank": 49}, {"name": "feat_assignments", "importance": 0.017424153900218408, "rank": 2}, {"name": "feat_function_calls", "importance": 0.005876873636128395, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.0036055914810765133, "rank": 12}, {"name": "feat_attribute_access", "importance": 0.003491407629208553, "rank": 3}, {"name": "feat_aug_assignments", "importance": 0.0015293295961684905, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.028548027442709494, "rank": 58}, {"name": "feat_colon_count", "importance": 0.0194708931722777, "rank": 15}, {"name": "feat_keyword_def_count", "importance": 0.016186671144348064, "rank": 34}, {"name": "feat_line_count", "importance": 0.01493841388622968, "rank": 45}, {"name": "feat_arithmetic_ops", "importance": 0.01100707324261374, "rank": 0}, {"name": "feat_paren_count", "importance": 0.008290403606928308, "rank": 56}, {"name": "feat_comparison_ops", "importance": 0.007436122736629812, "rank": 16}, {"name": "feat_char_count", "importance": 0.007044580140989152, "rank": 13}, {"name": "feat_subscript_access", "importance": 0.003866295927312528, "rank": 62}, {"name": "feat_import_statements", "importance": 0.0032707387898353486, "rank": 32}, {"name": "feat_keyword_return_count", "importance": 0.003115592026944184, "rank": 39}, {"name": "feat_keyword_import_count", "importance": 0.0017313188501565325, "rank": 38}, {"name": "feat_max_function_params", "importance": 0.0013470206443536108, "rank": 51}, {"name": "feat_equals_count", "importance": 0.0010372854963735386, "rank": 23}, {"name": "feat_word_count", "importance": 0.001012336787221678, "rank": 70}, {"name": "feat_bracket_count", "importance": 0.0006991710084225585, "rank": 10}, {"name": "feat_function_defs", "importance": 0.0006940906460969162, "rank": 26}, {"name": "feat_boolean_ops", "importance": 0.0004929761354007824, "rank": 8}, {"name": "feat_break_statements", "importance": 0.0004770693284044093, "rank": 11}, {"name": "feat_lambda_usage", "importance": 0.0001943598812132507, "rank": 43}, {"name": "feat_lambda_functions", "importance": 0.00018075040354324648, "rank": 42}, {"name": "feat_has_syntax_error", "importance": 0.00016152941269020443, "rank": 29}, {"name": "feat_list_comprehensions", "importance": 6.54995631278207e-05, "rank": 46}, {"name": "feat_list_comps", "importance": 4.5745364010316284e-05, "rank": 47}, {"name": "feat_generator_exps", "importance": 4.35207772108712e-05, "rank": 28}, {"name": "feat_class_defs", "importance": 3.2472378032628154e-05, "rank": 14}, {"name": "feat_generator_expressions", "importance": 2.3251963823304284e-05, "rank": 27}, {"name": "feat_keyword_class_count", "importance": 1.948897033706665e-05, "rank": 33}, {"name": "feat_dict_comprehensions", "importance": 5.310841391516018e-07, "rank": 21}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.04791085570343215, "control_flow_features": 0.02752949872124995, "complexity_features": 0.011831352417013845, "syntactic_features": 0.06859899959769539, "other_features": 0.1314332308113759}}}, "visualisations_generated": ["shap_summary_gradient_boosting_20251001_184045.png", "shap_bar_gradient_boosting_20251001_184045.png", "shap_waterfall_gradient_boosting_sample1_20251001_184045.png", "shap_waterfall_gradient_boosting_sample2_20251001_184045.png", "shap_waterfall_gradient_boosting_sample3_20251001_184045.png", "shap_dependence_gradient_boosting_feat1_20251001_184045.png", "shap_dependence_gradient_boosting_feat2_20251001_184045.png"]}