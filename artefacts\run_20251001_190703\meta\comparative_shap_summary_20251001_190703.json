{"metadata": {"analysis_type": "comparative_shap", "run_id": "20251001_190703", "timestamp": "2025-10-01T19:22:15.416849+00:00", "models_analysed": [{"name": "XGBoost", "cv_score": 0.6941256491034302}, {"name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "cv_score": 0.6878753046582485}, {"name": "Grad<PERSON>", "cv_score": 0.6806248976037113}], "files_validated": true, "reports_found": 3, "models_with_visualisations": 3}, "comparison_notes": {"purpose": "Compare feature importance patterns across top-performing models", "methodology": "SHAP (SHapley Additive exPlanations) analysis on test set samples", "interpretation": "Higher |SHAP| values indicate greater feature importance for predictions", "note": "Only models with successfully generated SHAP files are included"}, "model_reports": ["shap_analysis_report_xgboost_20251001_190703.json", "shap_analysis_report_histgradient_boosting_20251001_190703.json", "shap_analysis_report_gradient_boosting_20251001_190703.json"], "visualisations": {"XGBoost": ["shap_bar_xgboost_20251001_190703.png", "shap_waterfall_xgboost_sample1_20251001_190703.png", "shap_waterfall_xgboost_sample2_20251001_190703.png", "shap_waterfall_xgboost_sample3_20251001_190703.png"], "HistGradient Boosting": ["shap_bar_histgradient_boosting_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample1_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample2_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample3_20251001_190703.png"], "Gradient Boosting": ["shap_bar_gradient_boosting_20251001_190703.png", "shap_waterfall_gradient_boosting_sample1_20251001_190703.png", "shap_waterfall_gradient_boosting_sample2_20251001_190703.png", "shap_waterfall_gradient_boosting_sample3_20251001_190703.png"]}}