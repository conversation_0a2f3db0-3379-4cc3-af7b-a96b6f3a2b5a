{"metadata": {"model_name": "Grad<PERSON>", "run_id": "20251001_190703", "timestamp": "2025-10-01T19:22:15.412850+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.03968168231940516, "std_abs_shap": 0.07269085469311856, "max_abs_shap": 0.5186738116341871, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.03387078581027129, "std_abs_shap": 0.09163735462733115, "max_abs_shap": 0.7704083851008945, "feature_index": 58}, {"rank": 3, "feature_name": "feat_assignments", "mean_abs_shap": 0.02400171794594728, "std_abs_shap": 0.04482016167953094, "max_abs_shap": 0.3048740531712129, "feature_index": 2}, {"rank": 4, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.01740928761904072, "std_abs_shap": 0.04702211298977438, "max_abs_shap": 0.4647351841032122, "feature_index": 34}, {"rank": 5, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.01567670911968565, "std_abs_shap": 0.032308602818107234, "max_abs_shap": 0.2532229497257845, "feature_index": 44}, {"rank": 6, "feature_name": "feat_line_count", "mean_abs_shap": 0.014298461962975573, "std_abs_shap": 0.03237168699949326, "max_abs_shap": 0.31213035576271486, "feature_index": 45}, {"rank": 7, "feature_name": "feat_if_statements", "mean_abs_shap": 0.013444456902836148, "std_abs_shap": 0.03699799857905805, "max_abs_shap": 0.3854559424813389, "feature_index": 31}, {"rank": 8, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.010564814179888853, "std_abs_shap": 0.022354254849032952, "max_abs_shap": 0.2302725610389426, "feature_index": 64}, {"rank": 9, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.01031451567504232, "std_abs_shap": 0.027149331890632313, "max_abs_shap": 0.2948153621339648, "feature_index": 16}, {"rank": 10, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.010070230193680462, "std_abs_shap": 0.028047195234069015, "max_abs_shap": 0.4396870963186572, "feature_index": 0}, {"rank": 11, "feature_name": "feat_colon_count", "mean_abs_shap": 0.009647044570913558, "std_abs_shap": 0.03761172291222961, "max_abs_shap": 0.4858774933069002, "feature_index": 15}, {"rank": 12, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.008935044335179805, "std_abs_shap": 0.021600379890610037, "max_abs_shap": 0.19648413666647233, "feature_index": 55}, {"rank": 13, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.008174658900343076, "std_abs_shap": 0.01947439827706052, "max_abs_shap": 0.25908568435574414, "feature_index": 3}, {"rank": 14, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.007390893079902931, "std_abs_shap": 0.026397413472327255, "max_abs_shap": 0.4551036869935834, "feature_index": 62}, {"rank": 15, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.007264146123917687, "std_abs_shap": 0.014969125018782158, "max_abs_shap": 0.125634373819246, "feature_index": 48}, {"rank": 16, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.006940539787442805, "std_abs_shap": 0.02022863021967974, "max_abs_shap": 0.28064035154989336, "feature_index": 12}, {"rank": 17, "feature_name": "feat_function_calls", "mean_abs_shap": 0.006201115274445615, "std_abs_shap": 0.01936445446677011, "max_abs_shap": 0.28857169899191615, "feature_index": 25}, {"rank": 18, "feature_name": "feat_import_statements", "mean_abs_shap": 0.0061668568192492675, "std_abs_shap": 0.022881689616055304, "max_abs_shap": 0.2838684594829424, "feature_index": 32}, {"rank": 19, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.005759341054922299, "std_abs_shap": 0.015047715819289157, "max_abs_shap": 0.12690532526859602, "feature_index": 63}, {"rank": 20, "feature_name": "feat_word_count", "mean_abs_shap": 0.005183305714810191, "std_abs_shap": 0.019184224002530875, "max_abs_shap": 0.18993903260060502, "feature_index": 70}], "total_importance": 0.29143342218592994, "top_10_importance_ratio": 0.6496600846555691}, "feature_statistics": {"mean_importance": 0.004104696087125774, "median_importance": 0.00036550558727210206, "std_importance": 0.007466942344121381, "max_importance": 0.03968168231940516, "min_importance": 0.0, "importance_concentration": 0.44826767580213817}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.01567670911968565, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.010564814179888853, "rank": 64}, {"name": "feat_nodes_with_children", "importance": 0.008935044335179805, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.005759341054922299, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.0033283216963366975, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.013444456902836148, "rank": 31}, {"name": "feat_keyword_for_count", "importance": 0.0026118501744472615, "rank": 36}, {"name": "feat_keyword_if_count", "importance": 0.0012129784297131962, "rank": 37}, {"name": "feat_if_else_chains", "importance": 0.0009814333207685381, "rank": 30}, {"name": "feat_for_loops", "importance": 0.0008597370218104234, "rank": 24}, {"name": "feat_string_formatting", "importance": 0.00031601939558911063, "rank": 61}, {"name": "feat_try_statements", "importance": 0.0003048415903057827, "rank": 67}, {"name": "feat_while_loops", "importance": 0.00022884802416436187, "rank": 68}, {"name": "feat_nested_ifs", "importance": 0.00019712779316964572, "rank": 53}, {"name": "feat_nested_loops", "importance": 0.00010555937227787084, "rank": 54}, {"name": "feat_try_except_blocks", "importance": 0.00010238828624042119, "rank": 65}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.007264146123917687, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.0033175453095809927, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.0007959003770852572, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.0004095802542534343, "rank": 50}, {"name": "feat_avg_branching_factor", "importance": 0.00030876442315256274, "rank": 5}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.03968168231940516, "rank": 49}, {"name": "feat_assignments", "importance": 0.02400171794594728, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.008174658900343076, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.006940539787442805, "rank": 12}, {"name": "feat_function_calls", "importance": 0.006201115274445615, "rank": 25}, {"name": "feat_aug_assignments", "importance": 0.0007062877495900247, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.03387078581027129, "rank": 58}, {"name": "feat_keyword_def_count", "importance": 0.01740928761904072, "rank": 34}, {"name": "feat_line_count", "importance": 0.014298461962975573, "rank": 45}, {"name": "feat_comparison_ops", "importance": 0.01031451567504232, "rank": 16}, {"name": "feat_arithmetic_ops", "importance": 0.010070230193680462, "rank": 0}, {"name": "feat_colon_count", "importance": 0.009647044570913558, "rank": 15}, {"name": "feat_subscript_access", "importance": 0.007390893079902931, "rank": 62}, {"name": "feat_import_statements", "importance": 0.0061668568192492675, "rank": 32}, {"name": "feat_word_count", "importance": 0.005183305714810191, "rank": 70}, {"name": "feat_paren_count", "importance": 0.004301603182261398, "rank": 56}, {"name": "feat_char_count", "importance": 0.0026483872825075424, "rank": 13}, {"name": "feat_max_function_params", "importance": 0.0024758657395741084, "rank": 51}, {"name": "feat_equals_count", "importance": 0.0023224732080497816, "rank": 23}, {"name": "feat_decorator_usage", "importance": 0.0004202433548005916, "rank": 20}, {"name": "feat_has_syntax_error", "importance": 0.00039348626119890074, "rank": 29}, {"name": "feat_keyword_return_count", "importance": 0.00036550558727210206, "rank": 39}, {"name": "feat_brace_count", "importance": 0.0002753833607150098, "rank": 9}, {"name": "feat_lambda_usage", "importance": 0.0002626750562517545, "rank": 43}, {"name": "feat_function_defs", "importance": 0.0002382917811359524, "rank": 26}, {"name": "feat_boolean_ops", "importance": 0.00023793269047732722, "rank": 8}, {"name": "feat_keyword_import_count", "importance": 0.00020311307954793594, "rank": 38}, {"name": "feat_lambda_functions", "importance": 0.00020145479057478462, "rank": 42}, {"name": "feat_bracket_count", "importance": 0.00011357884095415577, "rank": 10}, {"name": "feat_list_comps", "importance": 9.096850292354695e-05, "rank": 47}, {"name": "feat_list_comprehensions", "importance": 4.9936957762562094e-05, "rank": 46}, {"name": "feat_keyword_class_count", "importance": 3.517121182662304e-05, "rank": 33}, {"name": "feat_class_defs", "importance": 5.792831128286808e-06, "rank": 14}, {"name": "feat_generator_expressions", "importance": 5.208822127392159e-06, "rank": 27}, {"name": "feat_generator_exps", "importance": 3.559036453911965e-06, "rank": 28}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.044264230386013306, "control_flow_features": 0.02036524031132276, "complexity_features": 0.012095936487989933, "syntactic_features": 0.08570600197717396, "other_features": 0.12900201302342998}}}, "visualisations_generated": ["shap_summary_gradient_boosting_20251001_190703.png", "shap_bar_gradient_boosting_20251001_190703.png", "shap_waterfall_gradient_boosting_sample1_20251001_190703.png", "shap_waterfall_gradient_boosting_sample2_20251001_190703.png", "shap_waterfall_gradient_boosting_sample3_20251001_190703.png", "shap_dependence_gradient_boosting_feat1_20251001_190703.png", "shap_dependence_gradient_boosting_feat2_20251001_190703.png"]}