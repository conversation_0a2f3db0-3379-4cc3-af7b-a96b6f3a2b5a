{"ablation_analysis": {"grouping_effect": {"original_accuracy": 0.4590981012658228, "grouped_accuracy": 0.6225042944439751, "improvement": 0.16340619317815236, "improvement_percent": 35.59287061471387, "p_value": 0.0, "effect_size_cohens_d": 4.580071237047969, "significance": "Significant"}, "noerror_effect": {"without_noerror": 0.6941256491034302, "with_noerror": 0.5304374931378139, "change": -0.16368815596561637, "change_percent": -23.581920100063257, "p_value": 0.0, "effect_size_cohens_d": -17.2157405375947, "significance": "Significant"}, "scaling_effect": {"sizes": [1000, 5000, 11977], "accuracies": [0.6225042944439751, 0.6941256491034302, 0.7166792568250457], "correlation": 0.9924774408202487, "p_value": 0.07813588833430567, "significance": "Not Significant"}}, "model_comparison": {"HistGradient Boosting": {"rank": 1, "accuracy": 0.7166792568250457, "std": 0.003784643206261115, "ci_lower": 0.7164632047853304, "ci_upper": 0.7169422742189365}, "XGBoost": {"rank": 2, "accuracy": 0.7158441129210389, "std": 0.00551454673193809, "ci_lower": 0.7153647657378539, "ci_upper": 0.7160609422005725}, "Neural Network": {"rank": 3, "accuracy": 0.6841661860376975, "std": 0.007402023402361184, "ci_lower": 0.6836078599206338, "ci_upper": 0.684543634402387}, "Gradient Boosting": {"rank": 4, "accuracy": 0.6504021071715332, "std": 0.006182852531158087, "ci_lower": 0.6498958570187949, "ci_upper": 0.6506873662542186}, "Random Forest": {"rank": 5, "accuracy": 0.6428352487566238, "std": 0.006014487006088164, "ci_lower": 0.6421259557375663, "ci_upper": 0.6428752157202522}, "SVM (Linear)": {"rank": 6, "accuracy": 0.5970666401789606, "std": 0.007470028622191268, "ci_lower": 0.5964998894690576, "ci_upper": 0.5974156320864895}, "SVM (RBF)": {"rank": 7, "accuracy": 0.5870475607940898, "std": 0.004197145088502341, "ci_lower": 0.5866316727423885, "ci_upper": 0.5871358717695985}, "K-Nearest Neighbors": {"rank": 8, "accuracy": 0.577236013108669, "std": 0.003187152033491637, "ci_lower": 0.5771055572851386, "ci_upper": 0.5775017244791966}, "Decision Tree": {"rank": 9, "accuracy": 0.5497343940705192, "std": 0.016631911559279015, "ci_lower": 0.5494139831292522, "ci_upper": 0.55148291232008}, "Logistic Regression": {"rank": 10, "accuracy": 0.5453503093903952, "std": 0.0031317459033008547, "ci_lower": 0.5453376639807053, "ci_upper": 0.5457234989675205}, "Naive Bayes": {"rank": 11, "accuracy": 0.20692062571910494, "std": 0.00729622854330234, "ci_lower": 0.20651495548865875, "ci_upper": 0.20742915026041236}, "comparisons": {"HistGradient Boosting_vs_XGBoost": {"model1": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "model2": "XGBoost", "accuracy_diff": 0.000835143904006852, "p_value": 0.0008493783735889378, "effect_size": 0.17658697246764543, "significance": "Significant"}, "HistGradient Boosting_vs_Neural Network": {"model1": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "model2": "Neural Network", "accuracy_diff": 0.03251307078734822, "p_value": 0.0, "effect_size": 5.530846731876823, "significance": "Significant"}, "XGBoost_vs_Neural Network": {"model1": "XGBoost", "model2": "Neural Network", "accuracy_diff": 0.03167792688334137, "p_value": 0.0, "effect_size": 4.853460547093189, "significance": "Significant"}}}, "summary_table": [{"Analysis": "Error Type Grouping", "Comparison": "Original vs Grouped Classes", "Effect Size (Cohen's d)": "4.58", "P-value": "0.000", "Significance": "Significant", "Practical Impact": "+35.6%"}, {"Analysis": "NoError Class Effect", "Comparison": "Errors Only vs Errors+NoError", "Effect Size (Cohen's d)": "-17.22", "P-value": "0.000", "Significance": "Significant", "Practical Impact": "-23.6%"}, {"Analysis": "Dataset Scaling", "Comparison": "Size vs Performance Correlation", "Effect Size (Cohen's d)": "N/A (correlation)", "P-value": "0.078", "Significance": "Not Significant", "Practical Impact": "r=0.992"}]}