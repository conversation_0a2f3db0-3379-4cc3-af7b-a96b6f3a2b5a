{"general": {"random_state": 25, "set_process_seeds": true, "sample_size_limit": null}, "tuning": {"enabled": true, "top_n": 3, "random_search_iters": 4, "cv_folds": 3, "exclusions": [], "n_jobs": -1, "logistic_regression": {"C": [0.01, 0.03, 0.1, 0.3, 1.0, 3.0, 10.0, 30.0]}, "random_forest": {"n_estimators": [200, 300, 500], "max_depth": [null, 10, 15, 20], "min_samples_split": [2, 5, 10], "min_samples_leaf": [1, 2, 4], "max_features": ["sqrt", "log2", null]}, "svm_linear": {"C": [0.01, 0.03, 0.1, 0.3, 1.0, 3.0, 10.0, 30.0, 100.0]}, "svm_rbf": {"C": [0.1, 0.3, 1.0, 3.0, 10.0, 30.0], "gamma": ["scale", "auto", 0.001, 0.01, 0.1]}, "gradient_boosting": {"n_estimators": [100, 150, 200], "learning_rate": [0.05, 0.1, 0.2], "max_depth": [2, 3, 4], "subsample": [0.6, 0.8, 1.0]}, "neural_network": {"hidden_layer_sizes": [[50], [100], [50, 50], [100, 50]], "alpha": [1e-05, 0.0001, 0.001, 0.01], "lri": [0.001, 0.0005, 0.0001], "activation": ["relu", "tanh"]}, "knn": {"n_neighbors": [3, 5, 7, 9, 11], "weights": ["uniform", "distance"], "metric": ["euclidean", "manhattan", "minkowski", "cosine"]}, "xgboost": {"n_estimators": [200, 400], "learning_rate": [0.05, 0.1, 0.2], "max_depth": [4, 6, 8], "subsample": [0.6, 0.8, 1.0], "colsample_bytree": [0.6, 0.8, 1.0], "reg_lambda": [0.0, 1.0, 5.0], "reg_alpha": [0.0, 0.5, 1.0]}, "hist_gradient_boosting": {"learning_rate": [0.05, 0.1, 0.2], "max_leaf_nodes": [31, 63, 127], "l2_regularization": [0.0, 0.1, 1.0]}}, "selection": {"metric": "cv", "time_weight": 0.01, "time_norm": "min", "use_log": true, "top_n_for_tuning": 3, "stability": {"std_threshold": 0.12, "penalty_weight": 0.5, "mode": "linear", "gate": true, "max_std": 0.25}, "time_gate_multiple": 0.0}, "class_balance": {"use_class_weight": true, "class_weight": "balanced", "resampling": {"enabled": true, "method": "random_over", "strategy": "auto", "random_state": 1}, "report_distributions": true}, "explainability": {"use_shap": true, "shap_sample_size": 150, "permutation_importance_sample": 300, "shap_background_size": 50, "shap_max_evals_cap": 5000, "shap_skip_if_costly": true, "shap_model_allowlist": ["linear", "random_forest", "xgboost", "hist_gradient_boosting", "gradient_boosting", "neural_network"], "shap_top_n_tokens": 20}, "evaluation": {"bootstrap_iterations": 200, "bootstrap_random_state": 25, "calibration_bins": 10, "calibration_posthoc": {"enabled": true, "method": "isotonic", "cv": 5, "allow_in_nested": false, "n_jobs": null}, "pr_curves": {"plot": true}, "cv": {"split_random_state_mode": "global", "cv_random_state": 25, "n_splits": 3, "repeats": 2, "scoring": "accuracy", "warn_std_threshold": 0.15}, "seed_sensitivity": {"enabled": false, "seeds": [25, 101, 202, 303, 404], "count": 2, "base_seed": 25, "offset": 100, "skip_final_test": false}, "nested_cv": {"enabled": true, "outer_splits": 5, "outer_repeats": 1, "random_state": 1}, "learning_curve": {"enabled": true, "scoring": "balanced_accuracy", "random_state": 25, "save_csv": true, "train_sizes": [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95, 1.0]}, "significance": {"enabled": true, "top_k": 2, "methods": "both"}}, "features": {"tfidf_analyzer": "word", "char_ngram_range": [3, 5]}}