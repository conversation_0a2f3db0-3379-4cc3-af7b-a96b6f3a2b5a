{"run_id": "20251001_204942", "dataset_hash": "7bf80d629753fa70bd8cddab72b6a8732bbe26d608a729a7dd86926f8f2a973c", "experiment": {"grouping": true, "include_no_error": true, "label_space": "grouped", "balance_mode": "errors+noerror"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.31537489265454727, "Random Forest": 0.4079373564801024, "SVM (RBF)": 0.37100023037255375, "SVM (Linear)": 0.36731204267133527, "Naive Bayes": 0.10500016736467581, "Neural Network": 0.4586868033093763, "Gradient Boosting": 0.4114995518658442, "K-Nearest Neighbors": 0.37056241000632606, "Decision Tree": 0.3242501429631874, "XGBoost": 0.5301250556358624, "HistGradient Boosting": 0.5304374931378139}, "cv_means": {"Logistic Regression": 0.31537489265454727, "Random Forest": 0.4079373564801024, "SVM (RBF)": 0.37100023037255375, "SVM (Linear)": 0.36731204267133527, "Naive Bayes": 0.10500016736467581, "Neural Network": 0.4586868033093763, "Gradient Boosting": 0.4114995518658442, "K-Nearest Neighbors": 0.37056241000632606, "Decision Tree": 0.3242501429631874, "XGBoost": 0.5301250556358624, "HistGradient Boosting": 0.5304374931378139}, "cv_stds": {"Logistic Regression": 0.008634341509283596, "Random Forest": 0.0076694252100448015, "SVM (RBF)": 0.008439214830885906, "SVM (Linear)": 0.009666878853057757, "Naive Bayes": 0.0016040563390026838, "Neural Network": 0.012277502626671849, "Gradient Boosting": 0.007369663408867212, "K-Nearest Neighbors": 0.00778602069251424, "Decision Tree": 0.010295182967442954, "XGBoost": 0.004566146000609339, "HistGradient Boosting": 0.011983971514490122}, "times_sec": {"Logistic Regression": 10.947773899999447, "Random Forest": 0.4414414999773726, "SVM (RBF)": 110.20804729999509, "SVM (Linear)": 335.38286310003605, "Naive Bayes": 0.10853069997392595, "Neural Network": 26.28638609999325, "Gradient Boosting": 30.074450699961744, "K-Nearest Neighbors": 0.05591390002518892, "Decision Tree": 0.19228479999583215, "XGBoost": 5.188429899979383, "HistGradient Boosting": 12.594091900042258}, "best_model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.31537489265454727, "cv_std_accuracy": 0.008634341509283596, "cv_fold_scores": [0.3172103487064117, 0.31683539557555307, 0.3083270817704426, 0.3025871766029246, 0.3265841769778778, 0.3207051762940735], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 10.947773899999447, "cv_time_sec": 13.230704399989918, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4079373564801024, "cv_std_accuracy": 0.0076694252100448015, "cv_fold_scores": [0.3997000374953131, 0.4019497562804649, 0.4118529632408102, 0.4161979752530934, 0.4161979752530934, 0.40172543135783945], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.4414414999773726, "cv_time_sec": 1.787200800026767, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.37100023037255375, "cv_std_accuracy": 0.008439214830885906, "cv_fold_scores": [0.35808023997000377, 0.3715785526809149, 0.3792198049512378, 0.38095238095238093, 0.36970378702662166, 0.3664666166541635], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 110.20804729999509, "cv_time_sec": 56.47644560004119, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.36731204267133527, "cv_std_accuracy": 0.009666878853057757, "cv_fold_scores": [0.35883014623172105, 0.3603299587551556, 0.3582145536384096, 0.3779527559055118, 0.37945256842894637, 0.3690922730682671], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 335.38286310003605, "cv_time_sec": 160.2470884999493, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.10500016736467581, "cv_std_accuracy": 0.0016040563390026838, "cv_fold_scores": [0.10423697037870266, 0.10311211098612673, 0.10690172543135784, 0.10348706411698538, 0.10648668916385452, 0.10577644411102775], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.10853069997392595, "cv_time_sec": 0.9800209999084473, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4586868033093763, "cv_std_accuracy": 0.012277502626671849, "cv_fold_scores": [0.4461942257217848, 0.4668166479190101, 0.44223555888972244, 0.45894263217097864, 0.4739407574053243, 0.46399099774943736], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 26.28638609999325, "cv_time_sec": 17.69226139993407, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4114995518658442, "cv_std_accuracy": 0.007369663408867212, "cv_fold_scores": [0.41019872515935507, 0.4068241469816273, 0.41035258814703673, 0.4259467566554181, 0.41019872515935507, 0.4054763690922731], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 30.074450699961744, "cv_time_sec": 23.034001599997282, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.15, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 50, "n_iter_no_change": null, "random_state": 25, "subsample": 0.8, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.37056241000632606, "cv_std_accuracy": 0.00778602069251424, "cv_fold_scores": [0.3674540682414698, 0.35995500562429694, 0.37396849212303074, 0.3742032245969254, 0.3820772403449569, 0.3657164291072768], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.05591390002518892, "cv_time_sec": 1.924001200008206, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3242501429631874, "cv_std_accuracy": 0.010295182967442954, "cv_fold_scores": [0.3194600674915636, 0.33070866141732286, 0.3402100525131283, 0.3194600674915636, 0.3250843644544432, 0.31057764441110275], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.19228479999583215, "cv_time_sec": 0.9947639000602067, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5301250556358624, "cv_std_accuracy": 0.004566146000609339, "cv_fold_scores": [0.5331833520809899, 0.5230596175478065, 0.532258064516129, 0.5358080239970003, 0.5275590551181102, 0.5288822205551388], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 5.188429899979383, "cv_time_sec": 12.737016699975356, "best_params": {"classifier__base_estimator__subsample": 0.6, "classifier__base_estimator__reg_lambda": 5.0, "classifier__base_estimator__reg_alpha": 0.5, "classifier__base_estimator__n_estimators": 400, "classifier__base_estimator__max_depth": 8, "classifier__base_estimator__learning_rate": 0.2, "classifier__base_estimator__colsample_bytree": 0.6}, "best_cv": 0.5315624228516822, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.6, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.2, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=8, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=400, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5304374931378139, "cv_std_accuracy": 0.011983971514490122, "cv_fold_scores": [0.5193100862392201, 0.5226846644169478, 0.5390097524381096, 0.5500562429696289, 0.5298087739032621, 0.521755438859715], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 12.594091900042258, "cv_time_sec": 17.044794099987485, "best_params": {"classifier__max_leaf_nodes": 127, "classifier__learning_rate": 0.05, "classifier__l2_regularization": 0.0}, "best_cv": 0.5304374931378139, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.05, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 127, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.04156619997229427, "per_sample_latency_sec": 2.0783099986147136e-05, "training_time_sec_by_model": {"Logistic Regression": 10.947773899999447, "Random Forest": 0.4414414999773726, "SVM (RBF)": 110.20804729999509, "SVM (Linear)": 335.38286310003605, "Naive Bayes": 0.10853069997392595, "Neural Network": 26.28638609999325, "Gradient Boosting": 30.074450699961744, "K-Nearest Neighbors": 0.05591390002518892, "Decision Tree": 0.19228479999583215, "XGBoost": 5.188429899979383, "HistGradient Boosting": 12.594091900042258}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 8000, "n_test": 2000}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.2903225806451613, 0.42105263157894735, 0.46474358974358976, 0.49169435215946844, 0.5246913580246914, 0.575, 0.840625], "calibration_mean_pred": [0.3758756978753097, 0.45988631117867146, 0.5501866499135605, 0.6522170910268741, 0.7501790707237798, 0.8476434125098576, 0.977556592449673], "brier_score": 0.23682427152016194, "ece": 0.1548295510950369, "mce": 0.2726434125098578, "brier_reliability": 0.028402016628044237, "brier_resolution": 0.028025318571739222, "brier_uncertainty": 0.238764, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 0, 31, 152, 312, 301, 324, 240, 640], "bin_acc": [null, null, null, 0.2903225806451613, 0.42105263157894735, 0.46474358974358976, 0.49169435215946844, 0.5246913580246914, 0.575, 0.840625], "bin_conf": [null, null, null, 0.3758756978753097, 0.45988631117867146, 0.5501866499135601, 0.6522170910268741, 0.7501790707237795, 0.8476434125098578, 0.9775565924496732], "plot_frac_pos": [0.2903225806451613, 0.42105263157894735, 0.46474358974358976, 0.49169435215946844, 0.5246913580246914, 0.575, 0.840625], "plot_mean_pred": [0.3758756978753097, 0.45988631117867146, 0.5501866499135601, 0.6522170910268741, 0.7501790707237795, 0.8476434125098578, 0.9775565924496732]}, "post": {"calibration_frac_pos": [0.4, 0.4594594594594595, 0.5656565656565656, 0.6425269645608629, 0.7762863534675615, 0.8895705521472392, 0.9947916666666666], "calibration_mean_pred": [0.38790691964130286, 0.4654527350252431, 0.5579666493548312, 0.648423749190358, 0.7406860258593106, 0.845943905420636, 0.9563237078536408], "brier_score": 0.18897224408789018, "ece": 0.019115014107491918, "mce": 0.04362664672660288, "brier_reliability": 0.0006064529620779442, "brier_resolution": 0.021715180610060315, "brier_uncertainty": 0.21119100000000002, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 0, 5, 148, 396, 649, 447, 163, 192], "bin_acc": [null, null, null, 0.4, 0.4594594594594595, 0.5656565656565656, 0.6425269645608629, 0.7762863534675615, 0.8895705521472392, 0.9947916666666666], "bin_conf": [null, null, null, 0.38790691964130286, 0.46545273502524315, 0.5579666493548311, 0.6484237491903583, 0.7406860258593113, 0.8459439054206364, 0.9563237078536412], "plot_frac_pos": [0.4, 0.4594594594594595, 0.5656565656565656, 0.6425269645608629, 0.7762863534675615, 0.8895705521472392, 0.9947916666666666], "plot_mean_pred": [0.38790691964130286, 0.46545273502524315, 0.5579666493548311, 0.6484237491903583, 0.7406860258593113, 0.8459439054206364, 0.9563237078536412]}, "plots": {"pre": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting.png", "post": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting_(pre_vs_post).png"}}, "bootstrap_ci": {"accuracy_ci95": [0.5864875, 0.6265124999999999], "macro_f1_ci95": [0.39178565166994495, 0.4543368082264036], "balanced_accuracy_ci95": [0.4026073720863168, 0.46536918266273025]}, "artefact": {"path": "artefacts\\run_20251001_204942\\models\\histgradient_boosting_20251001_204942_2025-10-01T21-08-46.704584+00-00.joblib", "size_bytes": 17638480, "size_bytes_by_model": {"Logistic Regression": 397362, "Random Forest": 79677771, "SVM (RBF)": 23198842, "SVM (Linear)": 25654618, "Naive Bayes": 406170, "Neural Network": 707751, "Gradient Boosting": 2267134, "K-Nearest Neighbors": 28039225, "Decision Tree": 504691, "XGBoost": 18477521, "HistGradient Boosting": 17638480}}, "best_model_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.05, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 127, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "pr_curves_plot": "artefacts\\run_20251001_204942\\figures\\pr_curves_pr_curves_-_histgradient_boosting.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251001_204942\\tables\\per_class_metrics_20251001_204942.csv", "confusions_csv": "artefacts\\run_20251001_204942\\tables\\top_confusions_20251001_204942.csv", "misclassifications_csv": "artefacts\\run_20251001_204942\\tables\\misclassifications_20251001_204942.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251001_204942\\tables\\slices_20251001_204942.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-01T21:08:47.293318+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "argparse": null, "seaborn": "0.13.2"}}}