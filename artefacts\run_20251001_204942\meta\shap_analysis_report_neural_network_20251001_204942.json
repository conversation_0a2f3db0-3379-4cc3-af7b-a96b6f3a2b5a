{"metadata": {"model_name": "Neural Network", "run_id": "20251001_204942", "timestamp": "2025-10-01T21:24:57.295795+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.02398709907631891, "std_abs_shap": 0.05321837049834475, "max_abs_shap": 0.4847255115677533, "feature_index": 49}, {"rank": 2, "feature_name": "feat_function_calls", "mean_abs_shap": 0.023288165975642053, "std_abs_shap": 0.04388934852311637, "max_abs_shap": 0.41012547701158314, "feature_index": 25}, {"rank": 3, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.023138535380409914, "std_abs_shap": 0.035422197309941894, "max_abs_shap": 0.2717567088510899, "feature_index": 55}, {"rank": 4, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02106282602331369, "std_abs_shap": 0.05727679971702451, "max_abs_shap": 0.5661008761636767, "feature_index": 58}, {"rank": 5, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.018938981464916585, "std_abs_shap": 0.03211809432568704, "max_abs_shap": 0.23361390917083008, "feature_index": 44}, {"rank": 6, "feature_name": "feat_assignments", "mean_abs_shap": 0.01606752350350136, "std_abs_shap": 0.029809626854091754, "max_abs_shap": 0.2816934180340029, "feature_index": 2}, {"rank": 7, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.013643264003332274, "std_abs_shap": 0.0279952073691209, "max_abs_shap": 0.23930818063545936, "feature_index": 64}, {"rank": 8, "feature_name": "feat_if_statements", "mean_abs_shap": 0.011750559541705254, "std_abs_shap": 0.02398401614803006, "max_abs_shap": 0.2520441287350713, "feature_index": 31}, {"rank": 9, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.01037768115979435, "std_abs_shap": 0.019726211494646515, "max_abs_shap": 0.15330749906191865, "feature_index": 63}, {"rank": 10, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.00849369117190005, "std_abs_shap": 0.026430397848489537, "max_abs_shap": 0.34662004755751896, "feature_index": 3}, {"rank": 11, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.008395523903240823, "std_abs_shap": 0.021063912136803923, "max_abs_shap": 0.24368539145274584, "feature_index": 51}, {"rank": 12, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.008333353812294416, "std_abs_shap": 0.02083448701442545, "max_abs_shap": 0.28617418988398097, "feature_index": 0}, {"rank": 13, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.008283286827061642, "std_abs_shap": 0.023918979445283872, "max_abs_shap": 0.23617417703143076, "feature_index": 12}, {"rank": 14, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.008147177447159832, "std_abs_shap": 0.021939302604706314, "max_abs_shap": 0.23593109940305304, "feature_index": 16}, {"rank": 15, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.008071266488474138, "std_abs_shap": 0.01959530800766958, "max_abs_shap": 0.17228379464307128, "feature_index": 48}, {"rank": 16, "feature_name": "feat_function_defs", "mean_abs_shap": 0.006423658882608941, "std_abs_shap": 0.021631589327923455, "max_abs_shap": 0.34956956654224813, "feature_index": 26}, {"rank": 17, "feature_name": "feat_colon_count", "mean_abs_shap": 0.006398682122825766, "std_abs_shap": 0.02157745418659558, "max_abs_shap": 0.34847414005688243, "feature_index": 15}, {"rank": 18, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.006074338226098046, "std_abs_shap": 0.024970277104328677, "max_abs_shap": 0.4217574854399918, "feature_index": 62}, {"rank": 19, "feature_name": "feat_if_else_chains", "mean_abs_shap": 0.005277714648392751, "std_abs_shap": 0.020278146122369174, "max_abs_shap": 0.2536702483361527, "feature_index": 30}, {"rank": 20, "feature_name": "feat_for_loops", "mean_abs_shap": 0.005248280500221411, "std_abs_shap": 0.016937583875207975, "max_abs_shap": 0.2510504791939999, "feature_index": 24}], "total_importance": 0.3035148098034424, "top_10_importance_ratio": 0.5625700024700997}, "feature_statistics": {"mean_importance": 0.004274856476104823, "median_importance": 0.0017580668411443145, "std_importance": 0.006057196835865781, "max_importance": 0.02398709907631891, "min_importance": 0.0, "importance_concentration": 0.3637898526009548}, "ast_feature_analysis": {"structural_features": [{"name": "feat_nodes_with_children", "importance": 0.023138535380409914, "rank": 55}, {"name": "feat_leaf_nodes", "importance": 0.018938981464916585, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.013643264003332274, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.01037768115979435, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.0049717986950973654, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.011750559541705254, "rank": 31}, {"name": "feat_if_else_chains", "importance": 0.005277714648392751, "rank": 30}, {"name": "feat_for_loops", "importance": 0.005248280500221411, "rank": 24}, {"name": "feat_string_formatting", "importance": 0.0023149555717351606, "rank": 61}, {"name": "feat_nested_ifs", "importance": 0.0021416331074035912, "rank": 53}, {"name": "feat_nested_loops", "importance": 0.001779042254047298, "rank": 54}, {"name": "feat_while_loops", "importance": 0.0014265375269965905, "rank": 68}, {"name": "feat_keyword_if_count", "importance": 0.00048442161600479943, "rank": 37}, {"name": "feat_keyword_while_count", "importance": 0.00038334601141805445, "rank": 41}, {"name": "feat_keyword_for_count", "importance": 0.0002476020431959895, "rank": 36}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.008071266488474138, "rank": 48}, {"name": "feat_max_function_complexity", "importance": 0.003704883253189912, "rank": 50}, {"name": "feat_cyclomatic_complexity", "importance": 0.003671173709307866, "rank": 19}, {"name": "feat_avg_branching_factor", "importance": 0.003605561852542775, "rank": 5}, {"name": "feat_avg_function_complexity", "importance": 0.0020666516287070917, "rank": 6}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.02398709907631891, "rank": 49}, {"name": "feat_function_calls", "importance": 0.023288165975642053, "rank": 25}, {"name": "feat_assignments", "importance": 0.01606752350350136, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.00849369117190005, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.008283286827061642, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0008274877329487506, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.02106282602331369, "rank": 58}, {"name": "feat_max_function_params", "importance": 0.008395523903240823, "rank": 51}, {"name": "feat_arithmetic_ops", "importance": 0.008333353812294416, "rank": 0}, {"name": "feat_comparison_ops", "importance": 0.008147177447159832, "rank": 16}, {"name": "feat_function_defs", "importance": 0.006423658882608941, "rank": 26}, {"name": "feat_colon_count", "importance": 0.006398682122825766, "rank": 15}, {"name": "feat_subscript_access", "importance": 0.006074338226098046, "rank": 62}, {"name": "feat_keyword_def_count", "importance": 0.004442934674346333, "rank": 34}, {"name": "feat_import_statements", "importance": 0.0043416160274328975, "rank": 32}, {"name": "feat_has_syntax_error", "importance": 0.002981438251077594, "rank": 29}, {"name": "feat_keyword_return_count", "importance": 0.0027817148245059924, "rank": 39}, {"name": "feat_line_count", "importance": 0.0027287466356294156, "rank": 45}, {"name": "feat_generator_exps", "importance": 0.0021725075195001557, "rank": 28}, {"name": "feat_list_comprehensions", "importance": 0.0019857249806646098, "rank": 46}, {"name": "feat_paren_count", "importance": 0.0017580668411443145, "rank": 56}, {"name": "feat_char_count", "importance": 0.001636499051096303, "rank": 13}, {"name": "feat_generator_expressions", "importance": 0.0014181248234304948, "rank": 27}, {"name": "feat_list_comps", "importance": 0.0009375333742624892, "rank": 47}, {"name": "feat_boolean_ops", "importance": 0.0008660658915202492, "rank": 8}, {"name": "feat_word_count", "importance": 0.0008470833290763872, "rank": 70}, {"name": "feat_bracket_count", "importance": 0.0008292405224524529, "rank": 10}, {"name": "feat_brace_count", "importance": 0.0008042754670446817, "rank": 9}, {"name": "feat_equals_count", "importance": 0.000720610713434784, "rank": 23}, {"name": "feat_raise_statements", "importance": 0.0006040726361906858, "rank": 57}, {"name": "feat_class_defs", "importance": 0.0005262501347767899, "rank": 14}, {"name": "feat_decorator_usage", "importance": 0.000485550674591927, "rank": 20}, {"name": "feat_keyword_class_count", "importance": 0.00046658614737958467, "rank": 33}, {"name": "feat_break_statements", "importance": 0.0004042117496045348, "rank": 11}, {"name": "feat_keyword_import_count", "importance": 0.00027387451155746703, "rank": 38}, {"name": "feat_lambda_usage", "importance": 0.0002714496242270263, "rank": 43}, {"name": "feat_lambda_functions", "importance": 0.00020392623668782914, "rank": 42}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.07107026070355049, "control_flow_features": 0.0310540928211209, "complexity_features": 0.021119536932221784, "syntactic_features": 0.08094725428737276, "other_features": 0.09932366505917652}}}, "visualisations_generated": ["shap_summary_neural_network_20251001_204942.png", "shap_bar_neural_network_20251001_204942.png", "shap_waterfall_neural_network_sample1_20251001_204942.png", "shap_waterfall_neural_network_sample2_20251001_204942.png", "shap_waterfall_neural_network_sample3_20251001_204942.png", "shap_dependence_neural_network_feat1_20251001_204942.png", "shap_dependence_neural_network_feat2_20251001_204942.png"]}