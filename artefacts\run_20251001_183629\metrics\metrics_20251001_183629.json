{"run_id": "20251001_183629", "dataset_hash": "2749c499b15de16bdf1a5a84a28dded6838ef5f4e0e4786b24ea375d310dc784", "experiment": {"grouping": false, "include_no_error": false, "label_space": "original", "balance_mode": "errors-only"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.35213607594936713, "Random Forest": 0.39936708860759496, "SVM (RBF)": 0.3270569620253164, "SVM (Linear)": 0.4276898734177215, "Naive Bayes": 0.3240506329113924, "Neural Network": 0.19798259493670886, "Gradient Boosting": 0.4590981012658228, "K-Nearest Neighbors": 0.3081091772151899, "Decision Tree": 0.3553006329113924, "XGBoost": 0.4526503164556962, "HistGradient Boosting": 0.44620253164556967}, "cv_means": {"Logistic Regression": 0.35213607594936713, "Random Forest": 0.39936708860759496, "SVM (RBF)": 0.3270569620253164, "SVM (Linear)": 0.4276898734177215, "Naive Bayes": 0.3240506329113924, "Neural Network": 0.19798259493670886, "Gradient Boosting": 0.4590981012658228, "K-Nearest Neighbors": 0.3081091772151899, "Decision Tree": 0.3553006329113924, "XGBoost": 0.4526503164556962, "HistGradient Boosting": 0.44620253164556967}, "cv_stds": {"Logistic Regression": 0.02582056771527976, "Random Forest": 0.02564267668339606, "SVM (RBF)": 0.03240311628842339, "SVM (Linear)": 0.06292337773920915, "Naive Bayes": 0.0817579281172154, "Neural Network": 0.03026949463712124, "Gradient Boosting": 0.030869573261224862, "K-Nearest Neighbors": 0.045767228418492914, "Decision Tree": 0.03214518580873, "XGBoost": 0.04892173328036157, "HistGradient Boosting": 0.07433408909642056}, "times_sec": {"Logistic Regression": 0.01147810008842498, "Random Forest": 0.14733079995494336, "SVM (RBF)": 0.028009100002236664, "SVM (Linear)": 0.015155000030063093, "Naive Bayes": 0.0029788000974804163, "Neural Network": 0.08891209994908422, "Gradient Boosting": 2.0182329000672325, "K-Nearest Neighbors": 0.002757800044491887, "Decision Tree": 0.006513900007121265, "XGBoost": 0.8576824000338092, "HistGradient Boosting": 0.48932890000287443}, "best_model_name": "Grad<PERSON>"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.35213607594936713, "cv_std_accuracy": 0.02582056771527976, "cv_fold_scores": [0.3375, 0.3291139240506329, 0.3875, 0.35443037974683544], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.01147810008842498, "cv_time_sec": 1.044086999958381, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.39936708860759496, "cv_std_accuracy": 0.02564267668339606, "cv_fold_scores": [0.375, 0.379746835443038, 0.425, 0.4177215189873418], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.14733079995494336, "cv_time_sec": 0.8707823000149801, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3270569620253164, "cv_std_accuracy": 0.03240311628842339, "cv_fold_scores": [0.2875, 0.34177215189873417, 0.3625, 0.31645569620253167], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.028009100002236664, "cv_time_sec": 1.1821365000214428, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4276898734177215, "cv_std_accuracy": 0.06292337773920915, "cv_fold_scores": [0.4125, 0.35443037974683544, 0.4375, 0.5063291139240507], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.015155000030063093, "cv_time_sec": 1.1271985999774188, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3240506329113924, "cv_std_accuracy": 0.0817579281172154, "cv_fold_scores": [0.225, 0.2911392405063291, 0.375, 0.4050632911392405], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.0029788000974804163, "cv_time_sec": 0.6962320000166073, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.19798259493670886, "cv_std_accuracy": 0.03026949463712124, "cv_fold_scores": [0.2, 0.189873417721519, 0.2375, 0.16455696202531644], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.08891209994908422, "cv_time_sec": 1.1633669999428093, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4590981012658228, "cv_std_accuracy": 0.030869573261224862, "cv_fold_scores": [0.425, 0.45569620253164556, 0.5, 0.45569620253164556], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 2.0182329000672325, "cv_time_sec": 2.4666232999879867, "best_params": {"classifier__subsample": 1.0, "classifier__n_estimators": 200, "classifier__max_depth": 4, "classifier__learning_rate": 0.1}, "best_cv": 0.4590981012658228, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3081091772151899, "cv_std_accuracy": 0.045767228418492914, "cv_fold_scores": [0.275, 0.26582278481012656, 0.3625, 0.3291139240506329], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.002757800044491887, "cv_time_sec": 1.2246086999075487, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.3553006329113924, "cv_std_accuracy": 0.03214518580873, "cv_fold_scores": [0.4, 0.35443037974683544, 0.325, 0.34177215189873417], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.006513900007121265, "cv_time_sec": 0.69792679999955, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4526503164556962, "cv_std_accuracy": 0.04892173328036157, "cv_fold_scores": [0.525, 0.43037974683544306, 0.4375, 0.4177215189873418], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.8576824000338092, "cv_time_sec": 1.8603038999717683, "best_params": {"classifier__base_estimator__subsample": 1.0, "classifier__base_estimator__reg_lambda": 5.0, "classifier__base_estimator__reg_alpha": 0.0, "classifier__base_estimator__n_estimators": 400, "classifier__base_estimator__max_depth": 8, "classifier__base_estimator__learning_rate": 0.05, "classifier__base_estimator__colsample_bytree": 0.6}, "best_cv": 0.4526503164556962, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.6, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.05, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=8, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=400, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.44620253164556967, "cv_std_accuracy": 0.07433408909642056, "cv_fold_scores": [0.55, 0.3924050632911392, 0.45, 0.3924050632911392], "cv_used_splits": 2, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.48932890000287443, "cv_time_sec": 1.4665214999113232, "best_params": {"classifier__max_leaf_nodes": 63, "classifier__learning_rate": 0.05, "classifier__l2_regularization": 0.1}, "best_cv": 0.44620253164556967, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.1, "learning_rate": 0.05, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 63, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.0024124999763444066, "per_sample_latency_sec": 6.0312499408610164e-05, "training_time_sec_by_model": {"Logistic Regression": 0.01147810008842498, "Random Forest": 0.14733079995494336, "SVM (RBF)": 0.028009100002236664, "SVM (Linear)": 0.015155000030063093, "Naive Bayes": 0.0029788000974804163, "Neural Network": 0.08891209994908422, "Gradient Boosting": 2.0182329000672325, "K-Nearest Neighbors": 0.002757800044491887, "Decision Tree": 0.006513900007121265, "XGBoost": 0.8576824000338092, "HistGradient Boosting": 0.48932890000287443}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 159, "n_test": 40}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.0, 0.0, 0.0, 0.4], "calibration_mean_pred": [0.6308245583884832, 0.7822153475192418, 0.831259133909929, 0.9825490056646521], "brier_score": 0.5672597597147159, "ece": 0.5973790838714361, "mce": 0.831259133909929, "brier_reliability": 0.35935971270080064, "brier_resolution": 0.0175, "brier_uncertainty": 0.22749999999999998, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 0, 0, 0, 0, 3, 1, 1, 35], "bin_acc": [null, null, null, null, null, null, 0.0, 0.0, 0.0, 0.4], "bin_conf": [null, null, null, null, null, null, 0.6308245583884832, 0.7822153475192418, 0.831259133909929, 0.9825490056646521], "plot_frac_pos": [0.0, 0.0, 0.0, 0.4], "plot_mean_pred": [0.6308245583884832, 0.7822153475192418, 0.831259133909929, 0.9825490056646521]}, "post": null, "plots": {"pre": "artefacts\\run_20251001_183629\\figures\\calibration_reliability_curve_-_gradient_boosting.png", "post": null, "comparison": null}}, "bootstrap_ci": {"accuracy_ci95": [0.225, 0.5], "macro_f1_ci95": [0.12914611886651362, 0.27835521103709965], "balanced_accuracy_ci95": [0.1492092803030303, 0.3515595238095238]}, "artefact": {"path": "artefacts\\run_20251001_183629\\models\\gradient_boosting_20251001_183629_2025-10-01T18-37-04.258884+00-00.joblib", "size_bytes": 5748188, "size_bytes_by_model": {"Logistic Regression": 16790, "Random Forest": 3414755, "SVM (RBF)": 234682, "SVM (Linear)": 161466, "Naive Bayes": 26258, "Neural Network": 326879, "Gradient Boosting": 5748188, "K-Nearest Neighbors": 212257, "Decision Tree": 22027, "XGBoost": 5237068, "HistGradient Boosting": 942560}}, "best_model_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "pr_curves_plot": "artefacts\\run_20251001_183629\\figures\\pr_curves_pr_curves_-_gradient_boosting.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251001_183629\\tables\\per_class_metrics_20251001_183629.csv", "confusions_csv": "artefacts\\run_20251001_183629\\tables\\top_confusions_20251001_183629.csv", "misclassifications_csv": "artefacts\\run_20251001_183629\\tables\\misclassifications_20251001_183629.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251001_183629\\tables\\slices_20251001_183629.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-01T18:37:04.697749+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "argparse": null, "seaborn": "0.13.2"}}}