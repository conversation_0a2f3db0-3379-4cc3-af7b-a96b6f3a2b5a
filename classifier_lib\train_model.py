""" End-to-end training and model selection pipeline with reporting."""
from __future__ import annotations
import json
from datetime import datetime, timezone
from typing import Any, Optional
import numpy as np
from sklearn.metrics import accuracy_score, balanced_accuracy_score, f1_score
from sklearn.model_selection import StratifiedKFold, RepeatedStratifiedKFold
from classifier_lib.config import name_to_type
from classifier_lib.create_pipeline import create_classifier_pipeline
from classifier_lib.preprocessing import (
    cv_splitter_seed,
    maybe_seed_process,
    cfg_seed,
)
from classifier_lib.testing import train_and_evaluate, hyperparameter_search
from classifier_lib.model_selection import (
    compute_selection_scores,
    select_best_model,
    should_run_final_test,
)

def nested_cv_evaluation(
    X,
    y,
    *,
    cfg: dict | None = None,
    dataset_hash: Optional[str] = None,
    models: list[str] | None = None,
) -> None:
    """Perform nested cross-validation on training data only.

    - Uses an outer StratifiedKFold/RepeatedStratifiedKFold for unbiased generalisation estimates.
    - For each outer split, runs inner hyperparameter search as configured (if enabled).
    - Evaluates models and prints aggregate statistics.
    - No artefacts or metrics files are persisted; console output only for dissertation reporting.
    """

    if cfg is None:
        cfg = {}
    print("\n=== NESTED CROSS-VALIDATION (training data only) ===")
    if dataset_hash:
        print(f"Dataset hash: {str(dataset_hash)[:12]}…")
    # Mark in-progress to prevent any saving in subordinate utilities (defensive)
    cfg = json.loads(json.dumps(cfg))  # shallow clone via JSON for safety
    cfg.setdefault("evaluation", {}).setdefault("nested_cv", {})["in_progress"] = True

    # Outer CV config
    nc = cfg.get("evaluation", {}).get("nested_cv", {})
    # Support both new keys (outer_*) and legacy keys (n_splits/repeats)
    n_splits = int(nc.get("outer_splits", nc.get("n_splits", 3)))
    repeats = int(nc.get("outer_repeats", nc.get("repeats", 1)))
    outer_seed = cfg_seed(cfg, 25)
    split_seed = cv_splitter_seed(cfg, outer_seed)
    if repeats > 1:
        outer_cv = RepeatedStratifiedKFold(
            n_splits=n_splits, n_repeats=repeats, random_state=split_seed
        )
    else:
        outer_cv = StratifiedKFold(
            n_splits=n_splits, shuffle=True, random_state=split_seed
        )

    # Model shortlist configurable; defaults sensible
    shortlist = (
        cfg.get("evaluation", {})
        .get("nested_cv", {})
        .get(
            "models",
            models,
        )
    )
    # Map display name -> classifier_type via create_pipeline mapping in this module

    # Scoring selection (same mapping as elsewhere)
    scoring_name = str(
        cfg.get("evaluation", {}).get("cv", {}).get("scoring", "accuracy")
    )

    def get_mfba_score(y_true, y_pred):
        """Returns the macro F1 score, balanced accuracy or accuracy based on scoring_name"""
        if scoring_name.lower() == "macro_f1":
            return f1_score(y_true, y_pred, average="macro", zero_division=0)
        if scoring_name.lower() == "balanced_accuracy":
            return balanced_accuracy_score(y_true, y_pred)
        return accuracy_score(y_true, y_pred)

    tuning_enabled = bool(cfg.get("tuning", {}).get("enabled", True))

    # Aggregate per-model outer scores
    outer_scores: dict[str, list[float]] = {
        name: [] for name in shortlist if name in name_to_type
    }

    # Iterate outer folds
    y_array = np.array(y)
    indices = np.arange(len(y_array))
    k = 0
    for train_idx, test_idx in outer_cv.split(indices, y_array):
        k += 1
        print(f"\nOuter fold {k}: train={len(train_idx)} test={len(test_idx)}")
        # Slice data
        if hasattr(X, "iloc"):
            X_tr, X_te = X.iloc[train_idx], X.iloc[test_idx]
        else:
            X_np = np.array(X, dtype=object)
            X_tr, X_te = X_np[train_idx], X_np[test_idx]
        if hasattr(y, "iloc"):
            y_tr, y_te = y.iloc[train_idx], y.iloc[test_idx]
        else:
            y_tr, y_te = y_array[train_idx], y_array[test_idx]
        for display_name in outer_scores.keys():
            clf_type = name_to_type[display_name]
            base = create_classifier_pipeline(clf_type, cfg=cfg)
            model = base
            if tuning_enabled:
                model, _, _ = hyperparameter_search(
                    base,
                    clf_type,
                    X_tr,
                    y_tr,
                    n_iter=int(cfg.get("tuning", {}).get("random_search_iters", 4)),
                    random_state=cfg_seed(cfg, 25),
                    cfg=cfg,
                )
            model.fit(X_tr, y_tr)
            y_hat = model.predict(X_te)
            s = get_mfba_score(y_te, y_hat)
            outer_scores[display_name].append(float(s))
            print(f"  {display_name:<22} outer score = {s:.4f}")

    # Print aggregate summary
    print("\n=== Nested CV summary (outer-fold scores) ===")
    for name, scores in outer_scores.items():
        if not scores:
            continue
        arr = np.array(scores, dtype=float)
        mean = float(arr.mean())
        std = float(arr.std(ddof=1)) if len(arr) > 1 else 0.0
        ci = 1.96 * (std / np.sqrt(len(arr))) if len(arr) > 1 else 0.0
        print(
            f"{name:<22} mean={mean:.4f} std={std:.4f} ci95=[{mean-ci:.4f}, {mean+ci:.4f}] n={len(arr)}"
        )
    print("(No artefacts saved; training-only diagnostic.)")


def train_baseline_models(cfg, X_train, y_train, X_test, y_test):
    """Train baseline models and perform hyperparameter tuning if enabled."""

    exclusions = set(
        str(x) for x in (cfg.get("tuning", {}) or {}).get("exclusions", [])
    )
    X_cv, y_cv = X_train, y_train

    results_by_name = {}
    fitted_models = {}
    timings = {}

    print("\n=== BASELINE FIT + CV (no test peeking) ===")
    for display_name, ctype in name_to_type.items():
        if any(
            ex.lower() in display_name.lower() or ex.lower() in ctype.lower()
            for ex in exclusions
        ):
            print(f"Skipping {display_name} (excluded)")
            continue

        try:
            pipe = create_classifier_pipeline(ctype, cfg=cfg)
        except Exception as e: 
            print(f"Skipping {display_name}: {e}")
            continue

        model, metrics = train_and_evaluate(
            pipe,
            X_train,
            y_train,
            X_test,
            y_test,
            X_cv,
            y_cv,
            model_name=display_name,
            evaluate_on_test=False,
            cfg=cfg,
        )
        results_by_name[display_name] = metrics
        fitted_models[display_name] = model
        timings[display_name] = {
            "fit_time_sec": metrics.get("fit_time_sec"),
            "cv_time_sec": metrics.get("cv_time_sec"),
        }

    # Hyperparameter tuning for top-n
    if bool(cfg.get("tuning", {}).get("enabled", True)):

        def cv_mean(m):
            v = results_by_name[m].get("cv_mean_accuracy")
            try:
                return float(v) if v is not None else -np.inf
            except Exception:
                return -np.inf

        top_n = int(
            cfg.get("selection", {}).get(
                "top_n_for_tuning", cfg.get("tuning", {}).get("top_n", 3)
            )
        )
        cand = sorted(results_by_name.keys(), key=cv_mean, reverse=True)[
            : max(0, top_n)
        ]

        if cand:
            print("-" * 80)
            print(f"Top Candidates for tuning: {', '.join(cand)}")
            print("-" * 80)
            print(f"\n=== HYPERPARAMETER TUNING (top {len(cand)}) ===")

        for display_name in cand:
            ctype = name_to_type[display_name]
            base = fitted_models[display_name]
            tuned, best_params, best_cv = hyperparameter_search(
                base,
                ctype,
                X_train,
                y_train,
                n_iter=int(cfg.get("tuning", {}).get("random_search_iters", 4)),
                random_state=cfg_seed(cfg, 25),
                cfg=cfg,
            )

            # Re-evaluate tuned model via CV only
            tuned_model, tuned_metrics = train_and_evaluate(
                tuned,
                X_train,
                y_train,
                X_test,
                y_test,
                X_cv,
                y_cv,
                model_name=f"{display_name} (tuned)",
                evaluate_on_test=False,
                cfg=cfg,
            )

            # Keep tuned model under original display name if improved
            prev = results_by_name.get(display_name, {})
            if (tuned_metrics.get("cv_mean_accuracy") or -np.inf) > (
                prev.get("cv_mean_accuracy") or -np.inf
            ):
                results_by_name[display_name] = tuned_metrics | {
                    "best_params": best_params,
                    "best_cv": best_cv,
                }
                fitted_models[display_name] = tuned_model
                timings[display_name] = {
                    "fit_time_sec": tuned_metrics.get("fit_time_sec"),
                    "cv_time_sec": tuned_metrics.get("cv_time_sec"),
                }

    return fitted_models, results_by_name, timings


def get_empty_test_results():
    """Return empty test results structure when final test is skipped."""
    return {
        "y_test_pred": None,
        "inference_time": None,
        "per_sample_latency": None,
        "pre_cal": None,
        "post_cal": None,
        "bootstrap_ci": None,
        "pr_plot_path": None,
        "error_analysis_paths": {},
        "significance_csv": None,
        "plots": {},
    }


def save_best_model(best_model, best_name, run_id, subdirs):
    """Save the best model to disk as a joblib artefact."""
    try:
        import joblib # pylint: disable=import-outside-toplevel

        timestamp = datetime.now(timezone.utc).isoformat().replace(":", "-")
        filename = f"{best_name.replace(' ', '_').lower()}_{run_id}_{timestamp}.joblib"
        artefact_path = subdirs["models"] / filename
        joblib.dump(best_model, artefact_path)
        print(f"Saved best model ({best_name}) -> {artefact_path}")
        return artefact_path
    except Exception as e: #pylint: disable=broad-except
        print(f"Failed to save model artefact: {e}")
        return None


def build_efficiency_data(timings, test_results):
    """Build efficiency data structure for JSON export."""
    return {
        "inference_time_sec": test_results.get("inference_time"),
        "per_sample_latency_sec": test_results.get("per_sample_latency"),
        "training_time_sec_by_model": {
            n: timings.get(n, {}).get("fit_time_sec") for n in timings
        },
    }


def build_calibration_data(test_results, bins):
    """Build calibration data structure for JSON export."""
    return {
        "bins": bins,
        "pre": test_results.get("pre_cal"),
        "post": test_results.get("post_cal"),
        "plots": test_results.get("plots", {}),
    }


def extract_best_model_params(best_model):
    """Extract sanitised parameters from the best model."""
    try:
        clf = None
        if hasattr(best_model, "named_steps"):
            clf = best_model.named_steps.get("classifier")
        clf = clf or best_model
        if hasattr(clf, "get_params"):
            params = clf.get_params(deep=False)
            # Sanitise for JSON serialisation
            sanitised = {}
            for k, v in params.items():
                try:
                    json.dumps(v)
                    sanitised[k] = v
                except (TypeError, ValueError):
                    sanitised[k] = str(v)
            return sanitised
    except Exception:
        pass
    return None


def build_slices_data(test_results, cfg):
    """Build slices data structure for JSON export."""
    slice_cfg = (
        (cfg.get("evaluation", {}) or {}).get("slices", {})
        if isinstance(cfg, dict)
        else {}
    )
    return {
        "metrics_csv": test_results.get("slices_csv"),
        "settings": {
            "rare_threshold": int(slice_cfg.get("rare_threshold", 5)),
            "length_short_max": int(slice_cfg.get("length_short_max", 5)),
            "length_long_min": int(slice_cfg.get("length_long_min", 21)),
        },
    }



def estimate_param_count(model):
    """Estimate parameter count for a fitted model."""
    try:
        # Extract classifier from pipeline if needed
        clf = model
        if hasattr(model, "named_steps"):
            clf = model.named_steps.get("classifier", model)

        # Different estimation strategies based on model type
        if hasattr(clf, "coef_"):
            # Linear models
            coef = clf.coef_
            intercept = getattr(clf, "intercept_", None)
            params = np.prod(coef.shape)
            if intercept is not None:
                params += np.prod(intercept.shape) if hasattr(intercept, "shape") else 1
            return int(params)
        elif hasattr(clf, "n_features_"):
            # Tree-based models - rough estimate based on nodes
            return getattr(clf, "tree_", {}).get("node_count", 0) * 2
        elif hasattr(clf, "support_vectors_"):
            # SVM models
            return len(clf.support_vectors_)
        else:
            # Default fallback
            return None
    except Exception: #pylint: disable=broad-except
        return None


def normalise_params(model):
    """Extract and normalise model parameters for comparison."""
    try:
        clf = model
        if hasattr(model, "named_steps"):
            clf = model.named_steps.get("classifier", model)

        if hasattr(clf, "get_params"):
            params = clf.get_params(deep=False)
            normalised = {}
            for k, v in params.items():
                try:
                    json.dumps(v)
                    normalised[k] = v
                except (TypeError, ValueError):
                    normalised[k] = str(v)
            return normalised
    except Exception: #pylint: disable=broad-except
        pass
    return {}


def train_model(
    X_train,
    y_train,
    X_test,
    y_test,
    *,
    cfg: dict | None = None,
    dataset_hash: Optional[str] = None,
    test_metadata: Any | None = None,
    run_data: list[any] = None,
):
    """End-to-end training/selection pipeline across multiple models with reporting.

    Returns (best_model, models_by_name, run_info)
    """
    if run_data is None:
        run_data = ["", "", {}]
    if cfg is None:
        cfg = {}
    maybe_seed_process(cfg)

    # Generate consistent run_id for this entire training run
    run_id, _, subdirs = run_data

    # Train baseline models and optional hyperparameter tuning
    fitted_models, results_by_name, timings = train_baseline_models(
        cfg, X_train, y_train, X_test, y_test
    )

    # Model selection with scoring and stability analysis

    names, cv_means, sel_scores, times = compute_selection_scores(
        results_by_name, timings, cfg
    )
    best_name, best_model, best_idx = select_best_model(
        names, sel_scores, fitted_models
    )
    do_final_test = should_run_final_test(best_name, results_by_name, cfg)

    # Model evaluation on test set (if enabled)
    test_results = {}
    if do_final_test:
        from classifier_lib.model_evaluation import (
            evaluate_model_on_test,
            performcalibration_analysis,
            computebootstrap_ci,
        ) #pylint: disable=import-outside-toplevel

        # Test evaluation and PR curves
        test_results = evaluate_model_on_test(
            best_model, X_test, y_test, best_name, cfg, subdirs
        )

        # Calibration analysis
        bins = int((cfg.get("evaluation", {}) or {}).get("calibration_bins", 10))
        calibration_results = performcalibration_analysis(
            best_model, X_train, y_train, X_test, y_test, best_name, cfg, subdirs, bins
        )
        test_results.update(calibration_results)

        # Bootstrap confidence intervals
        test_results["bootstrap_ci"] = computebootstrap_ci(
            y_test, test_results["y_test_pred"], cfg
        )

        # Error analysis and slice metrics
        from classifier_lib.error_analysis import (
            export_error_analysis,
            compute_significance_tests,
        ) #pylint: disable=import-outside-toplevel

        error_analysis_paths = export_error_analysis(
            y_test,
            test_results["y_test_pred"],
            X_test,
            y_train,
            test_metadata,
            run_id,
            subdirs,
            cfg,
        )
        test_results["error_analysis_paths"] = error_analysis_paths

        # Significance testing between top models
        test_results["significance_csv"] = compute_significance_tests(
            names, sel_scores, fitted_models, X_test, y_test, run_id, subdirs
        )
    else:
        print("\nSkipping final test evaluation (invalid CV scores or user override).")
        test_results = get_empty_test_results()

    # Save best model artefact
    artefact_path = save_best_model(
        best_model, best_name, run_id, subdirs
    )
    artefact_size = (
        artefact_path.stat().st_size
        if artefact_path and artefact_path.exists()
        else None
    )

    # Build and persist comprehensive metrics
    from classifier_lib.metrics_persistence import (
        sanitise_classifier_params,
        extract_model_info,
        compute_model_sizes,
        build_combined_json,
        persist_metrics_json,
        update_aggregate_csv,
        create_run_summary,
        compute_efficiency_metrics,
    )

    # Prepare data structures for JSON export
    cv_details_aug = sanitise_classifier_params(results_by_name, fitted_models)
    model_info = extract_model_info(best_model)
    size_bytes_by_model = compute_model_sizes(fitted_models)
    data_info = {"n_train": len(y_train), "n_test": len(y_test)}
    bins = int((cfg.get("evaluation", {}) or {}).get("calibration_bins", 10))
    # Build combined JSON structure
    combined_json = build_combined_json(
        run_id,
        dataset_hash,
        cfg,
        names,
        sel_scores,
        cv_means,
        np.array([results_by_name[n].get("cv_std_accuracy", 0.0) for n in names]),
        times,
        best_name,
        cv_details_aug,
        build_efficiency_data(timings, test_results),
        model_info,
        data_info,
        build_calibration_data(test_results, bins),
        test_results.get("bootstrap_ci"),
        {
            "path": str(artefact_path) if artefact_path else None,
            "size_bytes": artefact_size,
            "size_bytes_by_model": size_bytes_by_model,
        },
        extract_best_model_params(best_model),
        test_results.get("pr_plot_path"),
        test_results.get("error_analysis_paths", {}),
        build_slices_data(test_results, cfg),
        do_final_test,
    )

    # Persist all metrics and summaries
    json_path = persist_metrics_json(combined_json, run_id, subdirs)
    update_aggregate_csv(
        run_id,
        dataset_hash,
        best_name,
        results_by_name,
        sel_scores,
        best_idx,
        test_results.get("inference_time"),
        test_results.get("per_sample_latency"),
        artefact_path,
        artefact_size,
        test_results.get("pre_cal"),
        test_results.get("post_cal"),
        subdirs,
    )
    create_run_summary(
        run_id, best_name, timings, test_results.get("plots", {}), json_path, subdirs
    )

    # Compute additional efficiency metrics
    compute_efficiency_metrics(fitted_models, X_test, combined_json)

    return best_model, fitted_models, combined_json
