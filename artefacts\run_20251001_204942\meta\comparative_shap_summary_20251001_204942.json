{"metadata": {"analysis_type": "comparative_shap", "run_id": "20251001_204942", "timestamp": "2025-10-01T21:24:57.299287+00:00", "models_analysed": [{"name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "cv_score": 0.5304374931378139}, {"name": "XGBoost", "cv_score": 0.5301250556358624}, {"name": "Neural Network", "cv_score": 0.4586868033093763}], "files_validated": true, "reports_found": 3, "models_with_visualisations": 3}, "comparison_notes": {"purpose": "Compare feature importance patterns across top-performing models", "methodology": "SHAP (SHapley Additive exPlanations) analysis on test set samples", "interpretation": "Higher |SHAP| values indicate greater feature importance for predictions", "note": "Only models with successfully generated SHAP files are included"}, "model_reports": ["shap_analysis_report_histgradient_boosting_20251001_204942.json", "shap_analysis_report_xgboost_20251001_204942.json", "shap_analysis_report_neural_network_20251001_204942.json"], "visualisations": {"HistGradient Boosting": ["shap_bar_histgradient_boosting_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample1_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample2_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample3_20251001_204942.png"], "XGBoost": ["shap_bar_xgboost_20251001_204942.png", "shap_waterfall_xgboost_sample1_20251001_204942.png", "shap_waterfall_xgboost_sample2_20251001_204942.png", "shap_waterfall_xgboost_sample3_20251001_204942.png"], "Neural Network": ["shap_bar_neural_network_20251001_204942.png", "shap_waterfall_neural_network_sample1_20251001_204942.png", "shap_waterfall_neural_network_sample2_20251001_204942.png", "shap_waterfall_neural_network_sample3_20251001_204942.png"]}}