# Model selection utilities for comprehensive ML pipeline
import numpy as np
from typing import Dict, List, Tuple, Any


def compute_selection_scores(
    results_by_name: Dict[str, Any],
    timings: Dict[str, Dict[str, float | None]],
    cfg: Dict[str, Any],
) -> Tuple[List[str], np.ndarray, np.ndarray, np.ndarray]:
    """Compute selection scores with optional time penalties and stability gating.

    Returns:
        tuple of (names, cv_means, sel_scores, times)
    """
    sel_cfg = cfg.get("selection", {}) if isinstance(cfg, dict) else {}
    metric_mode = str(sel_cfg.get("metric", "cv")).lower()
    time_weight = float(sel_cfg.get("time_weight", 0.0))
    time_norm = str(sel_cfg.get("time_norm", "min"))
    use_log = bool(sel_cfg.get("use_log", True))

    # Stability penalty config
    stab_cfg = sel_cfg.get("stability", {})
    std_th = float(stab_cfg.get("std_threshold", 0.12))
    pen_w = float(stab_cfg.get("penalty_weight", 0.5))
    pen_mode = str(stab_cfg.get("mode", "linear")).lower()
    gate_unstable = bool(stab_cfg.get("gate", False))
    max_std = float(stab_cfg.get("max_std", 0.25))

    # Build raw scores
    names = list(results_by_name.keys())
    cv_means = np.array(
        [results_by_name[n].get("cv_mean_accuracy") or -np.inf for n in names],
        dtype=float,
    )

    # Time baseline (fit time as proxy; fall back to cv_time when present)
    times = []
    for n in names:
        ft = timings.get(n, {}).get("fit_time_sec")
        ct = timings.get(n, {}).get("cv_time_sec")
        times.append(float(ft if ft is not None else (ct if ct is not None else 0.0)))
    times = np.array(times, dtype=float)

    # Apply time gate if configured
    names, cv_means, times = apply_time_gate(names, cv_means, times, sel_cfg)

    # Compute selection scores
    if metric_mode == "cv_time" and len(times) > 0:
        sel_scores = compute_time_penalised_scores(
            cv_means, times, time_weight, time_norm, use_log
        )
    else:
        sel_scores = cv_means

    # Apply stability penalty / gate
    names, cv_means, sel_scores, times = apply_stability_penalty(
        names,
        cv_means,
        sel_scores,
        times,
        results_by_name,
        std_th,
        pen_w,
        pen_mode,
        gate_unstable,
        max_std,
    )

    return names, cv_means, sel_scores, times


def apply_time_gate(
    names: List[str], cv_means: np.ndarray, times: np.ndarray, sel_cfg: Dict[str, Any]
) -> Tuple[List[str], np.ndarray, np.ndarray]:
    """Apply optional time gate to filter out slow models."""
    gate_mult = float(sel_cfg.get("time_gate_multiple", 0.0))
    if gate_mult > 0 and len(times) > 0:
        base = float(np.nanmin(times[times > 0])) if np.any(times > 0) else None
        if base:
            ok = times <= gate_mult * base
            if not np.all(ok):
                dropped = [names[i] for i, keep in enumerate(ok) if not keep]
                print(
                    f"Gating out slow models (> {gate_mult}x base): {', '.join(dropped)}"
                )
                names = [names[i] for i, keep in enumerate(ok) if keep]
                cv_means = cv_means[ok]
                times = times[ok]
    return names, cv_means, times


def compute_time_penalised_scores(
    cv_means: np.ndarray,
    times: np.ndarray,
    time_weight: float,
    time_norm: str,
    use_log: bool,
) -> np.ndarray:
    """Compute selection scores with time penalties."""
    base = float(np.nanmin(times[times > 0])) if np.any(times > 0) else 1.0
    if time_norm == "median":
        base = float(np.nanmedian(times[times > 0])) if np.any(times > 0) else base

    rel = np.divide(times, base, out=np.ones_like(times), where=base > 0)
    if use_log:
        rel = np.log1p(rel)
    penalties = time_weight * rel
    return cv_means - penalties


def apply_stability_penalty(
    names: List[str],
    cv_means: np.ndarray,
    sel_scores: np.ndarray,
    times: np.ndarray,
    results_by_name: Dict[str, Any],
    std_th: float,
    pen_w: float,
    pen_mode: str,
    gate_unstable: bool,
    max_std: float,
) -> Tuple[List[str], np.ndarray, np.ndarray, np.ndarray]:
    """Apply stability penalty or gate based on CV standard deviation."""
    stds = np.array(
        [results_by_name[n].get("cv_std_accuracy") or 0.0 for n in names], dtype=float
    )

    if gate_unstable:
        ok = stds <= max_std
        names = [names[i] for i, keep in enumerate(ok) if keep]
        cv_means = cv_means[ok]
        sel_scores = sel_scores[ok]
        times = times[ok]
        stds = stds[ok]
    else:
        over = np.maximum(0.0, stds - std_th)
        if pen_mode == "quadratic":
            over = over**2
        sel_scores = sel_scores - pen_w * over

    return names, cv_means, sel_scores, times


def select_best_model(
    names: List[str], sel_scores: np.ndarray, fitted_models: Dict[str, Any]
) -> Tuple[str, Any, int]:
    """Select the best model based on selection scores.

    Returns:
        tuple of (best_name, best_model, best_idx)
    """
    if not names:
        raise RuntimeError("No models available after selection/gating.")

    best_idx = int(np.argmax(sel_scores))
    best_name = names[best_idx]
    best_model = fitted_models[best_name]

    print(f"\nSelected best model: {best_name}")
    return best_name, best_model, best_idx


def should_run_final_test(
    best_name: str, results_by_name: Dict[str, Any], cfg: Dict[str, Any]
) -> bool:
    """Determine whether to run final test evaluation based on CV validity."""
    dft_raw = (cfg.get("evaluation", {}) or {}).get("do_final_test", "auto")

    if isinstance(dft_raw, bool):
        return dft_raw
    elif isinstance(dft_raw, str) and dft_raw.lower() in {"true", "false"}:
        return dft_raw.lower() == "true"
    else:
        # Auto: require valid CV (finite) and at least 2 folds
        cv_ok = (
            np.isfinite(results_by_name[best_name].get("cv_mean_accuracy", np.nan))
            and (results_by_name[best_name].get("cv_used_splits") or 0) >= 2
        )
        return bool(cv_ok)
