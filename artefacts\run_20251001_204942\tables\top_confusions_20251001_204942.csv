actual,pred,count
<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,172
VariableError,<PERSON><PERSON><PERSON><PERSON>,124
NoE<PERSON><PERSON>,TypeErrorArity,92
TypeError<PERSON>rity,No<PERSON><PERSON><PERSON>,59
<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,42
<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,34
SyntaxErrorMissingColon,SyntaxErrorMismatchedParen,33
LogicErrorNegation,NoError,24
NoError,LogicErrorNegation,22
SyntaxErrorMismatchedParen,SyntaxErrorMissingColon,22
VariableError,TypeErrorArity,22
NoError,DataAccessError,13
NoError,RecursionErrorPotential,10
TypeErrorArity,VariableError,9
TypeErrorBadAdd,NoError,9
TypeErrorBadKwarg,NoError,9
RecursionErrorPotential,NoError,8
ArithmeticError,NoError,7
DataAccessError,NoE<PERSON>r,6
LogicErrorNegation,<PERSON><PERSON><PERSON>r<PERSON>rity,5
No<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,5
<PERSON><PERSON><PERSON>r,Type<PERSON><PERSON>r<PERSON>adAdd,5
VariableError,RecursionErrorPotential,5
NoError,TypeErrorBadKwarg,3
VariableError,TypeErrorBadAdd,3
DataAccessError,VariableError,2
LogicErrorComparison,LogicErrorNegation,2
LogicErrorComparison,VariableError,2
LogicErrorNegation,RecursionErrorPotential,2
LogicErrorNegation,VariableError,2
RecursionErrorPotential,TypeErrorArity,2
RecursionErrorPotential,VariableError,2
TypeErrorArity,RecursionErrorPotential,2
TypeErrorBadAdd,TypeErrorArity,2
TypeErrorBadKwarg,TypeErrorArity,2
TypeErrorBadKwarg,VariableError,2
VariableError,DataAccessError,2
VariableError,LogicErrorComparison,2
VariableError,LogicErrorNegation,2
ArithmeticError,TypeErrorArity,1
DataAccessError,LogicErrorComparison,1
DataAccessError,RecursionErrorPotential,1
DataAccessError,TypeErrorArity,1
LogicErrorComparison,TypeErrorArity,1
LogicErrorComparison,TypeErrorBadAdd,1
LogicErrorComparison,TypeErrorBadKwarg,1
LogicErrorNegation,LogicErrorComparison,1
LogicErrorNegation,TypeErrorBadAdd,1
LogicErrorNegation,TypeErrorBadKwarg,1
RecursionErrorPotential,LogicErrorNegation,1
TypeErrorArity,LogicErrorComparison,1
TypeErrorArity,LogicErrorNegation,1
TypeErrorBadAdd,ArithmeticError,1
TypeErrorBadAdd,LogicErrorComparison,1
TypeErrorBadAdd,LogicErrorNegation,1
VariableError,ArithmeticError,1
