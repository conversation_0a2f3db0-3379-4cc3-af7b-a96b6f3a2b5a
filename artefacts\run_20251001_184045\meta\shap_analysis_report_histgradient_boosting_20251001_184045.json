{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251001_184045", "timestamp": "2025-10-01T18:51:04.151055+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_line_count", "mean_abs_shap": 0.04520702416420583, "std_abs_shap": 0.07827838951778227, "max_abs_shap": 0.41299630769504286, "feature_index": 45}, {"rank": 2, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.04145441104147994, "std_abs_shap": 0.08326608450953338, "max_abs_shap": 0.6223403680964623, "feature_index": 49}, {"rank": 3, "feature_name": "feat_return_statements", "mean_abs_shap": 0.030742598288314, "std_abs_shap": 0.08300193598827482, "max_abs_shap": 0.7847455616985772, "feature_index": 58}, {"rank": 4, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.026125711064716048, "std_abs_shap": 0.051317280166889044, "max_abs_shap": 0.2796793957739652, "feature_index": 5}, {"rank": 5, "feature_name": "feat_if_statements", "mean_abs_shap": 0.01823216042120919, "std_abs_shap": 0.04150124617027214, "max_abs_shap": 0.4076675786723876, "feature_index": 31}, {"rank": 6, "feature_name": "feat_colon_count", "mean_abs_shap": 0.0174589032864137, "std_abs_shap": 0.05079142685918424, "max_abs_shap": 0.46825409005232893, "feature_index": 15}, {"rank": 7, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.0160907019332191, "std_abs_shap": 0.03120107361745438, "max_abs_shap": 0.3396521586203683, "feature_index": 0}, {"rank": 8, "feature_name": "feat_assignments", "mean_abs_shap": 0.010342549758531513, "std_abs_shap": 0.02341446941616934, "max_abs_shap": 0.2313599922077938, "feature_index": 2}, {"rank": 9, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.0082208176001016, "std_abs_shap": 0.016045438413584464, "max_abs_shap": 0.16852653506096904, "feature_index": 64}, {"rank": 10, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.007996242027953368, "std_abs_shap": 0.017270711569546893, "max_abs_shap": 0.1906884920775491, "feature_index": 44}, {"rank": 11, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.0068868354840949456, "std_abs_shap": 0.014916023021708679, "max_abs_shap": 0.13532109322036326, "feature_index": 55}, {"rank": 12, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.006857192064308752, "std_abs_shap": 0.023964391494238808, "max_abs_shap": 0.3057703020047233, "feature_index": 34}, {"rank": 13, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.006691503251436256, "std_abs_shap": 0.023645617712166007, "max_abs_shap": 0.31544992233271674, "feature_index": 16}, {"rank": 14, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006603152879316994, "std_abs_shap": 0.01673648611875514, "max_abs_shap": 0.21606247165306172, "feature_index": 63}, {"rank": 15, "feature_name": "feat_function_calls", "mean_abs_shap": 0.006445343255173557, "std_abs_shap": 0.015921035033779975, "max_abs_shap": 0.16310521526856434, "feature_index": 25}, {"rank": 16, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.0052878452622888575, "std_abs_shap": 0.019018816987210512, "max_abs_shap": 0.3169199775833518, "feature_index": 62}, {"rank": 17, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.004829373520440019, "std_abs_shap": 0.016604268014902633, "max_abs_shap": 0.35084917361767887, "feature_index": 19}, {"rank": 18, "feature_name": "feat_keyword_if_count", "mean_abs_shap": 0.004238157139636894, "std_abs_shap": 0.016283525692281992, "max_abs_shap": 0.15144276445559723, "feature_index": 37}, {"rank": 19, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.00357990360598183, "std_abs_shap": 0.014581256749254978, "max_abs_shap": 0.27033802326114115, "feature_index": 3}, {"rank": 20, "feature_name": "feat_char_count", "mean_abs_shap": 0.003566924287653263, "std_abs_shap": 0.015606308534794906, "max_abs_shap": 0.1449175454027145, "feature_index": 13}], "total_importance": 0.3051864762219543, "top_10_importance_ratio": 0.7270018066750216}, "feature_statistics": {"mean_importance": 0.004298401073548652, "median_importance": 0.00035372619673090534, "std_importance": 0.008871506828239398, "max_importance": 0.04520702416420583, "min_importance": 0.0, "importance_concentration": 0.5300428347364898}, "ast_feature_analysis": {"structural_features": [{"name": "feat_tree_depth", "importance": 0.0082208176001016, "rank": 64}, {"name": "feat_leaf_nodes", "importance": 0.007996242027953368, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.0068868354840949456, "rank": 55}, {"name": "feat_total_nodes", "importance": 0.006603152879316994, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.0022147417544766813, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.01823216042120919, "rank": 31}, {"name": "feat_keyword_if_count", "importance": 0.004238157139636894, "rank": 37}, {"name": "feat_for_loops", "importance": 0.0022334187194615043, "rank": 24}, {"name": "feat_keyword_for_count", "importance": 0.0019599570203615, "rank": 36}, {"name": "feat_nested_loops", "importance": 0.00013040551238651697, "rank": 54}, {"name": "feat_string_formatting", "importance": 7.18387607985343e-05, "rank": 61}, {"name": "feat_if_else_chains", "importance": 4.914183556470728e-05, "rank": 30}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_nested_ifs", "importance": 0.0, "rank": 53}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}, {"name": "feat_while_loops", "importance": 0.0, "rank": 68}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.026125711064716048, "rank": 5}, {"name": "feat_cyclomatic_complexity", "importance": 0.004829373520440019, "rank": 19}, {"name": "feat_max_branching_factor", "importance": 0.002133158180287124, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0012274344309288602, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00017822640747151432, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.04145441104147994, "rank": 49}, {"name": "feat_assignments", "importance": 0.010342549758531513, "rank": 2}, {"name": "feat_function_calls", "importance": 0.006445343255173557, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.00357990360598183, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.0019426356598774383, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.001774546128122988, "rank": 4}], "other_features": [{"name": "feat_line_count", "importance": 0.04520702416420583, "rank": 45}, {"name": "feat_return_statements", "importance": 0.030742598288314, "rank": 58}, {"name": "feat_colon_count", "importance": 0.0174589032864137, "rank": 15}, {"name": "feat_arithmetic_ops", "importance": 0.0160907019332191, "rank": 0}, {"name": "feat_keyword_def_count", "importance": 0.006857192064308752, "rank": 34}, {"name": "feat_comparison_ops", "importance": 0.006691503251436256, "rank": 16}, {"name": "feat_subscript_access", "importance": 0.0052878452622888575, "rank": 62}, {"name": "feat_char_count", "importance": 0.003566924287653263, "rank": 13}, {"name": "feat_keyword_return_count", "importance": 0.003053449648072016, "rank": 39}, {"name": "feat_paren_count", "importance": 0.0029830900510210458, "rank": 56}, {"name": "feat_keyword_import_count", "importance": 0.0019877067244518628, "rank": 38}, {"name": "feat_word_count", "importance": 0.0016809895426253187, "rank": 70}, {"name": "feat_max_function_params", "importance": 0.0015723983552454696, "rank": 51}, {"name": "feat_boolean_ops", "importance": 0.0009057808030192921, "rank": 8}, {"name": "feat_import_statements", "importance": 0.0008211354641800949, "rank": 32}, {"name": "feat_equals_count", "importance": 0.0005227772322462764, "rank": 23}, {"name": "feat_bracket_count", "importance": 0.00035372619673090534, "rank": 10}, {"name": "feat_list_comprehensions", "importance": 0.00019361498107528532, "rank": 46}, {"name": "feat_break_statements", "importance": 6.39269536275656e-05, "rank": 11}, {"name": "feat_keyword_class_count", "importance": 6.166391303247749e-05, "rank": 33}, {"name": "feat_class_defs", "importance": 4.7323294762720174e-05, "rank": 14}, {"name": "feat_lambda_functions", "importance": 4.26291591964558e-05, "rank": 42}, {"name": "feat_has_syntax_error", "importance": 3.425793679027419e-05, "rank": 29}, {"name": "feat_generator_expressions", "importance": 2.7912313728096338e-05, "rank": 27}, {"name": "feat_function_defs", "importance": 2.7034575390801556e-05, "rank": 26}, {"name": "feat_list_comps", "importance": 1.8154006442024776e-05, "rank": 47}, {"name": "feat_generator_exps", "importance": 1.3063031912476688e-05, "rank": 28}, {"name": "feat_lambda_usage", "importance": 2.9872921907663734e-06, "rank": 43}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.03192178974594359, "control_flow_features": 0.026915079409418848, "complexity_features": 0.034493903603843565, "syntactic_features": 0.06553938944916726, "other_features": 0.14631631401358097}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251001_184045.png", "shap_bar_histgradient_boosting_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample1_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample2_20251001_184045.png", "shap_waterfall_histgradient_boosting_sample3_20251001_184045.png", "shap_dependence_histgradient_boosting_feat1_20251001_184045.png", "shap_dependence_histgradient_boosting_feat2_20251001_184045.png"]}