{"metadata": {"model_name": "XGBoost", "run_id": "20251001_183629", "timestamp": "2025-10-01T18:39:37.561000+00:00", "n_samples": 40, "n_features": 71, "shap_values_shape": [40, 71, 13], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_return_statements", "mean_abs_shap": 0.022961397694022865, "std_abs_shap": 0.05440628534445324, "max_abs_shap": 0.5141717297212293, "feature_index": 58}, {"rank": 2, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.021906701899397835, "std_abs_shap": 0.04662804885250228, "max_abs_shap": 0.3885111788565684, "feature_index": 49}, {"rank": 3, "feature_name": "feat_char_count", "mean_abs_shap": 0.016313429437734776, "std_abs_shap": 0.03965486837357833, "max_abs_shap": 0.34057848968235116, "feature_index": 13}, {"rank": 4, "feature_name": "feat_line_count", "mean_abs_shap": 0.015036500557959727, "std_abs_shap": 0.03802331438531312, "max_abs_shap": 0.33125947053802507, "feature_index": 45}, {"rank": 5, "feature_name": "feat_if_statements", "mean_abs_shap": 0.012688860636266458, "std_abs_shap": 0.03223574708790115, "max_abs_shap": 0.2837249199834562, "feature_index": 31}, {"rank": 6, "feature_name": "feat_assignments", "mean_abs_shap": 0.012199411986176623, "std_abs_shap": 0.02604446549223858, "max_abs_shap": 0.1908362065678553, "feature_index": 2}, {"rank": 7, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.010434963291139658, "std_abs_shap": 0.03092330979429413, "max_abs_shap": 0.36240841859824874, "feature_index": 3}, {"rank": 8, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.00949616344162389, "std_abs_shap": 0.021879838741709824, "max_abs_shap": 0.1753759575293033, "feature_index": 44}, {"rank": 9, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.008598196958163787, "std_abs_shap": 0.019164772539494015, "max_abs_shap": 0.21235051762413978, "feature_index": 19}, {"rank": 10, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.0070507166601886636, "std_abs_shap": 0.013641649408621137, "max_abs_shap": 0.09121626152952153, "feature_index": 64}, {"rank": 11, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.006667867320684342, "std_abs_shap": 0.020620495628341003, "max_abs_shap": 0.2893394807909165, "feature_index": 62}, {"rank": 12, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006637656614881663, "std_abs_shap": 0.01714565977577609, "max_abs_shap": 0.1129657055397332, "feature_index": 63}, {"rank": 13, "feature_name": "feat_function_calls", "mean_abs_shap": 0.0063316816906026615, "std_abs_shap": 0.012344522206728019, "max_abs_shap": 0.12765019036064493, "feature_index": 25}, {"rank": 14, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.005724933706075545, "std_abs_shap": 0.016156969963563306, "max_abs_shap": 0.17923675851703114, "feature_index": 51}, {"rank": 15, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.005497875320879749, "std_abs_shap": 0.016688423684901466, "max_abs_shap": 0.16000676506532757, "feature_index": 16}, {"rank": 16, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.005110328010321856, "std_abs_shap": 0.010218664504816208, "max_abs_shap": 0.07843803536556435, "feature_index": 5}, {"rank": 17, "feature_name": "feat_colon_count", "mean_abs_shap": 0.0050690542875037855, "std_abs_shap": 0.016681429521482678, "max_abs_shap": 0.159382591042065, "feature_index": 15}, {"rank": 18, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.0050537268390728985, "std_abs_shap": 0.012124637992088587, "max_abs_shap": 0.11277588674166684, "feature_index": 48}, {"rank": 19, "feature_name": "feat_for_loops", "mean_abs_shap": 0.004858678797546025, "std_abs_shap": 0.011774936078237425, "max_abs_shap": 0.09339391888029448, "feature_index": 24}, {"rank": 20, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.004793460464665236, "std_abs_shap": 0.012715274031353462, "max_abs_shap": 0.1030425447368295, "feature_index": 55}], "total_importance": 0.216663644591204, "top_10_importance_ratio": 0.6308688419811775}, "feature_statistics": {"mean_importance": 0.0030516006280451266, "median_importance": 8.885911335692772e-05, "std_importance": 0.00511151150801885, "max_importance": 0.022961397694022865, "min_importance": 0.0, "importance_concentration": 0.4103452168596588}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.00949616344162389, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.0070507166601886636, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.006637656614881663, "rank": 63}, {"name": "feat_nodes_with_children", "importance": 0.004793460464665236, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.0037304417493679923, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.012688860636266458, "rank": 31}, {"name": "feat_for_loops", "importance": 0.004858678797546025, "rank": 24}, {"name": "feat_nested_ifs", "importance": 0.0001539058028561165, "rank": 53}, {"name": "feat_keyword_for_count", "importance": 0.0001294599159773123, "rank": 36}, {"name": "feat_keyword_if_count", "importance": 0.00011093598014647435, "rank": 37}, {"name": "feat_if_else_chains", "importance": 8.495710726603447e-05, "rank": 30}, {"name": "feat_nested_loops", "importance": 3.7718222196569473e-05, "rank": 54}, {"name": "feat_while_loops", "importance": 7.271226334322479e-07, "rank": 68}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_string_formatting", "importance": 0.0, "rank": 61}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_cyclomatic_complexity", "importance": 0.008598196958163787, "rank": 19}, {"name": "feat_avg_branching_factor", "importance": 0.005110328010321856, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.0050537268390728985, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0010774426548176897, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00072593589957678, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.021906701899397835, "rank": 49}, {"name": "feat_assignments", "importance": 0.012199411986176623, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.010434963291139658, "rank": 3}, {"name": "feat_function_calls", "importance": 0.0063316816906026615, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.003306643197277625, "rank": 12}, {"name": "feat_aug_assignments", "importance": 1.2350020506278525e-05, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.022961397694022865, "rank": 58}, {"name": "feat_char_count", "importance": 0.016313429437734776, "rank": 13}, {"name": "feat_line_count", "importance": 0.015036500557959727, "rank": 45}, {"name": "feat_subscript_access", "importance": 0.006667867320684342, "rank": 62}, {"name": "feat_max_function_params", "importance": 0.005724933706075545, "rank": 51}, {"name": "feat_comparison_ops", "importance": 0.005497875320879749, "rank": 16}, {"name": "feat_colon_count", "importance": 0.0050690542875037855, "rank": 15}, {"name": "feat_arithmetic_ops", "importance": 0.00391211475553091, "rank": 0}, {"name": "feat_keyword_def_count", "importance": 0.0038580741370802685, "rank": 34}, {"name": "feat_paren_count", "importance": 0.0021755986756972745, "rank": 56}, {"name": "feat_word_count", "importance": 0.0019149523902093437, "rank": 70}, {"name": "feat_equals_count", "importance": 0.0017951570027744934, "rank": 23}, {"name": "feat_keyword_import_count", "importance": 0.00040096652116494393, "rank": 38}, {"name": "feat_keyword_class_count", "importance": 0.0003968223598907163, "rank": 33}, {"name": "feat_list_comprehensions", "importance": 0.000210733428406139, "rank": 46}, {"name": "feat_bracket_count", "importance": 8.885911335692772e-05, "rank": 10}, {"name": "feat_import_statements", "importance": 6.895869473141308e-05, "rank": 32}, {"name": "feat_list_comps", "importance": 2.1468132858628906e-05, "rank": 47}, {"name": "feat_function_defs", "importance": 1.1301008957043185e-05, "rank": 26}, {"name": "feat_keyword_return_count", "importance": 6.515083015540336e-06, "rank": 39}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_boolean_ops", "importance": 0.0, "rank": 8}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_class_defs", "importance": 0.0, "rank": 14}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_generator_expressions", "importance": 0.0, "rank": 27}, {"name": "feat_generator_exps", "importance": 0.0, "rank": 28}, {"name": "feat_has_syntax_error", "importance": 0.0, "rank": 29}, {"name": "feat_lambda_functions", "importance": 0.0, "rank": 42}, {"name": "feat_lambda_usage", "importance": 0.0, "rank": 43}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.031708438930727445, "control_flow_features": 0.01806524358488842, "complexity_features": 0.02056563036195301, "syntactic_features": 0.05419175208510068, "other_features": 0.09213257962853444}}}, "visualisations_generated": ["shap_summary_xgboost_20251001_183629.png", "shap_bar_xgboost_20251001_183629.png", "shap_waterfall_xgboost_sample1_20251001_183629.png", "shap_waterfall_xgboost_sample2_20251001_183629.png", "shap_waterfall_xgboost_sample3_20251001_183629.png", "shap_dependence_xgboost_feat1_20251001_183629.png", "shap_dependence_xgboost_feat2_20251001_183629.png"]}