{"model": "HistGradientBoostingClassifier", "top_n": 20, "classes": ["ArithmeticError", "DataAccessError", "LogicErrorComparison", "LogicErrorNegation", "NoError", "RecursionErrorPotential", "SyntaxErrorMismatchedParen", "SyntaxErrorMissingColon", "TypeErrorArity", "TypeErrorBadAdd", "TypeErrorBadKwarg", "VariableError"], "global_top_tokens": [{"token": "f4", "mean_abs_shap": 0.013413830075753289}, {"token": "f6", "mean_abs_shap": 0.010261112912146278}, {"token": "f5", "mean_abs_shap": 0.007766920506447343}, {"token": "f8", "mean_abs_shap": 0.0077534628551439826}, {"token": "f7", "mean_abs_shap": 0.007730949232710558}, {"token": "f11", "mean_abs_shap": 0.005932125459798364}, {"token": "f1", "mean_abs_shap": 0.0023676617168130482}, {"token": "f3", "mean_abs_shap": 0.002195572264113629}, {"token": "f2", "mean_abs_shap": 0.0021909283407298835}, {"token": "f0", "mean_abs_shap": 0.001092423784383116}, {"token": "f9", "mean_abs_shap": 0.0008141095092853843}, {"token": "f10", "mean_abs_shap": 0.0002917520969284221}], "per_class_top_tokens": {"ArithmeticError": [{"token": "f4", "mean_abs_shap": 0.02190775854514733}, {"token": "f0", "mean_abs_shap": 0.017919896328601772}, {"token": "f8", "mean_abs_shap": 0.013220853749499115}, {"token": "f11", "mean_abs_shap": 0.011545798913374249}, {"token": "f3", "mean_abs_shap": 0.00939703989162851}, {"token": "f6", "mean_abs_shap": 0.009097694225554162}, {"token": "f9", "mean_abs_shap": 0.005636122650030894}, {"token": "f2", "mean_abs_shap": 0.005104106439583345}, {"token": "f7", "mean_abs_shap": 0.0048499581005725595}, {"token": "f5", "mean_abs_shap": 0.004198830159440687}, {"token": "f1", "mean_abs_shap": 0.001997930137254926}, {"token": "f10", "mean_abs_shap": 0.00041984368664825864}], "DataAccessError": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "LogicErrorComparison": [{"token": "f11", "mean_abs_shap": 0.04799297308310651}, {"token": "f4", "mean_abs_shap": 0.0303462289066285}, {"token": "f8", "mean_abs_shap": 0.013753012161510269}, {"token": "f6", "mean_abs_shap": 0.012368877151127883}, {"token": "f5", "mean_abs_shap": 0.011921027146026117}, {"token": "f2", "mean_abs_shap": 0.004711057316535597}, {"token": "f3", "mean_abs_shap": 0.0035208501620312654}, {"token": "f7", "mean_abs_shap": 0.0034957952355764953}, {"token": "f10", "mean_abs_shap": 0.0022151121864290835}, {"token": "f1", "mean_abs_shap": 0.0018313020074084744}, {"token": "f0", "mean_abs_shap": 0.0009464604863619915}, {"token": "f9", "mean_abs_shap": 0.0008050234141631013}], "LogicErrorNegation": [{"token": "f11", "mean_abs_shap": 0.014633539705940949}, {"token": "f4", "mean_abs_shap": 0.011494717970957757}, {"token": "f8", "mean_abs_shap": 0.007569121196663851}, {"token": "f6", "mean_abs_shap": 0.004424674023965153}, {"token": "f5", "mean_abs_shap": 0.003937545778367045}, {"token": "f2", "mean_abs_shap": 0.003705243960108447}, {"token": "f3", "mean_abs_shap": 0.002773456451578097}, {"token": "f7", "mean_abs_shap": 0.0014352849694386364}, {"token": "f0", "mean_abs_shap": 0.0014070777096207186}, {"token": "f1", "mean_abs_shap": 0.0014040223284709562}, {"token": "f10", "mean_abs_shap": 0.0004363819749900178}, {"token": "f9", "mean_abs_shap": 0.00015949718133644787}], "NoError": [{"token": "f4", "mean_abs_shap": 0.00400858148043892}, {"token": "f8", "mean_abs_shap": 0.0021845092478849026}, {"token": "f11", "mean_abs_shap": 0.0019179421181448819}, {"token": "f5", "mean_abs_shap": 0.0016793759730549152}, {"token": "f9", "mean_abs_shap": 0.0009655289216356542}, {"token": "f2", "mean_abs_shap": 0.000928983099293804}, {"token": "f1", "mean_abs_shap": 0.0007861671372566409}, {"token": "f3", "mean_abs_shap": 0.0005379853684936068}, {"token": "f7", "mean_abs_shap": 0.0003306714788956766}, {"token": "f6", "mean_abs_shap": 0.0003074284667584772}, {"token": "f10", "mean_abs_shap": 0.00016011160212766855}, {"token": "f0", "mean_abs_shap": 3.238626365524848e-05}], "RecursionErrorPotential": [{"token": "f6", "mean_abs_shap": 0.20821982673000045}, {"token": "f4", "mean_abs_shap": 0.18949115576757145}, {"token": "f8", "mean_abs_shap": 0.017006179862619108}, {"token": "f11", "mean_abs_shap": 0.016164151121189203}, {"token": "f7", "mean_abs_shap": 0.008009774149661357}, {"token": "f5", "mean_abs_shap": 0.007233036353421505}, {"token": "f2", "mean_abs_shap": 0.006773335764820913}, {"token": "f3", "mean_abs_shap": 0.004481403849639261}, {"token": "f0", "mean_abs_shap": 0.0036893380585721817}, {"token": "f1", "mean_abs_shap": 0.003224884564388007}, {"token": "f9", "mean_abs_shap": 0.0020438921125619076}, {"token": "f10", "mean_abs_shap": 0.0011163051466087262}], "SyntaxErrorMismatchedParen": [{"token": "f4", "mean_abs_shap": 0.01165654134488841}, {"token": "f5", "mean_abs_shap": 0.008827747138727315}, {"token": "f8", "mean_abs_shap": 0.005645621994460905}, {"token": "f9", "mean_abs_shap": 0.004628003029014128}, {"token": "f11", "mean_abs_shap": 0.004506596501058004}, {"token": "f3", "mean_abs_shap": 0.0033533565329909323}, {"token": "f6", "mean_abs_shap": 0.0032901009074733077}, {"token": "f1", "mean_abs_shap": 0.0030029503879729236}, {"token": "f0", "mean_abs_shap": 0.002846003164408664}, {"token": "f2", "mean_abs_shap": 0.0020934745960537442}, {"token": "f7", "mean_abs_shap": 0.0007101560076823144}, {"token": "f10", "mean_abs_shap": 0.0004318464595589927}], "SyntaxErrorMissingColon": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorArity": [{"token": "f4", "mean_abs_shap": 0.0018064947849454182}, {"token": "f3", "mean_abs_shap": 0.0015207076795735006}, {"token": "f11", "mean_abs_shap": 0.0007201722338570993}, {"token": "f5", "mean_abs_shap": 0.0006669947915242985}, {"token": "f8", "mean_abs_shap": 0.0005246997760462609}, {"token": "f2", "mean_abs_shap": 0.0004561453991325548}, {"token": "f9", "mean_abs_shap": 0.00043911631572507464}, {"token": "f1", "mean_abs_shap": 0.00011032734434362305}, {"token": "f6", "mean_abs_shap": 0.00010668002591254502}, {"token": "f10", "mean_abs_shap": 1.7186607455496692e-05}, {"token": "f7", "mean_abs_shap": 1.5430484730946615e-05}, {"token": "f0", "mean_abs_shap": 9.881768165804985e-06}], "TypeErrorBadAdd": [{"token": "f6", "mean_abs_shap": 0.0008423549964680036}, {"token": "f7", "mean_abs_shap": 0.0006373774893541408}, {"token": "f11", "mean_abs_shap": 0.00016195041744896313}, {"token": "f5", "mean_abs_shap": 1.5271247433082937e-05}, {"token": "f8", "mean_abs_shap": 1.0676256263452005e-05}, {"token": "f1", "mean_abs_shap": 8.49196280782253e-06}, {"token": "f3", "mean_abs_shap": 5.171717805438197e-06}, {"token": "f4", "mean_abs_shap": 3.0746684359775683e-06}, {"token": "f2", "mean_abs_shap": 2.5354861414102716e-06}, {"token": "f10", "mean_abs_shap": 2.1902381196630486e-06}, {"token": "f0", "mean_abs_shap": 2.1362422343776065e-06}, {"token": "f9", "mean_abs_shap": 1.9984974565850593e-06}], "TypeErrorBadKwarg": [{"token": "f6", "mean_abs_shap": 0.005865866539053061}, {"token": "f7", "mean_abs_shap": 0.005703375603750194}, {"token": "f2", "mean_abs_shap": 0.0002750431149571643}, {"token": "f5", "mean_abs_shap": 0.00025017348727295264}, {"token": "f4", "mean_abs_shap": 0.00021296766430539663}, {"token": "f8", "mean_abs_shap": 0.00014977127219939466}, {"token": "f1", "mean_abs_shap": 0.00014144648638594464}, {"token": "f11", "mean_abs_shap": 9.276536526304573e-05}, {"token": "f0", "mean_abs_shap": 9.238068102316303e-05}, {"token": "f3", "mean_abs_shap": 3.291106874555361e-05}, {"token": "f9", "mean_abs_shap": 3.158256194237058e-05}, {"token": "f10", "mean_abs_shap": 9.55718121907672e-06}], "VariableError": [{"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}]}, "n_samples_used": 150}