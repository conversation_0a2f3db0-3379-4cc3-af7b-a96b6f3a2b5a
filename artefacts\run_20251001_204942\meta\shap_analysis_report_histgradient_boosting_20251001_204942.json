{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251001_204942", "timestamp": "2025-10-01T21:23:31.698561+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 12], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.026526130804531124, "std_abs_shap": 0.05530707522631736, "max_abs_shap": 0.42598024791276007, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.020647152805182093, "std_abs_shap": 0.06601391651823423, "max_abs_shap": 0.6881982937331566, "feature_index": 58}, {"rank": 3, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.017790677573830623, "std_abs_shap": 0.06841272311945998, "max_abs_shap": 0.7275453738326637, "feature_index": 5}, {"rank": 4, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.011993243729440417, "std_abs_shap": 0.02507859788804116, "max_abs_shap": 0.1759236786158694, "feature_index": 44}, {"rank": 5, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.011892655203495413, "std_abs_shap": 0.04655420874522487, "max_abs_shap": 0.4913292279645106, "feature_index": 34}, {"rank": 6, "feature_name": "feat_line_count", "mean_abs_shap": 0.011273474941898015, "std_abs_shap": 0.039330525274071854, "max_abs_shap": 0.4429441254074881, "feature_index": 45}, {"rank": 7, "feature_name": "feat_if_statements", "mean_abs_shap": 0.010125911863531782, "std_abs_shap": 0.030753000015623807, "max_abs_shap": 0.3124077168638395, "feature_index": 31}, {"rank": 8, "feature_name": "feat_assignments", "mean_abs_shap": 0.008111534884026016, "std_abs_shap": 0.01838729424882099, "max_abs_shap": 0.18285901409939936, "feature_index": 2}, {"rank": 9, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.007659066059700817, "std_abs_shap": 0.020202928282442696, "max_abs_shap": 0.1687213498076978, "feature_index": 55}, {"rank": 10, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.007622815311717151, "std_abs_shap": 0.01724571668469889, "max_abs_shap": 0.1332390618059978, "feature_index": 48}, {"rank": 11, "feature_name": "feat_function_calls", "mean_abs_shap": 0.007535760130021361, "std_abs_shap": 0.01727617764142366, "max_abs_shap": 0.1784036691576212, "feature_index": 25}, {"rank": 12, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.007272177098990123, "std_abs_shap": 0.017515521237614174, "max_abs_shap": 0.17497209436184644, "feature_index": 64}, {"rank": 13, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.007234794582157926, "std_abs_shap": 0.018519896098869237, "max_abs_shap": 0.1708037700673994, "feature_index": 63}, {"rank": 14, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.005813374769702904, "std_abs_shap": 0.013235502879512084, "max_abs_shap": 0.18068231294099685, "feature_index": 0}, {"rank": 15, "feature_name": "feat_colon_count", "mean_abs_shap": 0.0052350191602995606, "std_abs_shap": 0.023102312206869362, "max_abs_shap": 0.29216991253206315, "feature_index": 15}, {"rank": 16, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.005079875053362354, "std_abs_shap": 0.014991148341489359, "max_abs_shap": 0.1614089108081578, "feature_index": 19}, {"rank": 17, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.004778259559989964, "std_abs_shap": 0.018650694797809265, "max_abs_shap": 0.3277240704118559, "feature_index": 51}, {"rank": 18, "feature_name": "feat_paren_count", "mean_abs_shap": 0.003931698783645844, "std_abs_shap": 0.018612145748352116, "max_abs_shap": 0.2697761221899011, "feature_index": 56}, {"rank": 19, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.003293992963038269, "std_abs_shap": 0.010908839241432875, "max_abs_shap": 0.11031656807298205, "feature_index": 3}, {"rank": 20, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.0032281008870951654, "std_abs_shap": 0.010218484181260843, "max_abs_shap": 0.11401961440145601, "feature_index": 16}], "total_importance": 0.20751468810899196, "top_10_importance_ratio": 0.6440154400403741}, "feature_statistics": {"mean_importance": 0.0029227420860421403, "median_importance": 0.0002630277467669897, "std_importance": 0.005146783065359743, "max_importance": 0.026526130804531124, "min_importance": 0.0, "importance_concentration": 0.42816178905761837}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.011993243729440417, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.007659066059700817, "rank": 55}, {"name": "feat_tree_depth", "importance": 0.007272177098990123, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.007234794582157926, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.002077465449169219, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.010125911863531782, "rank": 31}, {"name": "feat_string_formatting", "importance": 0.0017513551770178377, "rank": 61}, {"name": "feat_for_loops", "importance": 0.0004929410544804519, "rank": 24}, {"name": "feat_if_else_chains", "importance": 0.00038760098165283757, "rank": 30}, {"name": "feat_keyword_for_count", "importance": 0.0002941491692838147, "rank": 36}, {"name": "feat_keyword_if_count", "importance": 0.0002903910167363164, "rank": 37}, {"name": "feat_nested_ifs", "importance": 0.00019258803571534354, "rank": 53}, {"name": "feat_nested_loops", "importance": 0.0001772984049629114, "rank": 54}, {"name": "feat_keyword_while_count", "importance": 6.817464367256268e-05, "rank": 41}, {"name": "feat_while_loops", "importance": 6.482621161332844e-05, "rank": 68}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.017790677573830623, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.007622815311717151, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.005079875053362354, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.002010600396860533, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00018394720878187263, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.026526130804531124, "rank": 49}, {"name": "feat_assignments", "importance": 0.008111534884026016, "rank": 2}, {"name": "feat_function_calls", "importance": 0.007535760130021361, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.003293992963038269, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.0031812675685065506, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.00016321272146591377, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.020647152805182093, "rank": 58}, {"name": "feat_keyword_def_count", "importance": 0.011892655203495413, "rank": 34}, {"name": "feat_line_count", "importance": 0.011273474941898015, "rank": 45}, {"name": "feat_arithmetic_ops", "importance": 0.005813374769702904, "rank": 0}, {"name": "feat_colon_count", "importance": 0.0052350191602995606, "rank": 15}, {"name": "feat_max_function_params", "importance": 0.004778259559989964, "rank": 51}, {"name": "feat_paren_count", "importance": 0.003931698783645844, "rank": 56}, {"name": "feat_comparison_ops", "importance": 0.0032281008870951654, "rank": 16}, {"name": "feat_subscript_access", "importance": 0.002464569850676205, "rank": 62}, {"name": "feat_function_defs", "importance": 0.0016990647490573477, "rank": 26}, {"name": "feat_import_statements", "importance": 0.0014857590672028662, "rank": 32}, {"name": "feat_generator_expressions", "importance": 0.0008592827714408054, "rank": 27}, {"name": "feat_lambda_functions", "importance": 0.0003957839903589212, "rank": 42}, {"name": "feat_list_comprehensions", "importance": 0.00029430142824189075, "rank": 46}, {"name": "feat_equals_count", "importance": 0.00027530386246295177, "rank": 23}, {"name": "feat_keyword_return_count", "importance": 0.0002630277467669897, "rank": 39}, {"name": "feat_raise_statements", "importance": 0.00025723371790881876, "rank": 57}, {"name": "feat_char_count", "importance": 0.00025034630303531665, "rank": 13}, {"name": "feat_boolean_ops", "importance": 0.0001891654083881974, "rank": 8}, {"name": "feat_word_count", "importance": 0.0001191881429850364, "rank": 70}, {"name": "feat_bracket_count", "importance": 0.0001047313113472093, "rank": 10}, {"name": "feat_class_defs", "importance": 9.426516120738909e-05, "rank": 14}, {"name": "feat_decorator_usage", "importance": 7.780832971439775e-05, "rank": 20}, {"name": "feat_brace_count", "importance": 7.204321947219457e-05, "rank": 9}, {"name": "feat_has_syntax_error", "importance": 5.9891879099540694e-05, "rank": 29}, {"name": "feat_generator_exps", "importance": 5.620467789238547e-05, "rank": 28}, {"name": "feat_lambda_usage", "importance": 3.9077459374605384e-05, "rank": 43}, {"name": "feat_list_comps", "importance": 3.475298909881536e-05, "rank": 47}, {"name": "feat_break_statements", "importance": 2.699273018996391e-05, "rank": 11}, {"name": "feat_keyword_import_count", "importance": 1.4359107493693267e-05, "rank": 38}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_keyword_class_count", "importance": 0.0, "rank": 33}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.0362367469194585, "control_flow_features": 0.013845236558667186, "complexity_features": 0.03268791554455253, "syntactic_features": 0.04881189907158923, "other_features": 0.0759328900147245}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251001_204942.png", "shap_bar_histgradient_boosting_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample1_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample2_20251001_204942.png", "shap_waterfall_histgradient_boosting_sample3_20251001_204942.png", "shap_dependence_histgradient_boosting_feat1_20251001_204942.png", "shap_dependence_histgradient_boosting_feat2_20251001_204942.png"]}