{"run_id": "20251001_184045", "dataset_hash": "3d6e4fd0f75314c704e51be2376cf94b3cda38dbe3818ec034dead5887283c01", "experiment": {"grouping": true, "include_no_error": false, "label_space": "grouped", "balance_mode": "errors-only"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.5349750781448003, "Random Forest": 0.5575103489059728, "SVM (RBF)": 0.5106445890005914, "SVM (Linear)": 0.5793885462720472, "Naive Bayes": 0.2237381844123417, "Neural Network": 0.5668759914768194, "Gradient Boosting": 0.6225042944439751, "K-Nearest Neighbors": 0.4362310270057166, "Decision Tree": 0.5300141740118086, "XGBoost": 0.6181159828409977, "HistGradient Boosting": 0.6218800747186318}, "cv_means": {"Logistic Regression": 0.5349750781448003, "Random Forest": 0.5575103489059728, "SVM (RBF)": 0.5106445890005914, "SVM (Linear)": 0.5793885462720472, "Naive Bayes": 0.2237381844123417, "Neural Network": 0.5668759914768194, "Gradient Boosting": 0.6225042944439751, "K-Nearest Neighbors": 0.4362310270057166, "Decision Tree": 0.5300141740118086, "XGBoost": 0.6181159828409977, "HistGradient Boosting": 0.6218800747186318}, "cv_stds": {"Logistic Regression": 0.02171951579627409, "Random Forest": 0.04123265324061215, "SVM (RBF)": 0.01892401549379915, "SVM (Linear)": 0.027947660651132814, "Naive Bayes": 0.022665139496776794, "Neural Network": 0.027527701914555096, "Gradient Boosting": 0.0399106416996313, "K-Nearest Neighbors": 0.02863572216950675, "Decision Tree": 0.039672278474251844, "XGBoost": 0.02730816056289991, "HistGradient Boosting": 0.03757769531188822}, "times_sec": {"Logistic Regression": 0.08832709991838783, "Random Forest": 0.16344560007564723, "SVM (RBF)": 0.3186212999280542, "SVM (Linear)": 0.29156310006510466, "Naive Bayes": 0.005377700086683035, "Neural Network": 0.44593239994719625, "Gradient Boosting": 6.0582835000241175, "K-Nearest Neighbors": 0.003924299962818623, "Decision Tree": 0.012615400017239153, "XGBoost": 1.0978701999410987, "HistGradient Boosting": 0.751793900039047}, "best_model_name": "Grad<PERSON>"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5349750781448003, "cv_std_accuracy": 0.02171951579627409, "cv_fold_scores": [0.550561797752809, 0.5393258426966292, 0.5037593984962406, 0.5655430711610487, 0.5243445692883895, 0.5263157894736842], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.08832709991838783, "cv_time_sec": 1.1221205999609083, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5575103489059728, "cv_std_accuracy": 0.04123265324061215, "cv_fold_scores": [0.5131086142322098, 0.5730337078651685, 0.556390977443609, 0.6179775280898876, 0.5093632958801498, 0.575187969924812], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.16344560007564723, "cv_time_sec": 0.9589232000289485, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5106445890005914, "cv_std_accuracy": 0.01892401549379915, "cv_fold_scores": [0.5093632958801498, 0.5056179775280899, 0.5075187969924813, 0.5093632958801498, 0.4868913857677903, 0.5451127819548872], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.3186212999280542, "cv_time_sec": 1.4313702998915687, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5793885462720472, "cv_std_accuracy": 0.027947660651132814, "cv_fold_scores": [0.5730337078651685, 0.6104868913857678, 0.5676691729323309, 0.5730337078651685, 0.5393258426966292, 0.6127819548872181], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.29156310006510466, "cv_time_sec": 1.3629667999921367, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.2237381844123417, "cv_std_accuracy": 0.022665139496776794, "cv_fold_scores": [0.20224719101123595, 0.2397003745318352, 0.21428571428571427, 0.20973782771535582, 0.26217228464419473, 0.21428571428571427], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.005377700086683035, "cv_time_sec": 0.7504733999958262, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5668759914768194, "cv_std_accuracy": 0.027527701914555096, "cv_fold_scores": [0.5543071161048689, 0.599250936329588, 0.5338345864661654, 0.5617977528089888, 0.550561797752809, 0.6015037593984962], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.44593239994719625, "cv_time_sec": 1.601594000007026, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6225042944439751, "cv_std_accuracy": 0.0399106416996313, "cv_fold_scores": [0.602996254681648, 0.6554307116104869, 0.5827067669172933, 0.6479400749063671, 0.5767790262172284, 0.6691729323308271], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 6.0582835000241175, "cv_time_sec": 5.75340889999643, "best_params": {"classifier__subsample": 1.0, "classifier__n_estimators": 200, "classifier__max_depth": 4, "classifier__learning_rate": 0.1}, "best_cv": 0.6225042944439751, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.4362310270057166, "cv_std_accuracy": 0.02863572216950675, "cv_fold_scores": [0.4232209737827715, 0.46441947565543074, 0.424812030075188, 0.4794007490636704, 0.40823970037453183, 0.41729323308270677], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.003924299962818623, "cv_time_sec": 1.2891361000947654, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5300141740118086, "cv_std_accuracy": 0.039672278474251844, "cv_fold_scores": [0.5280898876404494, 0.5580524344569289, 0.5037593984962406, 0.5430711610486891, 0.4681647940074906, 0.5789473684210527], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.012615400017239153, "cv_time_sec": 0.74271470005624, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6181159828409977, "cv_std_accuracy": 0.02730816056289991, "cv_fold_scores": [0.6104868913857678, 0.6441947565543071, 0.5977443609022557, 0.651685393258427, 0.5805243445692884, 0.6240601503759399], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 1.0978701999410987, "cv_time_sec": 2.3807396000484005, "best_params": {"classifier__base_estimator__subsample": 0.6, "classifier__base_estimator__reg_lambda": 1.0, "classifier__base_estimator__reg_alpha": 1.0, "classifier__base_estimator__n_estimators": 400, "classifier__base_estimator__max_depth": 8, "classifier__base_estimator__learning_rate": 0.1, "classifier__base_estimator__colsample_bytree": 0.6}, "best_cv": 0.6237433471318746, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.6, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=8, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=400, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6218800747186318, "cv_std_accuracy": 0.03757769531188822, "cv_fold_scores": [0.6142322097378277, 0.6741573033707865, 0.6090225563909775, 0.6292134831460674, 0.5617977528089888, 0.6428571428571429], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.751793900039047, "cv_time_sec": 1.3743040999397635, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 0.0, "learning_rate": 0.2, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 31, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.00781260000076145, "per_sample_latency_sec": 3.9063000003807246e-05, "training_time_sec_by_model": {"Logistic Regression": 0.08832709991838783, "Random Forest": 0.16344560007564723, "SVM (RBF)": 0.3186212999280542, "SVM (Linear)": 0.29156310006510466, "Naive Bayes": 0.005377700086683035, "Neural Network": 0.44593239994719625, "Gradient Boosting": 6.0582835000241175, "K-Nearest Neighbors": 0.003924299962818623, "Decision Tree": 0.012615400017239153, "XGBoost": 1.0978701999410987, "HistGradient Boosting": 0.751793900039047}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 800, "n_test": 200}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.0, 0.3333333333333333, 0.38461538461538464, 0.35714285714285715, 0.5, 0.5, 0.78125], "calibration_mean_pred": [0.33590202335462627, 0.4774724943084622, 0.5422133485810698, 0.6539904316943685, 0.7596499106232729, 0.8542075978208432, 0.9799266056478969], "brier_score": 0.24505194494550986, "ece": 0.22486031591918648, "mce": 0.35420759782084354, "brier_reliability": 0.054007300173344874, "brier_resolution": 0.03097728937728938, "brier_uncertainty": 0.22597499999999998, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 0, 1, 6, 13, 14, 14, 24, 128], "bin_acc": [null, null, null, 0.0, 0.3333333333333333, 0.38461538461538464, 0.35714285714285715, 0.5, 0.5, 0.78125], "bin_conf": [null, null, null, 0.33590202335462627, 0.4774724943084622, 0.5422133485810698, 0.6539904316943685, 0.7596499106232729, 0.8542075978208435, 0.9799266056478966], "plot_frac_pos": [0.0, 0.3333333333333333, 0.38461538461538464, 0.35714285714285715, 0.5, 0.5, 0.78125], "plot_mean_pred": [0.33590202335462627, 0.4774724943084622, 0.5422133485810698, 0.6539904316943685, 0.7596499106232729, 0.8542075978208435, 0.9799266056478966]}, "post": {"calibration_frac_pos": [0.2857142857142857, 0.47058823529411764, 0.4444444444444444, 0.6428571428571429, 0.675, 0.8235294117647058, 0.8333333333333334, 1.0], "calibration_mean_pred": [0.27884219119789566, 0.34934352504019417, 0.45116661985861406, 0.547838020414349, 0.6425256637711902, 0.7480257607280968, 0.848720224509107, 0.9439423581461085], "brier_score": 0.18693998734684622, "ece": 0.053674580090428775, "mce": 0.12124471025392347, "brier_reliability": 0.004320670102338785, "brier_resolution": 0.03723566760037349, "brier_uncertainty": 0.222775, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 7, 17, 36, 42, 40, 17, 12, 29], "bin_acc": [null, null, 0.2857142857142857, 0.47058823529411764, 0.4444444444444444, 0.6428571428571429, 0.675, 0.8235294117647058, 0.8333333333333334, 1.0], "bin_conf": [null, null, 0.27884219119789566, 0.34934352504019417, 0.45116661985861384, 0.5478380204143489, 0.6425256637711902, 0.7480257607280968, 0.848720224509107, 0.9439423581461086], "plot_frac_pos": [0.2857142857142857, 0.47058823529411764, 0.4444444444444444, 0.6428571428571429, 0.675, 0.8235294117647058, 0.8333333333333334, 1.0], "plot_mean_pred": [0.27884219119789566, 0.34934352504019417, 0.45116661985861384, 0.5478380204143489, 0.6425256637711902, 0.7480257607280968, 0.848720224509107, 0.9439423581461086]}, "plots": {"pre": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting.png", "post": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting_(pre_vs_post).png"}}, "bootstrap_ci": {"accuracy_ci95": [0.585, 0.715], "macro_f1_ci95": [0.4003985234495874, 0.5678250421804797], "balanced_accuracy_ci95": [0.42101059013769576, 0.5822236509587553]}, "artefact": {"path": "artefacts\\run_20251001_184045\\models\\gradient_boosting_20251001_184045_2025-10-01T18-41-56.778249+00-00.joblib", "size_bytes": 6151709, "size_bytes_by_model": {"Logistic Regression": 28414, "Random Forest": 12012883, "SVM (RBF)": 1106894, "SVM (Linear)": 806110, "Naive Bayes": 36562, "Neural Network": 337471, "Gradient Boosting": 6151709, "K-Nearest Neighbors": 1150617, "Decision Tree": 61595, "XGBoost": 5497173, "HistGradient Boosting": 1213424}}, "best_model_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}, "pr_curves_plot": "artefacts\\run_20251001_184045\\figures\\pr_curves_pr_curves_-_gradient_boosting.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251001_184045\\tables\\per_class_metrics_20251001_184045.csv", "confusions_csv": "artefacts\\run_20251001_184045\\tables\\top_confusions_20251001_184045.csv", "misclassifications_csv": "artefacts\\run_20251001_184045\\tables\\misclassifications_20251001_184045.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251001_184045\\tables\\slices_20251001_184045.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-01T18:41:57.303085+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "argparse": null, "seaborn": "0.13.2"}}}