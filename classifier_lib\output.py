"""Output and visualisation utilities for the classifiers"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
from sklearn.model_selection import StratifiedShuffleSplit
from sklearn.base import clone
from sklearn.metrics import accuracy_score, balanced_accuracy_score, f1_score


def plot_confusion_matrix(
    y_true,
    y_pred,
    class_names,
    title="Confusion Matrix",
    figsize=(10, 8),
    subdirs: dict | None = None,
    run_id: str | None = None,
):
    """
    Creates and displays a confusion matrix with proper formatting.
    Returns the saved image path.
    """
    cm = confusion_matrix(y_true, y_pred, labels=class_names)

    # Create the plot
    plt.figure(figsize=figsize)

    # Use percentage and count annotations
    cm_percent = cm.astype("float") / cm.sum(axis=1)[:, np.newaxis] * 100

    # Create heatmap
    sns.heatmap(
        cm_percent,
        annot=True,
        fmt=".2f",
        cmap="Blues",
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={"label": "Count"},
    )

    plt.title(title)
    plt.xlabel("Predicted Error Type")
    plt.ylabel("Actual Error Type")
    plt.xticks(rotation=45, ha="right")
    plt.yticks(rotation=0)
    plt.tight_layout()

    # Save the confusion matrix
    filename = f'{title.lower().replace(" ", "_")}{run_id}.png'
    if subdirs and "figures" in subdirs:
        out_path = subdirs["figures"] / filename
    else:
        out_path = filename
    plt.savefig(
        out_path,
        dpi=300,
        bbox_inches="tight",
    )
    plt.close()
    print(f"Saved confusion matrix -> {out_path}")

    # Print detailed statistics
    print(f"\n{title} - Detailed Analysis:")
    print("-" * 50)

    for i, class_name in enumerate(class_names):
        if cm[i].sum() > 0:  # Avoid division by zero
            precision = cm[i, i] / cm[:, i].sum() if cm[:, i].sum() > 0 else 0
            recall = cm[i, i] / cm[i].sum()
            print(f"{class_name}:")
            print(f"- True instances: {cm[i].sum()}")
            print(f"- Correctly predicted: {cm[i, i]}")
            print(f"- Precision: {precision:.3f}")
            print(f"- Recall: {recall:.3f}")

    return str(out_path)


def plot_reliability_curve(
    frac_pos,
    mean_pred,
    title="Reliability Curve",
    figsize=(6, 5),
    subdirs: dict | None = None,
):
    """Plot and save a reliability (calibration) curve and return the saved path.

    Args:
        frac_pos: list or array of fraction of positives per bin (y-axis)
        mean_pred: list or array of mean predicted probability per bin (x-axis)
        title: plot title
        figsize: figure size
    """
    try:
        plt.figure(figsize=figsize)
        # Perfect calibration line
        plt.plot([0, 1], [0, 1], linestyle="--", color="gray", label="Perfect")
        # Empirical calibration
        plt.plot(mean_pred, frac_pos, marker="o", label="Model")
        plt.xlabel("Mean predicted probability")
        plt.ylabel("Fraction of positives")
        plt.title(title)
        plt.legend()
        plt.tight_layout()
        filename = f"calibration_{title.lower().replace(' ', '_')}.png"
        out_path = subdirs["figures"] / filename if subdirs else filename
        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved calibration plot -> {out_path}")
        return out_path
    except Exception as e:  # pylint: disable=broad-except
        print(f"WARNING: Failed to plot reliability curve: {e}")
        return None


def plot_reliability_curve_comparison(
    frac_pos_pre,
    mean_pred_pre,
    frac_pos_post,
    mean_pred_post,
    title: str = "Reliability Curve (Pre vs Post Calibration)",
    figsize=(6, 5),
    subdirs=None,
):
    """Plot pre- vs post-calibration reliability curves on one figure and return the saved path.

    Args:
        frac_pos_pre, mean_pred_pre: pre-calibration series
        frac_pos_post, mean_pred_post: post-calibration series
        title: plot title
    """
    try:
        plt.figure(figsize=figsize)
        # Perfect calibration
        plt.plot([0, 1], [0, 1], linestyle="--", color="gray", label="Perfect")
        # Pre
        plt.plot(mean_pred_pre, frac_pos_pre, marker="o", label="Pre-calibration")
        # Post
        plt.plot(mean_pred_post, frac_pos_post, marker="s", label="Post-calibration")
        plt.xlabel("Mean predicted probability")
        plt.ylabel("Fraction of positives")
        plt.title(title)
        plt.legend()
        plt.tight_layout()
        filename = f"calibration_{title.lower().replace(' ', '_')}.png"
        out_path = subdirs["figures"] / filename if subdirs else filename
        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved calibration comparison plot -> {out_path}")
        return out_path
    except Exception as e:  # pylint: disable=broad-except
        print(f"WARNING: Failed to plot calibration comparison: {e}")
        return None


def print_dataset_summary(df):
    """Print summary information about the dataset status."""
    print(f"\n{'='*60}")
    print("DATASET STATUS SUMMARY:")
    print(f"Current size: {len(df)} samples")


def plot_learning_curve_pipeline(
    pipeline,
    X_train,
    y_train,
    X_test,
    y_test,
    *,
    train_sizes=None,
    scoring: str = "accuracy",
    title: str | None = None,
    figsize=(7, 5),
    random_state: int = 25,
    subdirs: dict | None = None,
) -> None:
    """Compute and save a simple learning curve: train/test score vs training size.

    This refits a fresh clone of the provided pipeline for each training size on a
    stratified subsample of the training data, then scores on the subsample (train)
    and the held-out test set (test).

    Args:
        pipeline: fitted Pipeline to clone (hyperparameters reused)
        X_train, y_train: training data used for fitting subsets
        X_test, y_test: held-out test set used for the test curve
        train_sizes: list of fractions (0-1] or absolute integers; default fractions
        scoring: 'accuracy' (default), 'balanced_accuracy', or 'macro_f1'
        title: optional plot title
        figsize: figure size
        random_state: for reproducible subsampling
    """
    try:

        # Prepare sizes
        n_train = len(y_train)
        if train_sizes is None:
            train_sizes = [0.1, 0.2, 0.4, 0.6, 0.8, 1.0]
        # Convert to absolute counts
        abs_sizes = []
        for s in train_sizes:
            if isinstance(s, float):
                abs_sizes.append(max(2, int(np.ceil(s * n_train))))
            else:
                abs_sizes.append(int(s))
        # Deduplicate and clip
        abs_sizes = sorted(set(min(max(2, s), n_train) for s in abs_sizes))

        # Scorer
        def get_mfba_score(y_true, y_pred):
            match scoring:
                case "balanced_accuracy":
                    return balanced_accuracy_score(y_true, y_pred)
                case "macro_f1":
                    return f1_score(y_true, y_pred, average="macro", zero_division=0)
                case _:
                    return accuracy_score(y_true, y_pred)

        train_scores = []
        test_scores = []
        effective_sizes = []

        splitter = StratifiedShuffleSplit(n_splits=1, random_state=random_state)

        # We will sample a new stratified subset for each size to avoid class-drop issues
        for m in abs_sizes:
            # Some splits may fail if a class occurrence < 1 in the subset; retry with smaller test_size logic
            # Use train_size=m on full training set
            try:
                sss = StratifiedShuffleSplit(
                    n_splits=1, train_size=m, random_state=random_state
                )
                idx_train, _ = next(sss.split(np.arange(n_train), y_train))
            except Exception:
                # Fallback: take the first m indices
                idx_train = np.arange(m)
            # Build subset
            X_sub = (
                X_train.iloc[idx_train]
                if hasattr(X_train, "iloc")
                else np.array(X_train)[idx_train]
            )
            y_sub = (
                y_train.iloc[idx_train]
                if hasattr(y_train, "iloc")
                else np.array(y_train)[idx_train]
            )

            # Fit clone
            model = clone(pipeline)
            model.fit(X_sub, y_sub)

            # Train score (on subset)
            y_sub_pred = model.predict(X_sub)
            tr = get_mfba_score(y_sub, y_sub_pred)

            # Test score (on held-out test set)
            y_te_pred = model.predict(X_test)
            te = get_mfba_score(y_test, y_te_pred)

            train_scores.append(tr)
            test_scores.append(te)
            effective_sizes.append(len(y_sub))

        # Plot
        import matplotlib.pyplot as _plt

        _plt.figure(figsize=figsize)
        _plt.plot(effective_sizes, train_scores, marker="o", label="Train")
        _plt.plot(effective_sizes, test_scores, marker="s", label="Test")
        _plt.xlabel("Training samples used")
        metric_name = {
            "accuracy": "Accuracy",
            "balanced_accuracy": "Balanced Accuracy",
            "macro_f1": "Macro-F1",
        }.get(scoring, scoring)
        _plt.ylabel(metric_name)
        ttl = title or f"Learning Curve ({metric_name})"
        _plt.title(ttl)
        _plt.legend()
        _plt.grid(True, linestyle=":", alpha=0.5)
        _plt.tight_layout()
        safe_title = ttl.lower().replace(" ", "_")

        # Use unified output directory structure if subdirs provided
        if subdirs and "figures" in subdirs:
            out_path = subdirs["figures"] / f"{safe_title}.png"
        else:
            # Fallback to root directory if no subdirs provided
            out_path = f"{safe_title}.png"

        _plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # _plt.show()
        print(f"Saved learning curve -> {out_path}")

        # Optional CSV export
        try:
            from classifier_lib.config import get_config as get_cfg

            _cfg = get_cfg()
            save_csv = bool(
                _cfg.get("evaluation", {})
                .get("learning_curve", {})
                .get("save_csv", True)
            )
        except Exception:
            save_csv = True
        if save_csv:
            import csv as _csv

            # Use same directory structure for CSV
            if subdirs and "tables" in subdirs:
                csv_path = subdirs["tables"] / f"learning_curve_{safe_title}.csv"
            else:
                csv_path = f"learning_curve_{safe_title}.csv"

            with open(csv_path, "w", newline="", encoding="utf-8") as f:
                w = _csv.writer(f)
                w.writerow(["train_samples", "train_score", "test_score", "scoring"])
                for n, tr, te in zip(effective_sizes, train_scores, test_scores):
                    w.writerow([n, tr, te, scoring])
            print(f"Saved learning curve CSV -> {csv_path}")
    except Exception as e:  # pylint: disable=broad-except
        print(f"WARNING: Failed to compute learning curve: {e}")


def plot_per_class_pr_curves(
    pr_curves: dict,
    title: str = "Precision-Recall Curves",
    figsize=(7, 5),
    subdirs: dict | None = None,
):
    """Plot and save per-class Precision-Recall curves from a precomputed dict.

    pr_curves should be a dict mapping class label (str) -> {"precision": [...], "recall": [...], "average_precision": float}
    run_id: optional run identifier for unified directory structure
    subdirs: optional subdirectories dict for unified structure
    """
    try:
        if not isinstance(pr_curves, dict) or not pr_curves:
            print("No PR curves to plot.")
            return None
        plt.figure(figsize=figsize)
        for cls, series in pr_curves.items():
            if not isinstance(series, dict):
                continue
            precision = series.get("precision")
            recall = series.get("recall")
            ap = series.get("average_precision")
            if precision is None or recall is None:
                continue
            try:
                lbl = (
                    f"{cls} (AP={ap:.3f})" if isinstance(ap, (int, float)) else str(cls)
                )
            except Exception:
                lbl = str(cls)
            plt.plot(recall, precision, label=lbl)
        plt.xlabel("Recall")
        plt.ylabel("Precision")
        plt.title(title)
        plt.legend(loc="best", fontsize="small", ncol=2)
        plt.grid(True, linestyle=":", alpha=0.4)
        plt.tight_layout()

        # Use unified directory structure if available, otherwise save to root
        filename = f"pr_curves_{title.lower().replace(' ', '_')}.png"
        if subdirs and "figures" in subdirs:
            out_path = subdirs["figures"] / filename
        else:
            out_path = filename

        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved per-class PR curves -> {out_path}")
        return str(out_path)
    except Exception as e:  # pylint: disable=broad-except
        print(f"WARNING: Failed to plot PR curves: {e}")
        return None


def collect_provenance():
    """Collect provenance information for reproducibility."""
    import sys, os, platform
    from datetime import datetime, timezone
    from importlib.metadata import version, PackageNotFoundError

    pkgs = [
        "numpy",
        "scikit-learn",
        "pandas",
        "joblib",
        "shap",
        "xgboost",
        "imbalanced-learn",
        "matplotlib",
        "seaborn",
    ]
    libs = {}
    for p in pkgs:
        try:
            libs[p] = version(p)
        except PackageNotFoundError:
            libs[p] = None

    gpu = None
    try:
        import subprocess  # nosec

        proc = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True,
            timeout=2,
        )
        if proc.returncode == 0:
            gpu = [line.strip() for line in proc.stdout.splitlines() if line.strip()]
    except Exception:
        pass

    return {
        "timestamp_utc": datetime.now(timezone.utc).isoformat(),
        "python_version": sys.version,
        "platform": platform.platform(),
        "os_version": platform.version(),
        "hostname": platform.node(),
        "cpu_count": int(os.cpu_count() or 1),
        "cpu_arch": platform.machine(),
        "gpu": gpu,
        "libraries": libs,
    }


def validity_notes(run_info, cfg, metrics_dir, rid):
    """Generate a markdown-formatted list of threats to validity notes."""

    exp = cfg.get("experiment", {}) if isinstance(cfg, dict) else {}
    eval_cfg = cfg.get("evaluation", {}) if isinstance(cfg, dict) else {}
    dft_flag = run_info.get("do_final_test") if isinstance(run_info, dict) else False
    nested_on = bool(((eval_cfg.get("nested_cv") or {}).get("enabled", False)))
    cal_cfg = (
        (eval_cfg.get("calibration_posthoc") or {})
        if isinstance(eval_cfg, dict)
        else {}
    )
    ss = (run_info.get("seed_sensitivity") or {}) if isinstance(run_info, dict) else {}
    ss_metrics = ss.get("metrics") if isinstance(ss, dict) else None
    doc_path = metrics_dir / f"threats_to_validity_{rid}.md"
    content = []
    content.append(f"# Threats to Validity (Run {rid})\n")
    content.append(
        "This section summarises potential threats to the validity of the reported results and the steps taken to mitigate them."
    )
    # Internal validity
    content.append("\n## Internal validity")
    content.append(
        "- Data leakage: The split is performed on original samples before any balancing or feature construction; test data are never used for model selection. Nested cross-validation is available for unbiased performance estimation."
        if nested_on
        else "- Data leakage: The split is performed on original samples before balancing and feature construction; model selection uses only training folds. Nested CV is available but may be disabled for runtime reasons."
    )
    # Final test gating
    content.append(
        "- Final test gating: Final test evaluation is only performed when cross-validation is valid or when explicitly enabled. This prevents optimistic leakage into the test set."
        if dft_flag
        else "A final test evaluation was not performed due to invalid CV scores or user override; thus, no information from the test set was used during model selection."
    )
    # Post-hoc Calibration
    content.append(
        f"- Calibration: Post-hoc calibration using {cal_cfg.get('method', 'isotonic')} regression was applied using {cal_cfg.get('cv', 5)}-fold CV on the training set to improve probability estimates."
        if bool(cal_cfg.get("enabled", False))
        else "- Calibration: No post-hoc calibration was applied; predicted probabilities may be miscalibrated."
    )

    # Construct validity
    content.append("\n## Construct validity")
    content.append(
        f"- Label space and grouping: Grouping of error types is {'enabled' if exp.get('grouping', True) else 'disabled'}; this trades finer granularity for improved sample efficiency. An ablation without grouping is available via CLI and reported separately."
    )
    content.append(
        f"- Class balance: Inclusion of synthetic/structural 'NoError' samples is {'enabled' if exp.get('include_no_error', True) else 'disabled'}; this can affect decision thresholds. An ablation without NoError augmentation is available."
    )
    # External validity
    content.append("\n## External validity")
    content.append(
        "- Dataset representativeness: The dataset originates from controlled generations and may not capture the full variability of real-world code errors. Distribution shift to new sources, languages, or styles may degrade performance."
    )
    content.append(
        "- Generalisation risk: Token and structural features may overfit to dataset-specific artefacts; future work should validate on independent corpora."
    )
    # Statistical conclusion validity
    content.append("\n## Statistical conclusion validity")
    if isinstance(ss_metrics, dict):
        sc = (
            ss_metrics.get("selection_score", {})
            if isinstance(ss_metrics, dict)
            else {}
        )
        top1 = ss_metrics.get("top1_stability")
        content.append(
            f"- Randomness sensitivity: Seed study indicates selection-score mean={sc.get('mean')} (n={sc.get('n')}), with top-1 model stability={top1}. This quantifies variability across seeds."
        )
    else:
        content.append(
            "- Randomness sensitivity: A seed sensitivity study can be enabled to quantify variability across random initialisations."
        )
    content.append(
        "- Cross-validation: K-fold CV is used for model selection; optional nested CV provides an unbiased outer estimate. Bootstrap confidence intervals are computed for key test metrics when the final test is run."
    )
    # Calibration validity
    content.append("\n## Calibration validity")
    content.append(
        "- Probability calibration: Pre- and optional post-hoc calibration (isotonic or sigmoid) are evaluated via Brier score and reliability diagrams. Miscalibration can affect decision-making and downstream tooling."
    )
    # Implementation validity
    content.append("\n## Implementation and reproducibility")
    content.append(
        "- Reproducibility: Random states are centrally controlled via configuration and CLI. Runs are logged with configuration, seeds, and artefacts packaged for provenance."
    )
    content.append(
        "- Complexity and efficiency: Training/inference times and parameter counts are recorded per model to contextualise performance vs cost."
    )
    # Residual risks
    content.append("\n## Residual risks")
    content.append(
        "- Preprocessing assumptions (e.g., tokenisation, AST parsing) may introduce biases. Edge cases in code formatting or unparsable snippets can affect feature extraction and downstream predictions."
    )
    content.append(
        "- Class boundary ambiguity (e.g., SyntaxError vs ValueError) can limit ceiling performance; confusion analysis highlights these cases."
    )
    with doc_path.open("w", encoding="utf-8") as f:
        f.write("\n".join(content) + "\n")
    return doc_path

