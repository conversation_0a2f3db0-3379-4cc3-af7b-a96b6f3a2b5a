{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251001_183629", "timestamp": "2025-10-01T18:40:02.843472+00:00", "n_samples": 40, "n_features": 71, "shap_values_shape": [40, 71, 13], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.028405969697143253, "std_abs_shap": 0.057528868719886724, "max_abs_shap": 0.42556032922767906, "feature_index": 49}, {"rank": 2, "feature_name": "feat_assignments", "mean_abs_shap": 0.02179138922969715, "std_abs_shap": 0.03565166847294945, "max_abs_shap": 0.24870461324010018, "feature_index": 2}, {"rank": 3, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02168639669909554, "std_abs_shap": 0.04722121096927187, "max_abs_shap": 0.4176312654081236, "feature_index": 58}, {"rank": 4, "feature_name": "feat_if_statements", "mean_abs_shap": 0.01708230115813491, "std_abs_shap": 0.04012373331669448, "max_abs_shap": 0.3455101085621551, "feature_index": 31}, {"rank": 5, "feature_name": "feat_char_count", "mean_abs_shap": 0.014730075132639488, "std_abs_shap": 0.03305054137172052, "max_abs_shap": 0.2539449203529891, "feature_index": 13}, {"rank": 6, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.013988046692072409, "std_abs_shap": 0.03464193164202434, "max_abs_shap": 0.19748200343244077, "feature_index": 5}, {"rank": 7, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.013349282823623574, "std_abs_shap": 0.037201590986353485, "max_abs_shap": 0.4398366879576125, "feature_index": 3}, {"rank": 8, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.013321609033519059, "std_abs_shap": 0.024197292038089232, "max_abs_shap": 0.17345967037044424, "feature_index": 64}, {"rank": 9, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.012370773039476816, "std_abs_shap": 0.03391747076265325, "max_abs_shap": 0.5015327657248624, "feature_index": 62}, {"rank": 10, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.010336578366981958, "std_abs_shap": 0.024467598757369508, "max_abs_shap": 0.18088789534859284, "feature_index": 63}, {"rank": 11, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.008249926784159822, "std_abs_shap": 0.0229833428721442, "max_abs_shap": 0.22346892064990287, "feature_index": 52}, {"rank": 12, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.00797767224433697, "std_abs_shap": 0.022418782640761113, "max_abs_shap": 0.2857589510398338, "feature_index": 0}, {"rank": 13, "feature_name": "feat_function_calls", "mean_abs_shap": 0.007791504919919823, "std_abs_shap": 0.01972587231669279, "max_abs_shap": 0.24064354735675816, "feature_index": 25}, {"rank": 14, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.006996461957115644, "std_abs_shap": 0.022133832103185234, "max_abs_shap": 0.2757969508822976, "feature_index": 19}, {"rank": 15, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.006323173801933283, "std_abs_shap": 0.012600689020450915, "max_abs_shap": 0.08182646818716569, "feature_index": 55}, {"rank": 16, "feature_name": "feat_line_count", "mean_abs_shap": 0.00606456481825102, "std_abs_shap": 0.020779726732610266, "max_abs_shap": 0.12886313650380554, "feature_index": 45}, {"rank": 17, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.006011004820919068, "std_abs_shap": 0.015311064667834738, "max_abs_shap": 0.16166128378787226, "feature_index": 12}, {"rank": 18, "feature_name": "feat_word_count", "mean_abs_shap": 0.0057567971626656615, "std_abs_shap": 0.016973657413583962, "max_abs_shap": 0.11021093956991915, "feature_index": 70}, {"rank": 19, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.0057083199141010925, "std_abs_shap": 0.012409027987850589, "max_abs_shap": 0.11768840351898173, "feature_index": 44}, {"rank": 20, "feature_name": "feat_for_loops", "mean_abs_shap": 0.005451184410097117, "std_abs_shap": 0.014181775577736758, "max_abs_shap": 0.1196053241528969, "feature_index": 24}], "total_importance": 0.25244256271134885, "top_10_importance_ratio": 0.6617838928509406}, "feature_statistics": {"mean_importance": 0.003555529052272519, "median_importance": 2.6812611854495702e-05, "std_importance": 0.006156940532320046, "max_importance": 0.028405969697143253, "min_importance": 0.0, "importance_concentration": 0.4107711900995076}, "ast_feature_analysis": {"structural_features": [{"name": "feat_tree_depth", "importance": 0.013321609033519059, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.010336578366981958, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.008249926784159822, "rank": 52}, {"name": "feat_nodes_with_children", "importance": 0.006323173801933283, "rank": 55}, {"name": "feat_leaf_nodes", "importance": 0.0057083199141010925, "rank": 44}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.01708230115813491, "rank": 31}, {"name": "feat_for_loops", "importance": 0.005451184410097117, "rank": 24}, {"name": "feat_nested_ifs", "importance": 0.00022954885895101954, "rank": 53}, {"name": "feat_nested_loops", "importance": 9.212465611059787e-05, "rank": 54}, {"name": "feat_keyword_if_count", "importance": 5.1637484745835154e-05, "rank": 37}, {"name": "feat_if_else_chains", "importance": 2.4283066106134443e-05, "rank": 30}, {"name": "feat_while_loops", "importance": 2.135598227667245e-05, "rank": 68}, {"name": "feat_keyword_for_count", "importance": 5.859325283157038e-06, "rank": 36}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_string_formatting", "importance": 0.0, "rank": 61}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.013988046692072409, "rank": 5}, {"name": "feat_cyclomatic_complexity", "importance": 0.006996461957115644, "rank": 19}, {"name": "feat_max_branching_factor", "importance": 0.003982920000573989, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0007356061692050174, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 1.1834794823178647e-05, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.028405969697143253, "rank": 49}, {"name": "feat_assignments", "importance": 0.02179138922969715, "rank": 2}, {"name": "feat_attribute_access", "importance": 0.013349282823623574, "rank": 3}, {"name": "feat_function_calls", "importance": 0.007791504919919823, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.006011004820919068, "rank": 12}, {"name": "feat_aug_assignments", "importance": 3.077308357341768e-05, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.02168639669909554, "rank": 58}, {"name": "feat_char_count", "importance": 0.014730075132639488, "rank": 13}, {"name": "feat_subscript_access", "importance": 0.012370773039476816, "rank": 62}, {"name": "feat_arithmetic_ops", "importance": 0.00797767224433697, "rank": 0}, {"name": "feat_line_count", "importance": 0.00606456481825102, "rank": 45}, {"name": "feat_word_count", "importance": 0.0057567971626656615, "rank": 70}, {"name": "feat_max_function_params", "importance": 0.0053408600662053765, "rank": 51}, {"name": "feat_comparison_ops", "importance": 0.003805432356904457, "rank": 16}, {"name": "feat_keyword_def_count", "importance": 0.0022319350558617002, "rank": 34}, {"name": "feat_paren_count", "importance": 0.0015783095515731448, "rank": 56}, {"name": "feat_colon_count", "importance": 0.0005592558637356718, "rank": 15}, {"name": "feat_keyword_class_count", "importance": 5.890777968050335e-05, "rank": 33}, {"name": "feat_generator_exps", "importance": 3.373320175683906e-05, "rank": 28}, {"name": "feat_has_syntax_error", "importance": 3.311459165836228e-05, "rank": 29}, {"name": "feat_function_defs", "importance": 3.264322895532434e-05, "rank": 26}, {"name": "feat_class_defs", "importance": 2.6812611854495702e-05, "rank": 14}, {"name": "feat_import_statements", "importance": 2.5904559371242147e-05, "rank": 32}, {"name": "feat_generator_expressions", "importance": 2.587346979669732e-05, "rank": 27}, {"name": "feat_list_comps", "importance": 2.4441423482330642e-05, "rank": 47}, {"name": "feat_equals_count", "importance": 1.8841464526002494e-05, "rank": 23}, {"name": "feat_keyword_import_count", "importance": 1.7439562012776663e-05, "rank": 38}, {"name": "feat_list_comprehensions", "importance": 1.675883765528259e-05, "rank": 46}, {"name": "feat_keyword_return_count", "importance": 1.6591252868573036e-05, "rank": 39}, {"name": "feat_bracket_count", "importance": 1.168518664499542e-05, "rank": 10}, {"name": "feat_brace_count", "importance": 5.04651927239126e-06, "rank": 9}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_boolean_ops", "importance": 0.0, "rank": 8}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_lambda_functions", "importance": 0.0, "rank": 42}, {"name": "feat_lambda_usage", "importance": 0.0, "rank": 43}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.04393960790069521, "control_flow_features": 0.022958294941705443, "complexity_features": 0.02571486961379024, "syntactic_features": 0.07737992457487629, "other_features": 0.08244986568028166}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251001_183629.png", "shap_bar_histgradient_boosting_20251001_183629.png", "shap_waterfall_histgradient_boosting_sample1_20251001_183629.png", "shap_waterfall_histgradient_boosting_sample2_20251001_183629.png", "shap_waterfall_histgradient_boosting_sample3_20251001_183629.png", "shap_dependence_histgradient_boosting_feat1_20251001_183629.png", "shap_dependence_histgradient_boosting_feat2_20251001_183629.png"]}