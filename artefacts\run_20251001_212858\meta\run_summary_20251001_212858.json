{"run_id": "20251001_212858", "best_model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "training_time_sec_by_model": {"Logistic Regression": 4.207513800007291, "Random Forest": 0.31202399998437613, "SVM (RBF)": 41.45704189990647, "SVM (Linear)": 80.8248960999772, "Naive Bayes": 0.04727310000453144, "Neural Network": 9.199878699961118, "Gradient Boosting": 15.845452000037767, "K-Nearest Neighbors": 0.027828400023281574, "Decision Tree": 0.11187829996924847, "XGBoost": 1.2086716999765486, "HistGradient Boosting": 3.1321556998882443}, "calibration_plots": {"pre": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting.png", "post": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_212858\\figures\\calibration_reliability_curve_-_histgradient_boosting_(pre_vs_post).png"}, "metrics_json": "artefacts\\run_20251001_212858\\metrics\\metrics_20251001_212858.json"}