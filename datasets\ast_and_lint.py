"""AST and Lint Analysis for Python Code Snippets"""

import ast
import tempfile
import subprocess
import json
import sys
from typing import List, Dict, Any

# --- Internal Helper Functions ---
# These functions perform the checks and are called by the main analyse_code function.


def check_syntax(code_snippet: str) -> Dict[str, Any] | None:
    """Check the code snippet for syntax errors.

    Args:
        code_snippet (str): The code snippet to check.

    Returns:
        Dict[str, Any] | None: A dictionary containing error details or None if no error occurred.
    """
    try:
        ast.parse(code_snippet)
        return None
    except SyntaxError as e:
        return {
            "type": "SyntaxError",
            "message": e.msg,
            "line": e.lineno,
            "offset": e.offset,
        }


def lint_with_pyflakes(code_snippet: str) -> List[Dict[str, Any]]:
    """Lint the code snippet using pyflakes.

    Args:
        code_snippet (str): The code snippet to lint.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries containing error details.
    """
    errors = []
    with tempfile.NamedTemporaryFile("w", suffix=".py", delete=True) as tmp:
        tmp.write(code_snippet)
        tmp.flush()
        process = subprocess.run(
            ["pyflakes", tmp.name], capture_output=True, text=True, check=False
        )
    if process.stdout:
        for line in process.stdout.strip().split("\n"):
            if not line:
                continue
            parts = line.split(":")
            try:
                errors.append(
                    {
                        "type": "LinterError",
                        "message": ":".join(parts[2:]).strip(),
                        "line": int(parts[1]),
                        "offset": None,
                    }
                )
            except (ValueError, IndexError):
                errors.append(
                    {
                        "type": "LinterError",
                        "message": line,
                        "line": None,
                        "offset": None,
                    }
                )
    return errors


def type_check_with_mypy(code_snippet: str) -> List[Dict[str, Any]]:
    """Check the code snippet for type-related errors using mypy.

    Args:
        code_snippet (str): The code snippet to check.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries containing error details.
    """
    errors = []
    with tempfile.NamedTemporaryFile("w", suffix=".py", delete=True) as tmp:
        tmp.write(code_snippet)
        tmp.flush()
        process = subprocess.run(
            ["mypy", tmp.name, "--ignore-missing-imports", "--check-untyped-defs"],
            capture_output=True,
            text=True,
            check=False,
        )
    if process.stdout:
        for line in process.stdout.strip().split("\n"):
            if not line or "Success:" in line or "Found" in line or "note:" in line:
                continue
            parts = line.split(":")
            try:
                errors.append(
                    {
                        "type": "TypeError",
                        "message": ":".join(parts[3:]).strip(),
                        "line": int(parts[1]),
                        "offset": int(parts[2]) if parts[2].isdigit() else None,
                    }
                )
            except (ValueError, IndexError):
                errors.append(
                    {"type": "TypeError", "message": line, "line": None, "offset": None}
                )
    return errors


def execute_with_timeout(code_snippet: str, timeout: int = 1) -> Dict[str, Any] | None:
    """Execute a code snippet with a timeout.

    Args:
        code_snippet (str): The code snippet to execute.
        timeout (int, optional): The timeout duration in seconds. Defaults to 1.

    Returns:
        Dict[str, Any] | None: A dictionary containing error details or None if no error occurred.
    """
    executor_script = """
import sys
import traceback
import json

try:
    exec(sys.stdin.read())
except Exception as e:
    tb = traceback.extract_tb(e.__traceback__)
    last_call = tb[-1] if tb else None
    error_details = {
        'type': type(e).__name__,
        'message': str(e),
        'line': last_call.lineno if last_call else None
    }
    print(json.dumps(error_details), file=sys.stderr)
    sys.exit(1)
"""
    try:
        process = subprocess.run(
            [sys.executable, "-c", executor_script],
            input=code_snippet,
            capture_output=True,
            text=True,
            timeout=timeout,
            check=False,
        )
        if process.returncode != 0:
            try:
                # The error message is a string representation of a dictionary
                error_details = json.loads(process.stderr.strip())
                return {
                    "type": f"RuntimeError_{error_details.get('type', 'Unknown')}",
                    "message": error_details.get("message"),
                    "line": error_details.get("line"),
                    "offset": None,
                }
            except (json.JSONDecodeError, KeyError):
                return {
                    "type": "RuntimeError_Unknown",
                    "message": process.stderr.strip(),
                    "line": None,
                    "offset": None,
                }
        return None  # No runtime error
    except subprocess.TimeoutExpired:
        return {
            "type": "RuntimeError_Timeout",
            "message": f"Code execution exceeded the {timeout} second timeout.",
            "line": None,
            "offset": None,
        }
    except (subprocess.SubprocessError, OSError, ValueError) as e:
        return {
            "type": "RuntimeError_ExecutionFailed",
            "message": str(e),
            "line": None,
            "offset": None,
        }


# --- Main Public Function ---


def analyse_code(code_snippet: str) -> Dict[str, Any]:
    """
    Analyses a Python code snippet using a 4-stage pipeline and returns the first error found.

    The pipeline runs in the following order:
    1. AST Syntax Check (for SyntaxError)
    2. Pyflakes Lint Check (for NameError, etc.)
    3. MyPy Type Check (for TypeError)
    4. Runtime Execution Check (for IndexError, infinite loops, etc.)

    Args:
        code_snippet: The string of Python code to analyse.

    Returns:
        A dictionary in a standardised format detailing the result.
    """
    # Stage 1: AST Syntax Check
    syntax_error = check_syntax(code_snippet)
    if syntax_error:
        return {
            "is_valid_syntax": False,
            "error_type": syntax_error.get("type"),
            "error_description": syntax_error.get("message"),
            "line_number": syntax_error.get("line"),
            "offset": syntax_error.get("offset"),
        }

    # Stage 2: Pyflakes Linter Check
    linter_errors = lint_with_pyflakes(code_snippet)
    if linter_errors:
        first_error = linter_errors[0]
        return {
            "is_valid_syntax": True,
            "error_type": first_error.get("type"),
            "error_description": first_error.get("message"),
            "line_number": first_error.get("line"),
            "offset": first_error.get("offset"),
        }

    # Stage 3: MyPy Type Check
    type_errors = type_check_with_mypy(code_snippet)
    if type_errors:
        first_error = type_errors[0]
        return {
            "is_valid_syntax": True,
            "error_type": first_error.get("type"),
            "error_description": first_error.get("message"),
            "line_number": first_error.get("line"),
            "offset": first_error.get("offset"),
        }

    # Stage 4: Runtime Execution Check
    runtime_error = execute_with_timeout(code_snippet)
    if runtime_error:
        return {
            "is_valid_syntax": False,
            "error_type": runtime_error.get("type"),
            "error_description": runtime_error.get("message"),
            "line_number": runtime_error.get("line"),
            "offset": runtime_error.get("offset"),
        }

    # If no errors are found in any stage
    return {
        "is_valid_syntax": True,
        "error_type": None,
        "error_description": None,
        "line_number": None,
        "offset": None,
    }


if __name__ == "__main__":
    # Example
    TYPE_ERROR = 'def sum_numbers(*args):\n    return sum(args) + "extra"'
    NAME_ERROR = "a = b + 1"
    SYNTAX_ERROR = "print('hello'"
    CORRECT_CODE = "def add(a, b):\n    return a + b"
    RUNTIME_ERROR = """
def write_string_to_file(filename, content):
    with open(filename, 'w') as file:
    file.write(content + 1)
    """

    INFINITE_LOOP = "while True:\n    pass"

    print("--- 1. Testing Code with SyntaxError ---")
    result = analyse_code(SYNTAX_ERROR)
    print(json.dumps(result, indent=4))
    print("-" * 40)

    print("\n--- 2. Testing Code with NameError (Linter) ---")
    result = analyse_code(NAME_ERROR)
    print(json.dumps(result, indent=4))
    print("-" * 40)

    print("\n--- 3. Testing Code with TypeError (MyPy) ---")
    result = analyse_code(TYPE_ERROR)
    print(json.dumps(result, indent=4))
    print("-" * 40)

    print("\n--- 4. Testing Correct Code ---")
    result = analyse_code(CORRECT_CODE)
    print(json.dumps(result, indent=4))
    print("-" * 40)

    print("\n--- 5. Testing Code with RuntimeError (IndexError) ---")
    result = analyse_code(RUNTIME_ERROR)
    print(json.dumps(result, indent=4))
    print("-" * 40)

    print("\n--- 6. Testing Code with RuntimeError (Timeout) ---")
    result = analyse_code(INFINITE_LOOP)
    print(json.dumps(result, indent=4))
    print("-" * 40)
