{"run_id": "20251001_190703", "best_model_name": "XGBoost", "training_time_sec_by_model": {"Logistic Regression": 0.7656964999623597, "Random Forest": 0.2528924000216648, "SVM (RBF)": 6.539970200043172, "SVM (Linear)": 12.218516499968246, "Naive Bayes": 0.020519800018519163, "Neural Network": 3.092238700017333, "Gradient Boosting": 29.850695200031623, "K-Nearest Neighbors": 0.01273890002630651, "Decision Tree": 0.05283250007778406, "XGBoost": 2.573655700078234, "HistGradient Boosting": 2.7341056000441313}, "calibration_plots": {"pre": "artefacts\\run_20251001_190703\\figures\\calibration_reliability_curve_-_xgboost.png", "post": null, "comparison": null}, "metrics_json": "artefacts\\run_20251001_190703\\metrics\\metrics_20251001_190703.json"}