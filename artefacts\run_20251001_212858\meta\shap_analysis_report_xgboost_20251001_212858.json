{"metadata": {"model_name": "XGBoost", "run_id": "20251001_212858", "timestamp": "2025-10-01T21:45:22.432203+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.03636989384614475, "std_abs_shap": 0.06301653303778917, "max_abs_shap": 0.49655592903263596, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.02621044821513923, "std_abs_shap": 0.06677252251837823, "max_abs_shap": 0.7295849447756061, "feature_index": 58}, {"rank": 3, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.02241875238004734, "std_abs_shap": 0.03150162623172574, "max_abs_shap": 0.1809041752644059, "feature_index": 44}, {"rank": 4, "feature_name": "feat_assignments", "mean_abs_shap": 0.021941790091390412, "std_abs_shap": 0.03963746760921068, "max_abs_shap": 0.27403161368678075, "feature_index": 2}, {"rank": 5, "feature_name": "feat_paren_count", "mean_abs_shap": 0.019919161300739542, "std_abs_shap": 0.04644393191951558, "max_abs_shap": 0.35334654675152877, "feature_index": 56}, {"rank": 6, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.01360291538141952, "std_abs_shap": 0.03436077877209423, "max_abs_shap": 0.3514046704877183, "feature_index": 16}, {"rank": 7, "feature_name": "feat_colon_count", "mean_abs_shap": 0.013178239424559713, "std_abs_shap": 0.04087565615374026, "max_abs_shap": 0.3124722350538191, "feature_index": 15}, {"rank": 8, "feature_name": "feat_line_count", "mean_abs_shap": 0.012457905279462035, "std_abs_shap": 0.03653229754414696, "max_abs_shap": 0.30288772456255014, "feature_index": 45}, {"rank": 9, "feature_name": "feat_if_statements", "mean_abs_shap": 0.01209881424892419, "std_abs_shap": 0.031178552842566678, "max_abs_shap": 0.2909589424226667, "feature_index": 31}, {"rank": 10, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.009936303192457748, "std_abs_shap": 0.03327415280873106, "max_abs_shap": 0.3840369714822408, "feature_index": 34}, {"rank": 11, "feature_name": "feat_function_calls", "mean_abs_shap": 0.009487632522470563, "std_abs_shap": 0.0206761790696944, "max_abs_shap": 0.17914475690884718, "feature_index": 25}, {"rank": 12, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.008408170874924977, "std_abs_shap": 0.015106859005871607, "max_abs_shap": 0.12213667551003538, "feature_index": 55}, {"rank": 13, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.008116719678324407, "std_abs_shap": 0.019722048684969535, "max_abs_shap": 0.2517004801448805, "feature_index": 64}, {"rank": 14, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.007179264232487488, "std_abs_shap": 0.01891426570637144, "max_abs_shap": 0.21731181975960825, "feature_index": 3}, {"rank": 15, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006582704350064173, "std_abs_shap": 0.016747341186790034, "max_abs_shap": 0.15550437304541787, "feature_index": 63}, {"rank": 16, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.006084172998253948, "std_abs_shap": 0.015328363875272175, "max_abs_shap": 0.16313491488558093, "feature_index": 12}, {"rank": 17, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.005679645497022918, "std_abs_shap": 0.01404463151785941, "max_abs_shap": 0.13717710987311937, "feature_index": 48}, {"rank": 18, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.005634880272201791, "std_abs_shap": 0.01447507886304942, "max_abs_shap": 0.177095071007501, "feature_index": 0}, {"rank": 19, "feature_name": "feat_char_count", "mean_abs_shap": 0.005351110985286814, "std_abs_shap": 0.018680187580854393, "max_abs_shap": 0.19781934533686527, "feature_index": 13}, {"rank": 20, "feature_name": "feat_max_function_params", "mean_abs_shap": 0.004961428710246037, "std_abs_shap": 0.015756388753674872, "max_abs_shap": 0.2552418309530737, "feature_index": 51}], "total_importance": 0.2863557560956736, "top_10_importance_ratio": 0.6569947324454252}, "feature_statistics": {"mean_importance": 0.004033179663319346, "median_importance": 0.0005990395783107981, "std_importance": 0.0070633584063499325, "max_importance": 0.03636989384614475, "min_importance": 0.0, "importance_concentration": 0.4430155257332295}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.02241875238004734, "rank": 44}, {"name": "feat_nodes_with_children", "importance": 0.008408170874924977, "rank": 55}, {"name": "feat_tree_depth", "importance": 0.008116719678324407, "rank": 64}, {"name": "feat_total_nodes", "importance": 0.006582704350064173, "rank": 63}, {"name": "feat_max_nesting_depth", "importance": 0.002436363788122322, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.01209881424892419, "rank": 31}, {"name": "feat_keyword_if_count", "importance": 0.0018218501299917399, "rank": 37}, {"name": "feat_for_loops", "importance": 0.0013151363616414787, "rank": 24}, {"name": "feat_keyword_for_count", "importance": 0.000983653888982207, "rank": 36}, {"name": "feat_keyword_while_count", "importance": 0.00023731468652334726, "rank": 41}, {"name": "feat_if_else_chains", "importance": 0.00021235751598933312, "rank": 30}, {"name": "feat_while_loops", "importance": 7.58261329352578e-05, "rank": 68}, {"name": "feat_nested_ifs", "importance": 4.2203456928231365e-05, "rank": 53}, {"name": "feat_nested_loops", "importance": 1.470200026152711e-05, "rank": 54}, {"name": "feat_string_formatting", "importance": 9.308882000715857e-06, "rank": 61}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}], "complexity_features": [{"name": "feat_max_branching_factor", "importance": 0.005679645497022918, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.003784984718837486, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.0009345205135696037, "rank": 6}, {"name": "feat_avg_branching_factor", "importance": 0.0008161136578862891, "rank": 5}, {"name": "feat_max_function_complexity", "importance": 0.0004420547660630178, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.03636989384614475, "rank": 49}, {"name": "feat_assignments", "importance": 0.021941790091390412, "rank": 2}, {"name": "feat_function_calls", "importance": 0.009487632522470563, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.007179264232487488, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.006084172998253948, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0005422122674165209, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.02621044821513923, "rank": 58}, {"name": "feat_paren_count", "importance": 0.019919161300739542, "rank": 56}, {"name": "feat_comparison_ops", "importance": 0.01360291538141952, "rank": 16}, {"name": "feat_colon_count", "importance": 0.013178239424559713, "rank": 15}, {"name": "feat_line_count", "importance": 0.012457905279462035, "rank": 45}, {"name": "feat_keyword_def_count", "importance": 0.009936303192457748, "rank": 34}, {"name": "feat_arithmetic_ops", "importance": 0.005634880272201791, "rank": 0}, {"name": "feat_char_count", "importance": 0.005351110985286814, "rank": 13}, {"name": "feat_max_function_params", "importance": 0.004961428710246037, "rank": 51}, {"name": "feat_subscript_access", "importance": 0.0049334985680455695, "rank": 62}, {"name": "feat_word_count", "importance": 0.002527840415004134, "rank": 70}, {"name": "feat_import_statements", "importance": 0.002346200260269745, "rank": 32}, {"name": "feat_function_defs", "importance": 0.0019360703643797252, "rank": 26}, {"name": "feat_lambda_functions", "importance": 0.0011798106716756, "rank": 42}, {"name": "feat_equals_count", "importance": 0.0007315378078617179, "rank": 23}, {"name": "feat_class_defs", "importance": 0.0006441408773936639, "rank": 14}, {"name": "feat_bracket_count", "importance": 0.0006355269157923888, "rank": 10}, {"name": "feat_keyword_import_count", "importance": 0.0005990395783107981, "rank": 38}, {"name": "feat_keyword_class_count", "importance": 0.00034197325967286196, "rank": 33}, {"name": "feat_boolean_ops", "importance": 0.0003073511449226729, "rank": 8}, {"name": "feat_brace_count", "importance": 0.00022127011129629518, "rank": 9}, {"name": "feat_keyword_return_count", "importance": 0.00020614643404934927, "rank": 39}, {"name": "feat_context_managers", "importance": 0.0001586581292385497, "rank": 17}, {"name": "feat_generator_expressions", "importance": 0.00012955912872635646, "rank": 27}, {"name": "feat_list_comprehensions", "importance": 5.064992337099505e-05, "rank": 46}, {"name": "feat_has_syntax_error", "importance": 3.393529857733702e-05, "rank": 29}, {"name": "feat_lambda_usage", "importance": 2.524312007535128e-05, "rank": 43}, {"name": "feat_generator_exps", "importance": 2.5034342305728367e-05, "rank": 28}, {"name": "feat_list_comps", "importance": 2.330295884580656e-05, "rank": 47}, {"name": "feat_with_statements", "importance": 1.0410537142248537e-05, "rank": 69}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.04796271107148322, "control_flow_features": 0.01681116730417803, "complexity_features": 0.011657319153379316, "syntactic_features": 0.08160496595816369, "other_features": 0.1283195926084693}}}, "visualisations_generated": ["shap_summary_xgboost_20251001_212858.png", "shap_bar_xgboost_20251001_212858.png", "shap_waterfall_xgboost_sample1_20251001_212858.png", "shap_waterfall_xgboost_sample2_20251001_212858.png", "shap_waterfall_xgboost_sample3_20251001_212858.png", "shap_dependence_xgboost_feat1_20251001_212858.png", "shap_dependence_xgboost_feat2_20251001_212858.png"]}