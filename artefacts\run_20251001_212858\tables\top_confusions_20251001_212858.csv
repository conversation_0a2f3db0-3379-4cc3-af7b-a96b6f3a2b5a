actual,pred,count
Variable<PERSON>rror,TypeErrorArity,85
SyntaxErrorMissingColon,SyntaxErrorMismatchedParen,81
TypeErrorArity,VariableError,57
SyntaxErrorMismatchedParen,SyntaxErrorMissingColon,44
VariableError,LogicErrorComparison,33
LogicErrorComparison,VariableError,27
LogicErrorNegation,VariableError,24
VariableError,LogicErrorNegation,22
TypeErrorArity,LogicErrorComparison,20
LogicErrorNegation,LogicErrorComparison,17
LogicErrorComparison,LogicErrorNegation,13
LogicErrorComparison,TypeErrorArity,13
TypeErrorArity,LogicErrorNegation,11
TypeErrorBadKwarg,TypeErrorArity,11
TypeErrorBadKwarg,VariableError,11
VariableError,DataAccessError,11
DataAccessError,VariableError,10
VariableError,TypeErrorBadKwarg,10
VariableError,RecursionErrorPotential,9
TypeErrorBadAdd,LogicErrorComparison,8
VariableError,ArithmeticError,8
ArithmeticError,VariableError,7
RecursionErrorPotential,VariableError,7
TypeErrorArity,TypeErrorBadKwarg,6
TypeErrorBadAdd,VariableError,6
ArithmeticError,TypeErrorArity,5
DataAccessError,TypeErrorArity,5
LogicErrorComparison,TypeErrorBadAdd,5
RecursionErrorPotential,TypeErrorArity,5
TypeErrorArity,TypeErrorBadAdd,5
DataAccessError,LogicErrorComparison,4
LogicErrorNegation,TypeErrorArity,4
RecursionErrorPotential,LogicErrorNegation,4
TypeErrorBadAdd,TypeErrorArity,4
DataAccessError,TypeErrorBadKwarg,3
LogicErrorComparison,RecursionErrorPotential,3
RecursionErrorPotential,LogicErrorComparison,3
RecursionErrorPotential,TypeErrorBadAdd,3
TypeErrorArity,ArithmeticError,3
TypeErrorBadAdd,LogicErrorNegation,3
LogicErrorComparison,DataAccessError,2
LogicErrorNegation,TypeErrorBadAdd,2
SyntaxErrorMismatchedParen,VariableError,2
TypeErrorArity,RecursionErrorPotential,2
TypeErrorBadAdd,ArithmeticError,2
TypeErrorBadKwarg,LogicErrorComparison,2
VariableError,TypeErrorBadAdd,2
ArithmeticError,LogicErrorComparison,1
ArithmeticError,LogicErrorNegation,1
ArithmeticError,TypeErrorBadAdd,1
DataAccessError,ArithmeticError,1
DataAccessError,LogicErrorNegation,1
LogicErrorComparison,TypeErrorBadKwarg,1
LogicErrorNegation,DataAccessError,1
LogicErrorNegation,RecursionErrorPotential,1
RecursionErrorPotential,DataAccessError,1
SyntaxErrorMismatchedParen,TypeErrorArity,1
TypeErrorArity,DataAccessError,1
TypeErrorBadKwarg,LogicErrorNegation,1
TypeErrorBadKwarg,SyntaxErrorMismatchedParen,1
