train_samples,train_score,test_score,scoring
400,0.9966666666666667,0.31537779367775187,balanced_accuracy
800,0.9960416666666667,0.36073291364195487,balanced_accuracy
1200,0.9938775510204082,0.3495709467774019,balanced_accuracy
1600,0.9920663265306122,0.36922801461429594,balanced_accuracy
2000,0.988181590233105,0.369917465169266,balanced_accuracy
2400,0.9872518486916951,0.36923094286510966,balanced_accuracy
2800,0.9846021303258147,0.37473192787889786,balanced_accuracy
3200,0.9837133705972976,0.38417225583279585,balanced_accuracy
3600,0.9800277804363934,0.3848107535092269,balanced_accuracy
4000,0.9804500533826195,0.39461391184179084,balanced_accuracy
4400,0.9752203495105015,0.412394969651441,balanced_accuracy
4800,0.972977468557668,0.40555004287158763,balanced_accuracy
5200,0.9719128201239275,0.4191351144337481,balanced_accuracy
5600,0.9710000429865066,0.4216020504562306,balanced_accuracy
6000,0.9690982366383557,0.42058763562577806,balanced_accuracy
6400,0.9669309836433859,0.4475157363755513,balanced_accuracy
6800,0.9649798916681105,0.4388466349876332,balanced_accuracy
7200,0.9643440860635297,0.4430885251290726,balanced_accuracy
7600,0.9612176437304737,0.4373002150434997,balanced_accuracy
8000,0.9597414756985394,0.4334052292137848,balanced_accuracy
