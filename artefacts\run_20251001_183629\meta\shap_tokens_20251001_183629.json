{"model": "GradientBoostingClassifier", "top_n": 20, "classes": ["AttributeError", "KeyError", "LogicErrorComparison", "LogicErrorNegation", "NameError", "RecursionErrorPotential", "SyntaxErrorMismatchedParen", "SyntaxErrorMissingColon", "TypeErrorArity", "TypeErrorBadAdd", "TypeErrorBadKwarg", "UnboundLocalError", "ZeroDivisionError"], "global_top_tokens": [{"token": "f4", "mean_abs_shap": 0.01239537586139252}, {"token": "f8", "mean_abs_shap": 0.010992543801032845}, {"token": "f7", "mean_abs_shap": 0.010344390275843805}, {"token": "f6", "mean_abs_shap": 0.00748686528354152}, {"token": "f5", "mean_abs_shap": 0.0073717639147265115}, {"token": "f3", "mean_abs_shap": 0.006614206571601596}, {"token": "f0", "mean_abs_shap": 0.004736793850608524}, {"token": "f11", "mean_abs_shap": 0.004576391147592561}, {"token": "f12", "mean_abs_shap": 0.0035898060029522327}, {"token": "f2", "mean_abs_shap": 0.0033113098934152707}, {"token": "f1", "mean_abs_shap": 0.0017922625365062333}, {"token": "f9", "mean_abs_shap": 7.379298586740044e-06}, {"token": "f10", "mean_abs_shap": 1.3046887480771374e-06}], "per_class_top_tokens": {"AttributeError": [{"token": "f4", "mean_abs_shap": 0.029422346371383617}, {"token": "f12", "mean_abs_shap": 0.02176300594728008}, {"token": "f8", "mean_abs_shap": 0.012584505085522496}, {"token": "f11", "mean_abs_shap": 0.009454005437480564}, {"token": "f7", "mean_abs_shap": 0.008578288256466006}, {"token": "f6", "mean_abs_shap": 0.0050791117966946025}, {"token": "f5", "mean_abs_shap": 0.004196381098792805}, {"token": "f1", "mean_abs_shap": 0.0033687612194312168}, {"token": "f3", "mean_abs_shap": 0.0029196794641498302}, {"token": "f0", "mean_abs_shap": 0.0016146259445762462}, {"token": "f2", "mean_abs_shap": 0.0009814286158047435}, {"token": "f9", "mean_abs_shap": 3.417843857891638e-06}, {"token": "f10", "mean_abs_shap": 6.049104492482192e-07}], "KeyError": [{"token": "f12", "mean_abs_shap": 0.0}, {"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "LogicErrorComparison": [{"token": "f4", "mean_abs_shap": 0.16515992000527835}, {"token": "f7", "mean_abs_shap": 0.06281639990875963}, {"token": "f8", "mean_abs_shap": 0.03385898086284449}, {"token": "f6", "mean_abs_shap": 0.021943954666981604}, {"token": "f0", "mean_abs_shap": 0.015281517175753304}, {"token": "f3", "mean_abs_shap": 0.015190738553996903}, {"token": "f11", "mean_abs_shap": 0.014063026700725734}, {"token": "f5", "mean_abs_shap": 0.006496478247435803}, {"token": "f2", "mean_abs_shap": 0.004528351508132246}, {"token": "f12", "mean_abs_shap": 0.0037553279674774564}, {"token": "f1", "mean_abs_shap": 0.00367750998738343}, {"token": "f9", "mean_abs_shap": 8.222415830072538e-05}, {"token": "f10", "mean_abs_shap": 1.4489486322022556e-05}], "LogicErrorNegation": [{"token": "f0", "mean_abs_shap": 0.08287822285740767}, {"token": "f4", "mean_abs_shap": 0.029570987711368724}, {"token": "f3", "mean_abs_shap": 0.027395756748519812}, {"token": "f7", "mean_abs_shap": 0.0231565207430335}, {"token": "f8", "mean_abs_shap": 0.016574335475253978}, {"token": "f6", "mean_abs_shap": 0.012659464939958157}, {"token": "f5", "mean_abs_shap": 0.011565382485587415}, {"token": "f2", "mean_abs_shap": 0.009173077624222419}, {"token": "f11", "mean_abs_shap": 0.004912735784927809}, {"token": "f12", "mean_abs_shap": 0.0010109596983667109}, {"token": "f1", "mean_abs_shap": 0.0005661315411132672}, {"token": "f9", "mean_abs_shap": 9.961952685649502e-06}, {"token": "f10", "mean_abs_shap": 1.7762617977454888e-06}], "NameError": [{"token": "f3", "mean_abs_shap": 0.005248769402557655}, {"token": "f4", "mean_abs_shap": 0.002242516113166613}, {"token": "f11", "mean_abs_shap": 0.0022196659654773005}, {"token": "f5", "mean_abs_shap": 0.0008578876289986792}, {"token": "f8", "mean_abs_shap": 0.0007640503665892511}, {"token": "f1", "mean_abs_shap": 0.0003533463927437227}, {"token": "f7", "mean_abs_shap": 0.00020337154168223172}, {"token": "f0", "mean_abs_shap": 0.00015105254766963923}, {"token": "f2", "mean_abs_shap": 0.0001443235100986388}, {"token": "f6", "mean_abs_shap": 4.9769644248943206e-05}, {"token": "f12", "mean_abs_shap": 9.588564258930938e-06}, {"token": "f9", "mean_abs_shap": 2.9043147759276178e-06}, {"token": "f10", "mean_abs_shap": 5.151335122948283e-07}], "RecursionErrorPotential": [{"token": "f2", "mean_abs_shap": 0.00021695424210772663}, {"token": "f5", "mean_abs_shap": 0.00019906048838119807}, {"token": "f4", "mean_abs_shap": 3.2169292830175665e-05}, {"token": "f3", "mean_abs_shap": 2.6922093035746673e-05}, {"token": "f8", "mean_abs_shap": 1.769736076405521e-05}, {"token": "f11", "mean_abs_shap": 1.599485254264146e-05}, {"token": "f6", "mean_abs_shap": 8.375935902528201e-07}, {"token": "f12", "mean_abs_shap": 8.285894048348436e-07}, {"token": "f7", "mean_abs_shap": 5.629663048885774e-07}, {"token": "f0", "mean_abs_shap": 1.401055357011337e-07}, {"token": "f1", "mean_abs_shap": 1.1742360834149837e-08}, {"token": "f9", "mean_abs_shap": 5.926766092405158e-09}, {"token": "f10", "mean_abs_shap": 5.964497669590828e-10}], "SyntaxErrorMismatchedParen": [{"token": "f0", "mean_abs_shap": 0.01153935792762894}, {"token": "f3", "mean_abs_shap": 0.010790541430960962}, {"token": "f8", "mean_abs_shap": 0.008230130923344708}, {"token": "f4", "mean_abs_shap": 0.005780389174418952}, {"token": "f5", "mean_abs_shap": 0.0032585071480308835}, {"token": "f7", "mean_abs_shap": 0.0016460887368487565}, {"token": "f2", "mean_abs_shap": 0.0015316490034013564}, {"token": "f11", "mean_abs_shap": 0.0012721571929344623}, {"token": "f6", "mean_abs_shap": 0.0009925085058426245}, {"token": "f12", "mean_abs_shap": 0.0004664373121674722}, {"token": "f1", "mean_abs_shap": 0.00022672281717638082}, {"token": "f9", "mean_abs_shap": 1.3596943374228388e-06}, {"token": "f10", "mean_abs_shap": 2.4163990553832275e-07}], "SyntaxErrorMissingColon": [{"token": "f12", "mean_abs_shap": 0.0}, {"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorArity": [{"token": "f12", "mean_abs_shap": 0.0}, {"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "TypeErrorBadAdd": [{"token": "f7", "mean_abs_shap": 0.027541754395161266}, {"token": "f6", "mean_abs_shap": 0.02266123138384897}, {"token": "f4", "mean_abs_shap": 0.0037483047051153377}, {"token": "f8", "mean_abs_shap": 0.0025266409932581868}, {"token": "f5", "mean_abs_shap": 0.0008918134438251939}, {"token": "f3", "mean_abs_shap": 0.000890741611245921}, {"token": "f11", "mean_abs_shap": 0.0005505993609579365}, {"token": "f0", "mean_abs_shap": 0.0005385247055399671}, {"token": "f12", "mean_abs_shap": 0.0005173608329637075}, {"token": "f2", "mean_abs_shap": 0.0005004226553121721}, {"token": "f1", "mean_abs_shap": 5.0520411655953e-05}, {"token": "f9", "mean_abs_shap": 1.37059939143795e-07}, {"token": "f10", "mean_abs_shap": 2.3909505044530952e-08}], "TypeErrorBadKwarg": [{"token": "f7", "mean_abs_shap": 0.005170369097010097}, {"token": "f4", "mean_abs_shap": 0.0037826152646025487}, {"token": "f6", "mean_abs_shap": 0.003274306432156089}, {"token": "f0", "mean_abs_shap": 0.0007386379029530735}, {"token": "f8", "mean_abs_shap": 0.0004790308509483576}, {"token": "f11", "mean_abs_shap": 0.00035897440727896806}, {"token": "f12", "mean_abs_shap": 0.000250336437685641}, {"token": "f3", "mean_abs_shap": 0.00015913997540998988}, {"token": "f1", "mean_abs_shap": 0.0001414097680668504}, {"token": "f5", "mean_abs_shap": 2.7228720400410178e-05}, {"token": "f2", "mean_abs_shap": 1.418099269649458e-05}, {"token": "f9", "mean_abs_shap": 1.947321555566553e-06}, {"token": "f10", "mean_abs_shap": 3.454747322059807e-07}], "UnboundLocalError": [{"token": "f12", "mean_abs_shap": 0.0}, {"token": "f11", "mean_abs_shap": 0.0}, {"token": "f10", "mean_abs_shap": 0.0}, {"token": "f9", "mean_abs_shap": 0.0}, {"token": "f8", "mean_abs_shap": 0.0}, {"token": "f7", "mean_abs_shap": 0.0}, {"token": "f6", "mean_abs_shap": 0.0}, {"token": "f5", "mean_abs_shap": 0.0}, {"token": "f4", "mean_abs_shap": 0.0}, {"token": "f3", "mean_abs_shap": 0.0}, {"token": "f2", "mean_abs_shap": 0.0}, {"token": "f1", "mean_abs_shap": 0.0}, {"token": "f0", "mean_abs_shap": 0.0}], "ZeroDivisionError": [{"token": "f5", "mean_abs_shap": 0.028975182676234557}, {"token": "f4", "mean_abs_shap": 0.016213206834172923}, {"token": "f8", "mean_abs_shap": 0.011237913878657416}, {"token": "f3", "mean_abs_shap": 0.008878116408772138}, {"token": "f2", "mean_abs_shap": 0.007943775326412331}, {"token": "f7", "mean_abs_shap": 0.006014734659567329}, {"token": "f0", "mean_abs_shap": 0.0037936261883108066}, {"token": "f11", "mean_abs_shap": 0.0028504785890262653}, {"token": "f6", "mean_abs_shap": 0.002050504283534675}, {"token": "f1", "mean_abs_shap": 0.0009046912192012746}, {"token": "f12", "mean_abs_shap": 0.0007172664007654926}, {"token": "f9", "mean_abs_shap": 3.915078087003748e-06}, {"token": "f10", "mean_abs_shap": 6.752929115734931e-07}]}, "n_samples_used": 40}