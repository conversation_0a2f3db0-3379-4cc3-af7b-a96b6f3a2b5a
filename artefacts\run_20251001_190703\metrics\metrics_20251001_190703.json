{"run_id": "20251001_190703", "dataset_hash": "7bf80d629753fa70bd8cddab72b6a8732bbe26d608a729a7dd86926f8f2a973c", "experiment": {"grouping": true, "include_no_error": false, "label_space": "grouped", "balance_mode": "errors-only"}, "selection": {"metric": "cv", "scores": {"Logistic Regression": 0.5496234440919076, "Random Forest": 0.6435013925895267, "SVM (RBF)": 0.5638786008346165, "SVM (Linear)": 0.597749699793764, "Naive Bayes": 0.21250083885289164, "Neural Network": 0.6521255501281618, "Gradient Boosting": 0.6806248976037113, "K-Nearest Neighbors": 0.532252628374485, "Decision Tree": 0.5535007252562766, "XGBoost": 0.6941256491034302, "HistGradient Boosting": 0.6878753046582485}, "cv_means": {"Logistic Regression": 0.5496234440919076, "Random Forest": 0.6435013925895267, "SVM (RBF)": 0.5638786008346165, "SVM (Linear)": 0.597749699793764, "Naive Bayes": 0.21250083885289164, "Neural Network": 0.6521255501281618, "Gradient Boosting": 0.6806248976037113, "K-Nearest Neighbors": 0.532252628374485, "Decision Tree": 0.5535007252562766, "XGBoost": 0.6941256491034302, "HistGradient Boosting": 0.6878753046582485}, "cv_stds": {"Logistic Regression": 0.012897804078407463, "Random Forest": 0.010830862916216481, "SVM (RBF)": 0.015791968353144302, "SVM (Linear)": 0.008742259059037041, "Naive Bayes": 0.005482160414726985, "Neural Network": 0.010611219858852198, "Gradient Boosting": 0.01155510213554357, "K-Nearest Neighbors": 0.01758863149971614, "Decision Tree": 0.023712848576974574, "XGBoost": 0.006098404035577034, "HistGradient Boosting": 0.010053587879741263}, "times_sec": {"Logistic Regression": 0.7656964999623597, "Random Forest": 0.2528924000216648, "SVM (RBF)": 6.539970200043172, "SVM (Linear)": 12.218516499968246, "Naive Bayes": 0.020519800018519163, "Neural Network": 3.092238700017333, "Gradient Boosting": 29.850695200031623, "K-Nearest Neighbors": 0.01273890002630651, "Decision Tree": 0.05283250007778406, "XGBoost": 2.573655700078234, "HistGradient Boosting": 2.7341056000441313}, "best_model_name": "XGBoost"}, "cv_details": {"Logistic Regression": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5496234440919076, "cv_std_accuracy": 0.012897804078407463, "cv_fold_scores": [0.5539730134932533, 0.5641410352588146, 0.5386346586646662, 0.5577211394302849, 0.5296324081020255, 0.5536384096024006], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.7656964999623597, "cv_time_sec": 2.191951100016013, "classifier_params": {"C": 0.1, "class_weight": "balanced", "dual": false, "fit_intercept": true, "intercept_scaling": 1, "l1_ratio": null, "max_iter": 2000, "multi_class": "auto", "n_jobs": null, "penalty": "l2", "random_state": 25, "solver": "liblinear", "tol": 0.0001, "verbose": 0, "warm_start": false}}, "Random Forest": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6435013925895267, "cv_std_accuracy": 0.010830862916216481, "cv_fold_scores": [0.643928035982009, 0.6384096024006002, 0.6519129782445612, 0.6319340329835083, 0.6346586646661665, 0.660165041260315], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.2528924000216648, "cv_time_sec": 1.251541699981317, "classifier_params": {"bootstrap": true, "ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 15, "max_features": "sqrt", "max_leaf_nodes": null, "max_samples": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 2, "min_samples_split": 5, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "n_estimators": 200, "n_jobs": 21, "oob_score": false, "random_state": 25, "verbose": 0, "warm_start": false}}, "SVM (RBF)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5638786008346165, "cv_std_accuracy": 0.015791968353144302, "cv_fold_scores": [0.5359820089955023, 0.5611402850712678, 0.5836459114778695, 0.5629685157421289, 0.5701425356339085, 0.5693923480870218], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 6.539970200043172, "cv_time_sec": 4.88854169996921, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "rbf", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "SVM (Linear)": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.597749699793764, "cv_std_accuracy": 0.008742259059037041, "cv_fold_scores": [0.5877061469265368, 0.6001500375093773, 0.6046511627906976, 0.6101949025487257, 0.5903975993998499, 0.5933983495873969], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 12.218516499968246, "cv_time_sec": 7.417042899993248, "classifier_params": {"C": 1.0, "break_ties": false, "cache_size": 200, "class_weight": "balanced", "coef0": 0.0, "decision_function_shape": "ovr", "degree": 3, "gamma": "scale", "kernel": "linear", "max_iter": -1, "probability": true, "random_state": 25, "shrinking": true, "tol": 0.001, "verbose": false}}, "Naive Bayes": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.21250083885289164, "cv_std_accuracy": 0.005482160414726985, "cv_fold_scores": [0.20839580209895053, 0.20705176294073518, 0.2115528882220555, 0.2098950524737631, 0.2213053263315829, 0.21680420105026257], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.020519800018519163, "cv_time_sec": 0.8648251999402419, "classifier_params": {"priors": null, "var_smoothing": 1e-09}}, "Neural Network": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6521255501281618, "cv_std_accuracy": 0.010611219858852198, "cv_fold_scores": [0.643928035982009, 0.6466616654163541, 0.6669167291822956, 0.6559220389805097, 0.660165041260315, 0.6391597899474869], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 3.092238700017333, "cv_time_sec": 4.1233754000859335, "classifier_params": {"activation": "relu", "alpha": 0.01, "batch_size": "auto", "beta_1": 0.9, "beta_2": 0.999, "early_stopping": true, "epsilon": 1e-08, "hidden_layer_sizes": [100, 50], "learning_rate": "constant", "learning_rate_init": 0.001, "max_fun": 15000, "max_iter": 300, "momentum": 0.9, "n_iter_no_change": 10, "nesterovs_momentum": true, "power_t": 0.5, "random_state": 25, "shuffle": true, "solver": "adam", "tol": 0.0001, "validation_fraction": 0.2, "verbose": false, "warm_start": false}}, "Gradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6806248976037113, "cv_std_accuracy": 0.01155510213554357, "cv_fold_scores": [0.6664167916041979, 0.6796699174793699, 0.6931732933233309, 0.6956521739130435, 0.6759189797449362, 0.6729182295573893], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 29.850695200031623, "cv_time_sec": 22.046999100013636, "best_params": {"classifier__subsample": 1.0, "classifier__n_estimators": 200, "classifier__max_depth": 4, "classifier__learning_rate": 0.1}, "best_cv": 0.6806248976037113, "classifier_params": {"ccp_alpha": 0.0, "criterion": "friedman_mse", "init": null, "learning_rate": 0.1, "loss": "log_loss", "max_depth": 4, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 1, "min_samples_split": 2, "min_weight_fraction_leaf": 0.0, "n_estimators": 200, "n_iter_no_change": null, "random_state": 25, "subsample": 1.0, "tol": 0.0001, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}, "K-Nearest Neighbors": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.532252628374485, "cv_std_accuracy": 0.01758863149971614, "cv_fold_scores": [0.5037481259370314, 0.5213803450862715, 0.5536384096024006, 0.5397301349325337, 0.5423855963990998, 0.5326331582895724], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.01273890002630651, "cv_time_sec": 1.4456454999744892, "classifier_params": {"base_estimator": "KNeighborsClassifier(metric='euclidean', weights='distance')"}}, "Decision Tree": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.5535007252562766, "cv_std_accuracy": 0.023712848576974574, "cv_fold_scores": [0.5592203898050975, 0.5318829707426856, 0.5978994748687172, 0.5419790104947526, 0.5393848462115529, 0.5506376594148538], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 0.05283250007778406, "cv_time_sec": 0.8861817000433803, "classifier_params": {"ccp_alpha": 0.0, "class_weight": "balanced", "criterion": "gini", "max_depth": 10, "max_features": null, "max_leaf_nodes": null, "min_impurity_decrease": 0.0, "min_samples_leaf": 5, "min_samples_split": 10, "min_weight_fraction_leaf": 0.0, "monotonic_cst": null, "random_state": 25, "splitter": "best"}}, "XGBoost": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6941256491034302, "cv_std_accuracy": 0.006098404035577034, "cv_fold_scores": [0.6896551724137931, 0.6999249812453113, 0.7029257314328582, 0.6934032983508246, 0.687171792948237, 0.6916729182295573], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 2.573655700078234, "cv_time_sec": 4.528671000036411, "best_params": {"classifier__base_estimator__subsample": 0.6, "classifier__base_estimator__reg_lambda": 1.0, "classifier__base_estimator__reg_alpha": 1.0, "classifier__base_estimator__n_estimators": 400, "classifier__base_estimator__max_depth": 8, "classifier__base_estimator__learning_rate": 0.1, "classifier__base_estimator__colsample_bytree": 0.6}, "best_cv": 0.6922487743375124, "classifier_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.6, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=8, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=400, n_jobs=21,\n              num_parallel_tree=None, ...)"}}, "HistGradient Boosting": {"test_accuracy": null, "test_macro_f1": null, "test_micro_f1": null, "test_balanced_accuracy": null, "cv_mean_accuracy": 0.6878753046582485, "cv_std_accuracy": 0.010053587879741263, "cv_fold_scores": [0.6754122938530734, 0.6901725431357839, 0.700675168792198, 0.6979010494752623, 0.6811702925731433, 0.68192048012003], "cv_used_splits": 3, "cv_used_repeats": 2, "cv_scoring": "accuracy", "fit_time_sec": 2.7341056000441313, "cv_time_sec": 4.48513919999823, "best_params": {"classifier__max_leaf_nodes": 31, "classifier__learning_rate": 0.1, "classifier__l2_regularization": 1.0}, "best_cv": 0.6878753046582485, "classifier_params": {"categorical_features": "warn", "class_weight": null, "early_stopping": true, "interaction_cst": null, "l2_regularization": 1.0, "learning_rate": 0.1, "loss": "log_loss", "max_bins": 255, "max_depth": null, "max_features": 1.0, "max_iter": 100, "max_leaf_nodes": 31, "min_samples_leaf": 20, "monotonic_cst": null, "n_iter_no_change": 10, "random_state": 25, "scoring": "loss", "tol": 1e-07, "validation_fraction": 0.1, "verbose": 0, "warm_start": false}}}, "efficiency": {"inference_time_sec": 0.03307820006739348, "per_sample_latency_sec": 3.307820006739348e-05, "training_time_sec_by_model": {"Logistic Regression": 0.7656964999623597, "Random Forest": 0.2528924000216648, "SVM (RBF)": 6.539970200043172, "SVM (Linear)": 12.218516499968246, "Naive Bayes": 0.020519800018519163, "Neural Network": 3.092238700017333, "Gradient Boosting": 29.850695200031623, "K-Nearest Neighbors": 0.01273890002630651, "Decision Tree": 0.05283250007778406, "XGBoost": 2.573655700078234, "HistGradient Boosting": 2.7341056000441313}}, "model_info": {"n_classes": null, "feature_dimension": null, "has_predict_proba": true}, "data_info": {"n_train": 4000, "n_test": 1000}, "calibration": {"bins": 10, "pre": {"calibration_frac_pos": [0.3333333333333333, 0.25, 0.2982456140350877, 0.4954128440366973, 0.4838709677419355, 0.6126126126126126, 0.6693548387096774, 0.9144050104384134], "calibration_mean_pred": [0.2863379816214244, 0.3570157376428445, 0.46093324140498515, 0.5477939351983027, 0.6545141941757613, 0.7507154372361329, 0.853121772889168, 0.9708911960707328], "brier_score": 0.16909763589464416, "ece": 0.09873531380295754, "mce": 0.18376693417949064, "brier_reliability": 0.012630156650600594, "brier_resolution": 0.04620949974088451, "brier_uncertainty": 0.20505600000000002, "bins": 10, "bin_edges": [0.0, 0.1, 0.2, 0.30000000000000004, 0.4, 0.5, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 1.0], "bin_counts": [0, 0, 3, 24, 57, 109, 93, 111, 124, 479], "bin_acc": [null, null, 0.3333333333333333, 0.25, 0.2982456140350877, 0.4954128440366973, 0.4838709677419355, 0.6126126126126126, 0.6693548387096774, 0.9144050104384134], "bin_conf": [null, null, 0.2863379816214244, 0.3570157376428445, 0.46093324140498515, 0.5477939351983027, 0.6545141941757613, 0.7507154372361329, 0.853121772889168, 0.9708911960707328], "plot_frac_pos": [0.3333333333333333, 0.25, 0.2982456140350877, 0.4954128440366973, 0.4838709677419355, 0.6126126126126126, 0.6693548387096774, 0.9144050104384134], "plot_mean_pred": [0.2863379816214244, 0.3570157376428445, 0.46093324140498515, 0.5477939351983027, 0.6545141941757613, 0.7507154372361329, 0.853121772889168, 0.9708911960707328]}, "post": null, "plots": {"pre": "artefacts\\run_20251001_190703\\figures\\calibration_reliability_curve_-_xgboost.png", "post": null, "comparison": null}}, "bootstrap_ci": {"accuracy_ci95": [0.683975, 0.741025], "macro_f1_ci95": [0.4788535866396441, 0.5684642908404423], "balanced_accuracy_ci95": [0.47126246438374464, 0.5598500356474673]}, "artefact": {"path": "artefacts\\run_20251001_190703\\models\\xgboost_20251001_190703_2025-10-01T19-11-02.549049+00-00.joblib", "size_bytes": 11758957, "size_bytes_by_model": {"Logistic Regression": 98814, "Random Forest": 42216067, "SVM (RBF)": 5870878, "SVM (Linear)": 5420030, "Naive Bayes": 106962, "Neural Network": 408399, "Gradient Boosting": 6733597, "K-Nearest Neighbors": 6289817, "Decision Tree": 175163, "XGBoost": 11758957, "HistGradient Boosting": 4084800}}, "best_model_params": {"base_estimator": "XGBClassifier(base_score=None, booster=None, callbacks=None,\n              colsample_bylevel=None, colsample_bynode=None,\n              colsample_bytree=0.6, device=None, early_stopping_rounds=None,\n              enable_categorical=False, eval_metric='mlogloss',\n              feature_types=None, feature_weights=None, gamma=None,\n              grow_policy=None, importance_type=None,\n              interaction_constraints=None, learning_rate=0.1, max_bin=None,\n              max_cat_threshold=None, max_cat_to_onehot=None,\n              max_delta_step=None, max_depth=8, max_leaves=None,\n              min_child_weight=None, missing=nan, monotone_constraints=None,\n              multi_strategy=None, n_estimators=400, n_jobs=21,\n              num_parallel_tree=None, ...)"}, "pr_curves_plot": "artefacts\\run_20251001_190703\\figures\\pr_curves_pr_curves_-_xgboost.png", "error_analysis": {"per_class_csv": "artefacts\\run_20251001_190703\\tables\\per_class_metrics_20251001_190703.csv", "confusions_csv": "artefacts\\run_20251001_190703\\tables\\top_confusions_20251001_190703.csv", "misclassifications_csv": "artefacts\\run_20251001_190703\\tables\\misclassifications_20251001_190703.csv", "misclassifications_shap_csv": null, "slices_csv": "artefacts\\run_20251001_190703\\tables\\slices_20251001_190703.csv"}, "slices": {"metrics_csv": null, "settings": {"rare_threshold": 5, "length_short_max": 5, "length_long_min": 21}}, "do_final_test": true, "provenance": {"timestamp_utc": "2025-10-01T19:11:03.004579+00:00", "python_version": "3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 15:03:56) [MSC v.1929 64 bit (AMD64)]", "platform": "Windows-11-10.0.26100-SP0", "os_version": "10.0.26100", "hostname": "DESKTOP-KNG670J", "cpu_count": 32, "cpu_arch": "AMD64", "gpu": ["NVIDIA GeForce RTX 4070 Ti SUPER"], "libraries": {"numpy": "1.26.4", "scikit-learn": "1.4.2", "pandas": "2.2.3", "joblib": "1.4.2", "shap": "0.48.0", "xgboost": "3.0.5", "imbalanced-learn": "0.12.3", "matplotlib": "3.8.4", "argparse": null, "seaborn": "0.13.2"}}}