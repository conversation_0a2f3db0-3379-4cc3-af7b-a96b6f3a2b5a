{"run_id": "20251001_184045", "best_model_name": "Grad<PERSON>", "training_time_sec_by_model": {"Logistic Regression": 0.08832709991838783, "Random Forest": 0.16344560007564723, "SVM (RBF)": 0.3186212999280542, "SVM (Linear)": 0.29156310006510466, "Naive Bayes": 0.005377700086683035, "Neural Network": 0.44593239994719625, "Gradient Boosting": 6.0582835000241175, "K-Nearest Neighbors": 0.003924299962818623, "Decision Tree": 0.012615400017239153, "XGBoost": 1.0978701999410987, "HistGradient Boosting": 0.751793900039047}, "calibration_plots": {"pre": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting.png", "post": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_184045\\figures\\calibration_reliability_curve_-_gradient_boosting_(pre_vs_post).png"}, "metrics_json": "artefacts\\run_20251001_184045\\metrics\\metrics_20251001_184045.json"}