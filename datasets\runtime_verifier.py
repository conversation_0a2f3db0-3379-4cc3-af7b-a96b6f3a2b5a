"""
Hybrid runtime verification utilities for injected Python code.

Strategy:
Fast subprocess harness with timeout & builtin restrictions.
Optional fallback to analyse_code (imports lazily) if harness yields no error
but injection metadata suggests a runtime error should exist (e.g., ZeroDivisionError).

This keeps verification decoupled from the injection logic and avoids inline exec.
"""

from __future__ import annotations
import subprocess
import tempfile
import textwrap
import json
import sys
from typing import Optional, Dict, Any

RUNTIME_TIMEOUT = 1.0
HARNESS = textwrap.dedent(
    """
    import runpy, builtins, sys, json, traceback
    # Restrict builtins (minimal safe whitelist + essentials for traceback & runtime)
    allowed = {
        'print': (lambda *a, **k: None),
        'len': len,
        'range': range,
        'Exception': Exception,
        'ValueError': ValueError,
        'TypeError': TypeError,
        'getattr': getattr,
        'setattr': setattr,
        'isinstance': isinstance,
        'issubclass': issubclass,
        'object': object,
        'str': str,
        'int': int,
        'float': float,
        'bool': bool,
        'list': list,
        'dict': dict,
        'tuple': tuple,
        'set': set,
        'bytes': bytes,
        'enumerate': enumerate,
        'reversed': reversed,
        'zip': zip,
        'min': min,
        'max': max,
        'sum': sum,
        'abs': abs,
    }
    builtins.__dict__.clear()
    builtins.__dict__.update(allowed)

    # Disable input
    builtins.input = lambda *a, **k: (_ for _ in ()).throw(RuntimeError('input disabled'))

    target = sys.argv[1]
    try:
        runpy.run_path(target, run_name='__main__')
        print(json.dumps({'status': 'ok'}))
    except Exception as e:
        tb = traceback.extract_tb(e.__traceback__)
        frame = tb[-1] if tb else None
        print(json.dumps({
            'status': 'error',
            'error_type': type(e).__name__,
            'message': str(e),
            'line': frame.lineno if frame else None
        }))
    """
)


def verify_runtime_subprocess(
    code: str, timeout: float = RUNTIME_TIMEOUT
) -> Dict[str, Any]:
    """Run code in a restricted subprocess harness.
    Returns dict with runtime_error_type/description and status.
    """
    with tempfile.NamedTemporaryFile("w", suffix=".py", delete=False) as tmp:
        tmp.write(code)
        tmp_path = tmp.name
    cmd = [sys.executable, "-c", HARNESS, tmp_path]
    try:
        proc = subprocess.run(
            cmd, capture_output=True, text=True, timeout=timeout, check=False
        )
    except subprocess.TimeoutExpired:
        return {
            "runtime_verification_status": "timeout",
            "runtime_error_type": "RuntimeError_Timeout",
            "runtime_error_description": f"Execution exceeded {timeout}s",
            "runtime_error_line": None,
        }
    stdout = proc.stdout.strip().splitlines()
    last = stdout[-1] if stdout else ""
    try:
        payload = json.loads(last)
    except json.JSONDecodeError:
        return {
            "runtime_verification_status": "malformed_output",
            "runtime_error_type": None,
            "runtime_error_description": proc.stderr.strip() or last,
            "runtime_error_line": None,
        }
    if payload.get("status") == "ok":
        return {
            "runtime_verification_status": "clean",
            "runtime_error_type": None,
            "runtime_error_description": None,
            "runtime_error_line": None,
        }
    if payload.get("status") == "error":
        return {
            "runtime_verification_status": "error",
            "runtime_error_type": payload.get("error_type"),
            "runtime_error_description": payload.get("message"),
            "runtime_error_line": payload.get("line"),
        }
    return {
        "runtime_verification_status": "unknown",
        "runtime_error_type": None,
        "runtime_error_description": None,
        "runtime_error_line": None,
    }


def enrich_with_pipeline_analysis(code: str, meta: Dict[str, Any]) -> Dict[str, Any]:
    """Secondary analysis using analyse_code for further classification.
    Only invoked if runtime_verification_status in {'clean','unknown'} and the intended
    error looks runtime (e.g., contains 'Error').
    """
    intended = (meta.get("error_type") or "").lower()
    if meta.get("runtime_verification_status") in {"error", "timeout"}:
        return meta  # already have runtime classification
    if not any(
        token in intended for token in ["error", "division", "recursion", "unbound"]
    ):
        return meta  # logic-only or syntax markers
    try:
        # local import to avoid heavy load if unused
        from ast_and_lint import analyse_code  # pylint: disable=import-outside-toplevel

        analysis = analyse_code(code)
        if analysis.get("error_type"):
            meta.update(
                {
                    "runtime_verification_status": meta.get(
                        "runtime_verification_status"
                    )
                    + "+pipeline",
                    "pipeline_error_type": analysis.get("error_type"),
                    "pipeline_error_description": analysis.get("error_description"),
                    "pipeline_error_line": analysis.get("line_number"),
                }
            )
    except Exception as e:  # pylint: disable=broad-except
        meta.setdefault("pipeline_error_type", None)
        meta.setdefault("pipeline_error_description", f"pipeline_failed: {e}")
    return meta


def hybrid_verify(code: str, intended_error_type: Optional[str]) -> Dict[str, Any]:
    """Hybrid verification: subprocess first, optional pipeline enrichment."""
    meta = verify_runtime_subprocess(code)
    meta["intended_error_type"] = intended_error_type
    return enrich_with_pipeline_analysis(code, meta)
