2025-10-01 19:36:29 | = Error Type Classifier - Comprehensive ML Comparison =
2025-10-01 19:36:29 | ============================================================
2025-10-01 19:36:29 | Loading dataset...
2025-10-01 19:36:29 | Dataset loaded: 11977 samples
2025-10-01 19:36:29 | Error type distribution:
2025-10-01 19:36:29 | intended_error_type
2025-10-01 19:36:29 | SyntaxErrorMissingColon       2073
2025-10-01 19:36:29 | TypeErrorArity                1831
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    1826
2025-10-01 19:36:29 | NameError                     1802
2025-10-01 19:36:29 | RecursionErrorPotential       1399
2025-10-01 19:36:29 | UnboundLocalError              691
2025-10-01 19:36:29 | LogicErrorComparison           602
2025-10-01 19:36:29 | LogicErrorNegation             592
2025-10-01 19:36:29 | AttributeError                 506
2025-10-01 19:36:29 | ZeroDivisionError              172
2025-10-01 19:36:29 | TypeErrorBadAdd                170
2025-10-01 19:36:29 | TypeErrorBadKwarg              137
2025-10-01 19:36:29 | KeyError                        92
2025-10-01 19:36:29 | IndexError                      77
2025-10-01 19:36:29 | FileNotFoundError                7
2025-10-01 19:36:29 | Name: count, dtype: int64
2025-10-01 19:36:29 | Limiting dataset to first 200 samples...
2025-10-01 19:36:29 | Dataset size after limiting: 200 samples
2025-10-01 19:36:29 | Skipping error-type grouping (ablation). Using original labels.
2025-10-01 19:36:29 | Ablation settings -> grouping: off | NoError augmentation: off

2025-10-01 19:36:29 | [Original (pre-split)] Class distribution (n=200):
2025-10-01 19:36:29 | intended_error_type
2025-10-01 19:36:29 | SyntaxErrorMissingColon       34
2025-10-01 19:36:29 | TypeErrorArity                31
2025-10-01 19:36:29 | NameError                     25
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    25
2025-10-01 19:36:29 | RecursionErrorPotential       22
2025-10-01 19:36:29 | UnboundLocalError             16
2025-10-01 19:36:29 | LogicErrorNegation            14
2025-10-01 19:36:29 | LogicErrorComparison          10
2025-10-01 19:36:29 | AttributeError                10
2025-10-01 19:36:29 | ZeroDivisionError              4
2025-10-01 19:36:29 | TypeErrorBadAdd                3
2025-10-01 19:36:29 | KeyError                       3
2025-10-01 19:36:29 | TypeErrorBadKwarg              2
2025-10-01 19:36:29 | IndexError                     1
2025-10-01 19:36:29 | Name: count, dtype: int64

2025-10-01 19:36:29 | [Pruning] Removing 1 samples from 1 class(es) with <2 samples (cannot stratify / CV): IndexError (n=1)

2025-10-01 19:36:29 | [Post-singleton-removal] Class distribution (n=199):
2025-10-01 19:36:29 | intended_error_type
2025-10-01 19:36:29 | SyntaxErrorMissingColon       34
2025-10-01 19:36:29 | TypeErrorArity                31
2025-10-01 19:36:29 | NameError                     25
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    25
2025-10-01 19:36:29 | RecursionErrorPotential       22
2025-10-01 19:36:29 | UnboundLocalError             16
2025-10-01 19:36:29 | LogicErrorNegation            14
2025-10-01 19:36:29 | LogicErrorComparison          10
2025-10-01 19:36:29 | AttributeError                10
2025-10-01 19:36:29 | ZeroDivisionError              4
2025-10-01 19:36:29 | TypeErrorBadAdd                3
2025-10-01 19:36:29 | KeyError                       3
2025-10-01 19:36:29 | TypeErrorBadKwarg              2
2025-10-01 19:36:29 | Name: count, dtype: int64
2025-10-01 19:36:29 | Performing leakage-safe split at original sample level before balancing...

2025-10-01 19:36:29 | [Base Train (post-split, pre-balance)] Class distribution (n=159):
2025-10-01 19:36:29 | intended_error_type
2025-10-01 19:36:29 | SyntaxErrorMissingColon       27
2025-10-01 19:36:29 | TypeErrorArity                25
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    20
2025-10-01 19:36:29 | NameError                     20
2025-10-01 19:36:29 | RecursionErrorPotential       18
2025-10-01 19:36:29 | UnboundLocalError             13
2025-10-01 19:36:29 | LogicErrorNegation            11
2025-10-01 19:36:29 | AttributeError                 8
2025-10-01 19:36:29 | LogicErrorComparison           8
2025-10-01 19:36:29 | ZeroDivisionError              3
2025-10-01 19:36:29 | TypeErrorBadKwarg              2
2025-10-01 19:36:29 | TypeErrorBadAdd                2
2025-10-01 19:36:29 | KeyError                       2
2025-10-01 19:36:29 | Name: count, dtype: int64

2025-10-01 19:36:29 | [Base Test (post-split, pre-balance)] Class distribution (n=40):
2025-10-01 19:36:29 | intended_error_type
2025-10-01 19:36:29 | SyntaxErrorMissingColon       7
2025-10-01 19:36:29 | TypeErrorArity                6
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    5
2025-10-01 19:36:29 | NameError                     5
2025-10-01 19:36:29 | RecursionErrorPotential       4
2025-10-01 19:36:29 | LogicErrorNegation            3
2025-10-01 19:36:29 | UnboundLocalError             3
2025-10-01 19:36:29 | AttributeError                2
2025-10-01 19:36:29 | LogicErrorComparison          2
2025-10-01 19:36:29 | ZeroDivisionError             1
2025-10-01 19:36:29 | KeyError                      1
2025-10-01 19:36:29 | TypeErrorBadAdd               1
2025-10-01 19:36:29 | Name: count, dtype: int64
2025-10-01 19:36:29 | Creating training subset without NoError augmentation...
2025-10-01 19:36:29 | Created dataset (errors only):
2025-10-01 19:36:29 |  Error samples: 159
2025-10-01 19:36:29 |  NoError samples: 0 (disabled)
2025-10-01 19:36:29 |  Total: 159
2025-10-01 19:36:29 | Creating test subset without NoError augmentation...
2025-10-01 19:36:29 | Created dataset (errors only):
2025-10-01 19:36:29 |  Error samples: 40
2025-10-01 19:36:29 |  NoError samples: 0 (disabled)
2025-10-01 19:36:29 |  Total: 40

2025-10-01 19:36:29 | [Train (no NoError)] Class distribution (n=159):
2025-10-01 19:36:29 | label
2025-10-01 19:36:29 | SyntaxErrorMissingColon       27
2025-10-01 19:36:29 | TypeErrorArity                25
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    20
2025-10-01 19:36:29 | NameError                     20
2025-10-01 19:36:29 | RecursionErrorPotential       18
2025-10-01 19:36:29 | UnboundLocalError             13
2025-10-01 19:36:29 | LogicErrorNegation            11
2025-10-01 19:36:29 | AttributeError                 8
2025-10-01 19:36:29 | LogicErrorComparison           8
2025-10-01 19:36:29 | ZeroDivisionError              3
2025-10-01 19:36:29 | TypeErrorBadKwarg              2
2025-10-01 19:36:29 | TypeErrorBadAdd                2
2025-10-01 19:36:29 | KeyError                       2
2025-10-01 19:36:29 | Name: count, dtype: int64

2025-10-01 19:36:29 | [Test (no NoError)] Class distribution (n=40):
2025-10-01 19:36:29 | label
2025-10-01 19:36:29 | SyntaxErrorMissingColon       7
2025-10-01 19:36:29 | TypeErrorArity                6
2025-10-01 19:36:29 | SyntaxErrorMismatchedParen    5
2025-10-01 19:36:29 | NameError                     5
2025-10-01 19:36:29 | RecursionErrorPotential       4
2025-10-01 19:36:29 | LogicErrorNegation            3
2025-10-01 19:36:29 | UnboundLocalError             3
2025-10-01 19:36:29 | AttributeError                2
2025-10-01 19:36:29 | LogicErrorComparison          2
2025-10-01 19:36:29 | ZeroDivisionError             1
2025-10-01 19:36:29 | KeyError                      1
2025-10-01 19:36:29 | TypeErrorBadAdd               1
2025-10-01 19:36:29 | Name: count, dtype: int64
2025-10-01 19:36:29 | Creating AST features for training subset...
2025-10-01 19:36:29 | Creating AST features for test subset...
2025-10-01 19:36:29 | Sample AST features examples (Train):
2025-10-01 19:36:29 |   Train Sample 1 -> Label: TypeErrorBadKwarg | Features: {'feat_arithmetic_ops': 0, 'feat_assert_statements': 0, 'feat_assignments': 4, 'feat_attribute_access': 4, 'feat_aug_assignments': 0}...
2025-10-01 19:36:29 |   Train Sample 2 -> Label: TypeErrorBadKwarg | Features: {'feat_arithmetic_ops': 4, 'feat_assert_statements': 0, 'feat_assignments': 1, 'feat_attribute_access': 2, 'feat_aug_assignments': 0}...
2025-10-01 19:36:29 | --------------------------------------------------
2025-10-01 19:36:29 | Sample AST features examples (Test):
2025-10-01 19:36:29 |   Test Sample 1 -> Label: TypeErrorArity | Features: {'feat_arithmetic_ops': 0, 'feat_assert_statements': 0, 'feat_assignments': 3, 'feat_attribute_access': 4, 'feat_aug_assignments': 0}...
2025-10-01 19:36:29 |   Test Sample 2 -> Label: SyntaxErrorMissingColon | Features: {'feat_arithmetic_ops': 0, 'feat_assert_statements': 0, 'feat_assignments': 0, 'feat_attribute_access': 0, 'feat_aug_assignments': 0}...
2025-10-01 19:36:29 | --------------------------------------------------

2025-10-01 19:36:29 | Starting comprehensive classifier comparison...

2025-10-01 19:36:29 | === BASELINE FIT + CV (no test peeking) ===
2025-10-01 19:36:29 | Training Logistic Regression on 159 samples...
2025-10-01 19:36:29 | Logistic Regression training complete. (fit_time=0.011s)

2025-10-01 19:36:29 | === LOGISTIC REGRESSION CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:30 | 2-Fold (repeats=2) CV accuracy: 0.3521 (+/- 0.0516) over 4 folds
2025-10-01 19:36:30 | Training Random Forest on 159 samples...
2025-10-01 19:36:30 | Random Forest training complete. (fit_time=0.147s)

2025-10-01 19:36:30 | === RANDOM FOREST CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:31 | 2-Fold (repeats=2) CV accuracy: 0.3994 (+/- 0.0513) over 4 folds
2025-10-01 19:36:31 | Training SVM (RBF) on 159 samples...
2025-10-01 19:36:31 | SVM (RBF) training complete. (fit_time=0.028s)

2025-10-01 19:36:31 | === SVM (RBF) CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:31 |   Using conservative parallelism for SVM (RBF): n_jobs=8
2025-10-01 19:36:32 | 2-Fold (repeats=2) CV accuracy: 0.3271 (+/- 0.0648) over 4 folds
2025-10-01 19:36:32 | Training SVM (Linear) on 159 samples...
2025-10-01 19:36:32 | SVM (Linear) training complete. (fit_time=0.015s)

2025-10-01 19:36:32 | === SVM (LINEAR) CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:33 | 2-Fold (repeats=2) CV accuracy: 0.4277 (+/- 0.1258) over 4 folds
2025-10-01 19:36:33 | Training Naive Bayes on 159 samples...
2025-10-01 19:36:33 | Naive Bayes training complete. (fit_time=0.003s)

2025-10-01 19:36:33 | === NAIVE BAYES CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:34 | 2-Fold (repeats=2) CV accuracy: 0.3241 (+/- 0.1635) over 4 folds
2025-10-01 19:36:34 | Training Neural Network on 159 samples...
2025-10-01 19:36:34 | Neural Network training complete. (fit_time=0.089s)

2025-10-01 19:36:34 | === NEURAL NETWORK CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:34 |   Using conservative parallelism for Neural Network: n_jobs=8
2025-10-01 19:36:35 | 2-Fold (repeats=2) CV accuracy: 0.1980 (+/- 0.0605) over 4 folds
2025-10-01 19:36:35 | Training Gradient Boosting on 159 samples...
2025-10-01 19:36:36 | Gradient Boosting training complete. (fit_time=0.518s)

2025-10-01 19:36:36 | === GRADIENT BOOSTING CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:36 |   Using conservative parallelism for Gradient Boosting: n_jobs=8
2025-10-01 19:36:37 | 2-Fold (repeats=2) CV accuracy: 0.4307 (+/- 0.0808) over 4 folds
2025-10-01 19:36:37 | Training K-Nearest Neighbors on 159 samples...
2025-10-01 19:36:37 | K-Nearest Neighbors training complete. (fit_time=0.003s)

2025-10-01 19:36:37 | === K-NEAREST NEIGHBORS CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:38 | 2-Fold (repeats=2) CV accuracy: 0.3081 (+/- 0.0915) over 4 folds
2025-10-01 19:36:38 | Training Decision Tree on 159 samples...
2025-10-01 19:36:38 | Decision Tree training complete. (fit_time=0.007s)

2025-10-01 19:36:38 | === DECISION TREE CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:39 | 2-Fold (repeats=2) CV accuracy: 0.3553 (+/- 0.0643) over 4 folds
2025-10-01 19:36:39 | Training XGBoost on 159 samples...
2025-10-01 19:36:39 | XGBoost training complete. (fit_time=0.374s)

2025-10-01 19:36:39 | === XGBOOST CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:39 |   Using conservative parallelism for XGBoost: n_jobs=8
2025-10-01 19:36:41 | 2-Fold (repeats=2) CV accuracy: 0.4338 (+/- 0.0903) over 4 folds
2025-10-01 19:36:41 | Training HistGradient Boosting on 159 samples...
2025-10-01 19:36:41 | HistGradient Boosting training complete. (fit_time=0.483s)

2025-10-01 19:36:41 | === HISTGRADIENT BOOSTING CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:41 |   Using conservative parallelism for HistGradient Boosting: n_jobs=8
2025-10-01 19:36:42 | 2-Fold (repeats=2) CV accuracy: 0.4399 (+/- 0.1611) over 4 folds
2025-10-01 19:36:42 | --------------------------------------------------------------------------------
2025-10-01 19:36:42 | Top Candidates for tuning: HistGradient Boosting, XGBoost, Gradient Boosting
2025-10-01 19:36:42 | --------------------------------------------------------------------------------

2025-10-01 19:36:42 | === HYPERPARAMETER TUNING (top 3) ===
2025-10-01 19:36:42 | Hyperparameter search for hist_gradient_boosting with 4 iterations (CV=2, scoring=accuracy)...
2025-10-01 19:36:44 |   Best CV score (accuracy): 0.4462
2025-10-01 19:36:44 | Training HistGradient Boosting (tuned) on 159 samples...
2025-10-01 19:36:45 | HistGradient Boosting (tuned) training complete. (fit_time=0.489s)

2025-10-01 19:36:45 | === HISTGRADIENT BOOSTING (TUNED) CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:45 |   Using conservative parallelism for HistGradient Boosting (tuned): n_jobs=8
2025-10-01 19:36:46 | 2-Fold (repeats=2) CV accuracy: 0.4462 (+/- 0.1487) over 4 folds
2025-10-01 19:36:46 |   Using conservative parallelism for xgboost: n_jobs=12, pre_dispatch=10
2025-10-01 19:36:46 | Hyperparameter search for xgboost with 4 iterations (CV=2, scoring=accuracy)...
2025-10-01 19:36:50 |   Best CV score (accuracy): 0.4527
2025-10-01 19:36:50 | Training XGBoost (tuned) on 159 samples...
2025-10-01 19:36:51 | XGBoost (tuned) training complete. (fit_time=0.858s)

2025-10-01 19:36:51 | === XGBOOST (TUNED) CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:36:51 |   Using conservative parallelism for XGBoost (tuned): n_jobs=8
2025-10-01 19:36:53 | 2-Fold (repeats=2) CV accuracy: 0.4527 (+/- 0.0978) over 4 folds
2025-10-01 19:36:53 |   Using conservative parallelism for gradient_boosting: n_jobs=12, pre_dispatch=10
2025-10-01 19:36:53 | Hyperparameter search for gradient_boosting with 4 iterations (CV=2, scoring=accuracy)...
2025-10-01 19:36:59 |   Best CV score (accuracy): 0.4591
2025-10-01 19:36:59 | Training Gradient Boosting (tuned) on 159 samples...
2025-10-01 19:37:01 | Gradient Boosting (tuned) training complete. (fit_time=2.018s)

2025-10-01 19:37:01 | === GRADIENT BOOSTING (TUNED) CROSS-VALIDATION (Training data: 159 samples) ===
2025-10-01 19:37:01 |   Using conservative parallelism for Gradient Boosting (tuned): n_jobs=8
2025-10-01 19:37:03 | 2-Fold (repeats=2) CV accuracy: 0.4591 (+/- 0.0617) over 4 folds

2025-10-01 19:37:03 | Selected best model: Gradient Boosting
2025-10-01 19:37:03 | Testing Gradient Boosting on 40 samples...Inference time on test (40): 0.002s | per-sample 0.06 ms
2025-10-01 19:37:03 | Saved per-class PR curves -> artefacts\run_20251001_183629\figures\pr_curves_pr_curves_-_gradient_boosting.png
2025-10-01 19:37:04 | Saved calibration plot -> artefacts\run_20251001_183629\figures\calibration_reliability_curve_-_gradient_boosting.png
2025-10-01 19:37:04 | Post-hoc calibration failed/skipped: Requesting 5-fold cross-validation but provided less than 5 examples for at least one class.
2025-10-01 19:37:04 | Saved slice metrics -> artefacts\run_20251001_183629\tables\slices_20251001_183629.csv
2025-10-01 19:37:04 | Saved significance results -> artefacts\run_20251001_183629\tables\significance_20251001_183629.csv
2025-10-01 19:37:04 | Saved best model (Gradient Boosting) -> artefacts\run_20251001_183629\models\gradient_boosting_20251001_183629_2025-10-01T18-37-04.258884+00-00.joblib
2025-10-01 19:37:04 | Saved combined metrics JSON -> artefacts\run_20251001_183629\metrics\metrics_20251001_183629.json

2025-10-01 19:37:04 | === NESTED CROSS-VALIDATION (training data only) ===
2025-10-01 19:37:04 | Dataset hash: 2749c499b15d…

2025-10-01 19:37:04 | Outer fold 1: train=127 test=32
2025-10-01 19:37:04 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:05 |   Gradient Boosting      outer score = 0.5625
2025-10-01 19:37:05 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:05 |   XGBoost                outer score = 0.5625

2025-10-01 19:37:05 | Outer fold 2: train=127 test=32
2025-10-01 19:37:05 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:06 |   Gradient Boosting      outer score = 0.3125
2025-10-01 19:37:06 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:06 |   XGBoost                outer score = 0.4062

2025-10-01 19:37:06 | Outer fold 3: train=127 test=32
2025-10-01 19:37:06 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:06 |   Gradient Boosting      outer score = 0.5000
2025-10-01 19:37:06 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:07 |   XGBoost                outer score = 0.4688

2025-10-01 19:37:07 | Outer fold 4: train=127 test=32
2025-10-01 19:37:07 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:07 |   Gradient Boosting      outer score = 0.4375
2025-10-01 19:37:07 | Skipping hyperparameter search (insufficient per-class samples for stratified CV).
2025-10-01 19:37:07 |   XGBoost                outer score = 0.4688

2025-10-01 19:37:07 | Outer fold 5: train=128 test=31
2025-10-01 19:37:07 |   Using conservative parallelism for gradient_boosting: n_jobs=12, pre_dispatch=10
2025-10-01 19:37:07 | Hyperparameter search for gradient_boosting with 4 iterations (CV=2, scoring=accuracy)...
2025-10-01 19:37:11 |   Best CV score (accuracy): 0.4336
2025-10-01 19:37:12 |   Gradient Boosting      outer score = 0.4516
2025-10-01 19:37:12 |   Using conservative parallelism for xgboost: n_jobs=12, pre_dispatch=10
2025-10-01 19:37:12 | Hyperparameter search for xgboost with 4 iterations (CV=2, scoring=accuracy)...
2025-10-01 19:37:13 |   Best CV score (accuracy): 0.4258
2025-10-01 19:37:14 |   XGBoost                outer score = 0.3226

2025-10-01 19:37:14 | === Nested CV summary (outer-fold scores) ===
2025-10-01 19:37:14 | Gradient Boosting      mean=0.4528 std=0.0924 ci95=[0.3718, 0.5339] n=5
2025-10-01 19:37:14 | XGBoost                mean=0.4458 std=0.0886 ci95=[0.3681, 0.5235] n=5
2025-10-01 19:37:14 | (No artefacts saved; training-only diagnostic.)

2025-10-01 19:37:14 | --- Testing Predictions on Sample Code Patterns ---

2025-10-01 19:37:14 | Test Case 1:
2025-10-01 19:37:14 | Code: print(unknown_var)
2025-10-01 19:37:14 | AST Message: name 'unknown_var' is not defined
2025-10-01 19:37:14 | Predicted Error Type: SyntaxErrorMissingColon

2025-10-01 19:37:14 | Test Case 2:
2025-10-01 19:37:14 | Code: if x > 5
2025-10-01 19:37:14 |     print("greater")
2025-10-01 19:37:14 | AST Message: expected ':'
2025-10-01 19:37:14 | Predicted Error Type: SyntaxErrorMissingColon

2025-10-01 19:37:14 | Test Case 3:
2025-10-01 19:37:14 | Code: result = "Number: " + 25
2025-10-01 19:37:14 | AST Message: can only concatenate str (not "int") to str
2025-10-01 19:37:14 | Predicted Error Type: ZeroDivisionError

2025-10-01 19:37:14 | Test Case 4:
2025-10-01 19:37:14 | Code: def greet(name):
2025-10-01 19:37:14 |     return f"Hello, {name}!"

2025-10-01 19:37:14 | print(greet("World"))
2025-10-01 19:37:14 | AST Message: 
2025-10-01 19:37:14 | Predicted Error Type: UnboundLocalError

2025-10-01 19:37:14 | === DETAILED PREDICTION ANALYSIS ===

2025-10-01 19:37:14 | Test Case 1:
2025-10-01 19:37:14 | Code: print(unknown_var)
2025-10-01 19:37:14 | Expected Error Type: Variable not defined
2025-10-01 19:37:14 | Predicted Error Type: SyntaxErrorMissingColon
2025-10-01 19:37:14 | Confidence: 0.286
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. SyntaxErrorMissingColon: 0.286
2025-10-01 19:37:14 |   2. AttributeError: 0.233
2025-10-01 19:37:14 |   3. SyntaxErrorMismatchedParen: 0.180
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'function_calls', 'tree_depth', 'max_branching_factor', 'avg_branching_factor']...

2025-10-01 19:37:14 | Test Case 2:
2025-10-01 19:37:14 | Code: if x > 5
2025-10-01 19:37:14 |     print("greater")
2025-10-01 19:37:14 | Expected Error Type: Missing colon in if statement
2025-10-01 19:37:14 | Predicted Error Type: SyntaxErrorMissingColon
2025-10-01 19:37:14 | Confidence: 0.998
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. SyntaxErrorMissingColon: 0.998
2025-10-01 19:37:14 |   2. SyntaxErrorMismatchedParen: 0.002
2025-10-01 19:37:14 |   3. NameError: 0.000
2025-10-01 19:37:14 | Non-zero features: ['line_count', 'char_count', 'word_count', 'keyword_if_count', 'paren_count']...

2025-10-01 19:37:14 | Test Case 3:
2025-10-01 19:37:14 | Code: result = "Number: " + 25
2025-10-01 19:37:14 | Expected Error Type: Type mismatch in concatenation
2025-10-01 19:37:14 | Predicted Error Type: ZeroDivisionError
2025-10-01 19:37:14 | Confidence: 0.998
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. ZeroDivisionError: 0.998
2025-10-01 19:37:14 |   2. NameError: 0.002
2025-10-01 19:37:14 |   3. SyntaxErrorMissingColon: 0.000
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'assignments', 'tree_depth', 'max_branching_factor', 'avg_branching_factor']...

2025-10-01 19:37:14 | Test Case 4:
2025-10-01 19:37:14 | Code: def greet(name):
2025-10-01 19:37:14 |     return f"Hello, {name}!"

2025-10-01 19:37:14 | pri...
2025-10-01 19:37:14 | Expected Error Type: No errors found
2025-10-01 19:37:14 | Predicted Error Type: UnboundLocalError
2025-10-01 19:37:14 | Confidence: 0.466
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. UnboundLocalError: 0.466
2025-10-01 19:37:14 |   2. SyntaxErrorMissingColon: 0.221
2025-10-01 19:37:14 |   3. SyntaxErrorMismatchedParen: 0.139
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'function_defs', 'function_calls', 'return_statements', 'tree_depth']...
2025-10-01 19:37:14 | INFO: Predicted error for seemingly correct code

2025-10-01 19:37:14 | Test Case 5:
2025-10-01 19:37:14 | Code: my_list = [1, 2, 3]
2025-10-01 19:37:14 | print(my_list[5])
2025-10-01 19:37:14 | Expected Error Type: Index out of range
2025-10-01 19:37:14 | Predicted Error Type: KeyError
2025-10-01 19:37:14 | Confidence: 0.997
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. KeyError: 0.997
2025-10-01 19:37:14 |   2. NameError: 0.003
2025-10-01 19:37:14 |   3. AttributeError: 0.000
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'function_calls', 'assignments', 'tree_depth', 'max_branching_factor']...

2025-10-01 19:37:14 | Test Case 6:
2025-10-01 19:37:14 | Code: result = 10 / 0
2025-10-01 19:37:14 | Expected Error Type: Division by zero
2025-10-01 19:37:14 | Predicted Error Type: ZeroDivisionError
2025-10-01 19:37:14 | Confidence: 0.998
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. ZeroDivisionError: 0.998
2025-10-01 19:37:14 |   2. NameError: 0.002
2025-10-01 19:37:14 |   3. SyntaxErrorMissingColon: 0.000
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'assignments', 'tree_depth', 'max_branching_factor', 'avg_branching_factor']...

2025-10-01 19:37:14 | Test Case 7:
2025-10-01 19:37:14 | Code: for i in range(1, 11):
2025-10-01 19:37:14 |     print(i)
2025-10-01 19:37:14 | Expected Error Type: Off by one error in range
2025-10-01 19:37:14 | Predicted Error Type: TypeErrorArity
2025-10-01 19:37:14 | Confidence: 1.000
2025-10-01 19:37:14 | Top 3 predictions:
2025-10-01 19:37:14 |   1. TypeErrorArity: 1.000
2025-10-01 19:37:14 |   2. AttributeError: 0.000
2025-10-01 19:37:14 |   3. SyntaxErrorMissingColon: 0.000
2025-10-01 19:37:14 | Non-zero features: ['total_nodes', 'for_loops', 'function_calls', 'tree_depth', 'max_branching_factor']...
2025-10-01 19:37:14 | INFO: Predicted error for seemingly correct code
2025-10-01 19:37:14 | D:\Users\jan\Documents\MSc AI\Final Project\code\classifier_lib\output.py:31: RuntimeWarning: invalid value encountered in divide
2025-10-01 19:37:14 |   cm_percent = cm.astype("float") / cm.sum(axis=1)[:, np.newaxis] * 100
2025-10-01 19:37:14 | Saved confusion matrix -> artefacts\run_20251001_183629\figures\gradient_boosting_confusion_matrix20251001_183629.png

2025-10-01 19:37:14 | Gradient Boosting Confusion Matrix - Detailed Analysis:
2025-10-01 19:37:14 | --------------------------------------------------
2025-10-01 19:37:14 | AttributeError:
2025-10-01 19:37:14 | - True instances: 2
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | KeyError:
2025-10-01 19:37:14 | - True instances: 1
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | LogicErrorComparison:
2025-10-01 19:37:14 | - True instances: 2
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | LogicErrorNegation:
2025-10-01 19:37:14 | - True instances: 3
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | NameError:
2025-10-01 19:37:14 | - True instances: 5
2025-10-01 19:37:14 | - Correctly predicted: 4
2025-10-01 19:37:14 | - Precision: 0.500
2025-10-01 19:37:14 | - Recall: 0.800
2025-10-01 19:37:14 | RecursionErrorPotential:
2025-10-01 19:37:14 | - True instances: 4
2025-10-01 19:37:14 | - Correctly predicted: 3
2025-10-01 19:37:14 | - Precision: 0.600
2025-10-01 19:37:14 | - Recall: 0.750
2025-10-01 19:37:14 | SyntaxErrorMismatchedParen:
2025-10-01 19:37:14 | - True instances: 5
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | SyntaxErrorMissingColon:
2025-10-01 19:37:14 | - True instances: 7
2025-10-01 19:37:14 | - Correctly predicted: 4
2025-10-01 19:37:14 | - Precision: 0.444
2025-10-01 19:37:14 | - Recall: 0.571
2025-10-01 19:37:14 | TypeErrorArity:
2025-10-01 19:37:14 | - True instances: 6
2025-10-01 19:37:14 | - Correctly predicted: 3
2025-10-01 19:37:14 | - Precision: 0.500
2025-10-01 19:37:14 | - Recall: 0.500
2025-10-01 19:37:14 | TypeErrorBadAdd:
2025-10-01 19:37:14 | - True instances: 1
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | UnboundLocalError:
2025-10-01 19:37:14 | - True instances: 3
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:14 | ZeroDivisionError:
2025-10-01 19:37:14 | - True instances: 1
2025-10-01 19:37:14 | - Correctly predicted: 0
2025-10-01 19:37:14 | - Precision: 0.000
2025-10-01 19:37:14 | - Recall: 0.000
2025-10-01 19:37:38 | Saved learning curve -> artefacts\run_20251001_183629\figures\learning_curve_-_gradient_boosting.png
2025-10-01 19:37:38 | Saved learning curve CSV -> artefacts\run_20251001_183629\tables\learning_curve_learning_curve_-_gradient_boosting.csv

2025-10-01 19:37:38 | === MODEL DIAGNOSTIC ANALYSIS ===
2025-10-01 19:37:38 |  - Total features: 71
2025-10-01 19:37:38 |  - Classes: 13

2025-10-01 19:37:38 | === FEATURE QUALITY DIAGNOSTICS ===
2025-10-01 19:37:38 |  - AST message features: 0
2025-10-01 19:37:38 |  - Code keyword features: 0
2025-10-01 19:37:38 |  - Literal number features: 0

2025-10-01 19:37:38 | === CLASS / FEATURE CONTRIBUTION ANALYSIS ===
2025-10-01 19:37:38 | Model exposes feature_importances_ (global). Top features:
2025-10-01 19:37:38 |    1. f25                                                0.0810
2025-10-01 19:37:38 |    2. f63                                                0.0672
2025-10-01 19:37:38 |    3. f32                                                0.0667
2025-10-01 19:37:38 |    4. f64                                                0.0618
2025-10-01 19:37:38 |    5. f31                                                0.0525
2025-10-01 19:37:38 |    6. f45                                                0.0474
2025-10-01 19:37:38 |    7. f44                                                0.0471
2025-10-01 19:37:38 |    8. f0                                                 0.0431
2025-10-01 19:37:38 |    9. f15                                                0.0417
2025-10-01 19:37:38 |   10. f58                                                0.0402
2025-10-01 19:37:38 | Per-class attributions not inherent; using permutation importance for robustness.
2025-10-01 19:37:38 |   Computing permutation importance on classifier (balanced_accuracy, 3 repeats) with transformed features...
2025-10-01 19:37:38 |   Top permutation features:
2025-10-01 19:37:38 |      1. f2                                                 0.0571
2025-10-01 19:37:38 |      2. f64                                                0.0565
2025-10-01 19:37:38 |      3. f58                                                0.0515
2025-10-01 19:37:38 |      4. f49                                                0.0257
2025-10-01 19:37:38 |      5. f70                                                0.0190
2025-10-01 19:37:38 |      6. f3                                                 0.0156
2025-10-01 19:37:38 |      7. f62                                                0.0091
2025-10-01 19:37:38 |      8. f9                                                 0.0079
2025-10-01 19:37:38 |      9. f26                                                0.0069
2025-10-01 19:37:38 |     10. f31                                                0.0069

2025-10-01 19:37:38 | === PREDICTION CONFIDENCE ANALYSIS ===
2025-10-01 19:37:38 |  - Very high (>0.9): 35/40 (87.5%)
2025-10-01 19:37:38 |  - High (0.7-0.9): 2/40 (5.0%)
2025-10-01 19:37:38 |  - Medium (0.5-0.7): 3/40 (7.5%)
2025-10-01 19:37:38 |  - Low (<0.5): 0/40 (0.0%)
2025-10-01 19:37:38 |   Over-confident examples (up to 3):
2025-10-01 19:37:38 |  - Example 1: Conf=0.999 Pred=TypeErrorArity Actual=TypeErrorArity | feat_arithmetic_ops        0
2025-10-01 19:37:38 | feat_assert_statements     0
2025-10-01 19:37:38 | feat_assignments           3
2025-10-01 19:37:38 | feat_attribute_access      4
2025-10-01 19:37:38 | feat_aug_assignments       0
2025-10-01 19:37:38 |                           ..
2025-10-01 19:37:38 | feat_try_finally_blocks    0
2025-10-01 19:37:38 | feat_try_statements        0
2025-10-01 19:37:38 | feat_while_loops           0
2025-10-01 19:37:38 | feat_with_statements       0
2025-10-01 19:37:38 | feat_word_count            0
2025-10-01 19:37:38 | Name: 0, Length: 71, dtype: int64...
2025-10-01 19:37:38 |  - Example 2: Conf=1.000 Pred=SyntaxErrorMissingColon Actual=SyntaxErrorMissingColon | feat_arithmetic_ops         0
2025-10-01 19:37:38 | feat_assert_statements      0
2025-10-01 19:37:38 | feat_assignments            0
2025-10-01 19:37:38 | feat_attribute_access       0
2025-10-01 19:37:38 | feat_aug_assignments        0
2025-10-01 19:37:38 |                            ..
2025-10-01 19:37:38 | feat_try_finally_blocks     0
2025-10-01 19:37:38 | feat_try_statements         0
2025-10-01 19:37:38 | feat_while_loops            0
2025-10-01 19:37:38 | feat_with_statements        0
2025-10-01 19:37:38 | feat_word_count            48
2025-10-01 19:37:38 | Name: 1, Length: 71, dtype: int64...
2025-10-01 19:37:38 |  - Example 3: Conf=1.000 Pred=SyntaxErrorMismatchedParen Actual=SyntaxErrorMissingColon | feat_arithmetic_ops         0
2025-10-01 19:37:38 | feat_assert_statements      0
2025-10-01 19:37:38 | feat_assignments            0
2025-10-01 19:37:38 | feat_attribute_access       0
2025-10-01 19:37:38 | feat_aug_assignments        0
2025-10-01 19:37:38 |                            ..
2025-10-01 19:37:38 | feat_try_finally_blocks     0
2025-10-01 19:37:38 | feat_try_statements         0
2025-10-01 19:37:38 | feat_while_loops            0
2025-10-01 19:37:38 | feat_with_statements        0
2025-10-01 19:37:38 | feat_word_count            36
2025-10-01 19:37:38 | Name: 2, Length: 71, dtype: int64...

2025-10-01 19:37:38 | === SHAP EXPLAINABILITY (sampled) ===
2025-10-01 19:37:38 |   TreeExplainer not available for this model (GradientBoostingClassifier is only supported for binary classification right now!). 
2025-10-01 19:37:38 |                                 Falling back to permutation-based SHAP.
2025-10-01 19:37:42 |   Saved SHAP token lists -> artefacts\run_20251001_183629\meta\shap_tokens_20251001_183629.json
2025-10-01 19:37:42 |   Generating SHAP visualisations for GradientBoostingClassifier...
2025-10-01 19:37:42 |     SHAP values shape: (40, 71, 13), Feature names: 71
2025-10-01 19:37:42 |  - Summary plot saved -> artefacts\run_20251001_183629\figures\shap_summary_gradientboostingclassifier_20251001_183629.png
2025-10-01 19:37:42 |  - Bar plot saved -> artefacts\run_20251001_183629\figures\shap_bar_gradientboostingclassifier_20251001_183629.png
2025-10-01 19:37:42 |  - Waterfall plot 1 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradientboostingclassifier_sample1_20251001_183629.png
2025-10-01 19:37:42 |  - Waterfall plot 2 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradientboostingclassifier_sample2_20251001_183629.png
2025-10-01 19:37:42 |  - Waterfall plot 3 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradientboostingclassifier_sample3_20251001_183629.png
2025-10-01 19:37:43 |  - Dependence plot 1 saved -> artefacts\run_20251001_183629\figures\shap_dependence_gradientboostingclassifier_feat1_20251001_183629.png
2025-10-01 19:37:43 |  - Dependence plot 2 saved -> artefacts\run_20251001_183629\figures\shap_dependence_gradientboostingclassifier_feat2_20251001_183629.png
2025-10-01 19:37:43 |  - SHAP analysis report saved -> artefacts\run_20251001_183629\meta\shap_analysis_report_gradientboostingclassifier_20251001_183629.json
2025-10-01 19:37:43 | SHAP visualisations complete for GradientBoostingClassifier
2025-10-01 19:37:43 |   Top SHAP global features (mean |SHAP|):
2025-10-01 19:37:43 |      1. f4                                                 0.012395
2025-10-01 19:37:43 |      2. f8                                                 0.010993
2025-10-01 19:37:43 |      3. f7                                                 0.010344
2025-10-01 19:37:43 |      4. f6                                                 0.007487
2025-10-01 19:37:43 |      5. f5                                                 0.007372
2025-10-01 19:37:43 |      6. f3                                                 0.006614
2025-10-01 19:37:43 |      7. f0                                                 0.004737
2025-10-01 19:37:43 |      8. f11                                                0.004576
2025-10-01 19:37:43 |      9. f12                                                0.003590
2025-10-01 19:37:43 |     10. f2                                                 0.003311

2025-10-01 19:37:43 |   Class 'AttributeError' top SHAP features (subset):
2025-10-01 19:37:43 |  - f4                                       0.029422
2025-10-01 19:37:43 |  - f8                                       0.012585
2025-10-01 19:37:43 |  - f7                                       0.008578

2025-10-01 19:37:43 |   Class 'KeyError' top SHAP features (subset):
2025-10-01 19:37:43 |  - f4                                       0.000000
2025-10-01 19:37:43 |  - f8                                       0.000000
2025-10-01 19:37:43 |  - f7                                       0.000000

2025-10-01 19:37:43 |   Class 'LogicErrorComparison' top SHAP features (subset):
2025-10-01 19:37:43 |  - f4                                       0.165160
2025-10-01 19:37:43 |  - f8                                       0.033859
2025-10-01 19:37:43 |  - f7                                       0.062816

2025-10-01 19:37:43 | === COMPARATIVE SHAP ANALYSIS (Top 3 Models) ===
2025-10-01 19:37:43 | Running comparative SHAP analysis for Gradient Boosting (CV: 0.459)...
2025-10-01 19:37:43 |   Generating SHAP analysis for Gradient Boosting...
2025-10-01 19:37:43 |     TreeExplainer failed (This 'Pipeline' has no attribute 'transform'), falling back to KernelExplainer...
2025-10-01 19:37:43 | 
  0%|                                                                                                                                                                                                                                                          | 0/40 [00:00<?, ?it/s]2025-10-01 19:37:45 | 
  2%|######                                                                                                                                                                                                                                            | 1/40 [00:02<01:40,  2.58s/it]2025-10-01 19:37:48 | 
  5%|############1                                                                                                                                                                                                                                     | 2/40 [00:05<01:37,  2.58s/it]2025-10-01 19:37:50 | 
  8%|##################1                                                                                                                                                                                                                               | 3/40 [00:07<01:33,  2.54s/it]2025-10-01 19:37:53 | 
 10%|########################2                                                                                                                                                                                                                         | 4/40 [00:10<01:30,  2.53s/it]2025-10-01 19:37:55 | 
 12%|##############################2                                                                                                                                                                                                                   | 5/40 [00:12<01:27,  2.49s/it]2025-10-01 19:37:58 | 
 15%|####################################3                                                                                                                                                                                                             | 6/40 [00:15<01:25,  2.52s/it]2025-10-01 19:38:00 | 
 18%|##########################################3                                                                                                                                                                                                       | 7/40 [00:17<01:22,  2.50s/it]2025-10-01 19:38:03 | 
 20%|################################################4                                                                                                                                                                                                 | 8/40 [00:20<01:20,  2.52s/it]2025-10-01 19:38:05 | 
 22%|######################################################4                                                                                                                                                                                           | 9/40 [00:22<01:16,  2.48s/it]2025-10-01 19:38:08 | 
 25%|############################################################2                                                                                                                                                                                    | 10/40 [00:25<01:15,  2.50s/it]2025-10-01 19:38:10 | 
 28%|##################################################################2                                                                                                                                                                              | 11/40 [00:27<01:12,  2.49s/it]2025-10-01 19:38:13 | 
 30%|########################################################################3                                                                                                                                                                        | 12/40 [00:30<01:10,  2.51s/it]2025-10-01 19:38:15 | 
 32%|##############################################################################3                                                                                                                                                                  | 13/40 [00:32<01:07,  2.51s/it]2025-10-01 19:38:18 | 
 35%|####################################################################################3                                                                                                                                                            | 14/40 [00:35<01:05,  2.53s/it]2025-10-01 19:38:21 | 
 38%|##########################################################################################3                                                                                                                                                      | 15/40 [00:37<01:03,  2.55s/it]2025-10-01 19:38:23 | 
 40%|################################################################################################4                                                                                                                                                | 16/40 [00:40<01:01,  2.58s/it]2025-10-01 19:38:26 | 
 42%|######################################################################################################4                                                                                                                                          | 17/40 [00:42<00:58,  2.56s/it]2025-10-01 19:38:28 | 
 45%|############################################################################################################4                                                                                                                                    | 18/40 [00:45<00:56,  2.59s/it]2025-10-01 19:38:31 | 
 48%|##################################################################################################################4                                                                                                                              | 19/40 [00:48<00:54,  2.57s/it]2025-10-01 19:38:34 | 
 50%|########################################################################################################################5                                                                                                                        | 20/40 [00:50<00:52,  2.62s/it]2025-10-01 19:38:36 | 
 52%|##############################################################################################################################5                                                                                                                  | 21/40 [00:53<00:48,  2.57s/it]2025-10-01 19:38:39 | 
 55%|####################################################################################################################################5                                                                                                            | 22/40 [00:55<00:45,  2.54s/it]2025-10-01 19:38:41 | 
 57%|##########################################################################################################################################5                                                                                                      | 23/40 [00:58<00:43,  2.55s/it]2025-10-01 19:38:44 | 
 60%|################################################################################################################################################6                                                                                                | 24/40 [01:01<00:40,  2.56s/it]2025-10-01 19:38:46 | 
 62%|######################################################################################################################################################6                                                                                          | 25/40 [01:03<00:38,  2.59s/it]2025-10-01 19:38:49 | 
 65%|############################################################################################################################################################6                                                                                    | 26/40 [01:06<00:36,  2.60s/it]2025-10-01 19:38:52 | 
 68%|##################################################################################################################################################################6                                                                              | 27/40 [01:09<00:34,  2.66s/it]2025-10-01 19:38:54 | 
 70%|########################################################################################################################################################################7                                                                        | 28/40 [01:11<00:31,  2.64s/it]2025-10-01 19:38:57 | 
 72%|##############################################################################################################################################################################7                                                                  | 29/40 [01:14<00:28,  2.63s/it]2025-10-01 19:39:00 | 
 75%|####################################################################################################################################################################################7                                                            | 30/40 [01:16<00:25,  2.58s/it]2025-10-01 19:39:02 | 
 78%|##########################################################################################################################################################################################7                                                      | 31/40 [01:19<00:22,  2.53s/it]2025-10-01 19:39:04 | 
 80%|################################################################################################################################################################################################8                                                | 32/40 [01:21<00:20,  2.53s/it]2025-10-01 19:39:07 | 
 82%|######################################################################################################################################################################################################8                                          | 33/40 [01:24<00:17,  2.50s/it]2025-10-01 19:39:09 | 
 85%|############################################################################################################################################################################################################8                                    | 34/40 [01:26<00:14,  2.49s/it]2025-10-01 19:39:12 | 
 88%|##################################################################################################################################################################################################################8                              | 35/40 [01:28<00:12,  2.44s/it]2025-10-01 19:39:14 | 
 90%|########################################################################################################################################################################################################################9                        | 36/40 [01:31<00:09,  2.44s/it]2025-10-01 19:39:17 | 
 92%|##############################################################################################################################################################################################################################9                  | 37/40 [01:33<00:07,  2.46s/it]2025-10-01 19:39:19 | 
 95%|####################################################################################################################################################################################################################################9            | 38/40 [01:36<00:04,  2.45s/it]2025-10-01 19:39:22 | 
 98%|##########################################################################################################################################################################################################################################9      | 39/40 [01:38<00:02,  2.49s/it]2025-10-01 19:39:24 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [01:41<00:00,  2.45s/it]2025-10-01 19:39:24 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [01:41<00:00,  2.53s/it]
2025-10-01 19:39:24 |   Generating SHAP visualisations for Gradient Boosting...
2025-10-01 19:39:24 |     SHAP values shape: (40, 71, 13), Feature names: 71
2025-10-01 19:39:24 |  - Summary plot saved -> artefacts\run_20251001_183629\figures\shap_summary_gradient_boosting_20251001_183629.png
2025-10-01 19:39:25 |  - Bar plot saved -> artefacts\run_20251001_183629\figures\shap_bar_gradient_boosting_20251001_183629.png
2025-10-01 19:39:25 |  - Waterfall plot 1 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradient_boosting_sample1_20251001_183629.png
2025-10-01 19:39:25 |  - Waterfall plot 2 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradient_boosting_sample2_20251001_183629.png
2025-10-01 19:39:25 |  - Waterfall plot 3 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_gradient_boosting_sample3_20251001_183629.png
2025-10-01 19:39:25 |  - Dependence plot 1 saved -> artefacts\run_20251001_183629\figures\shap_dependence_gradient_boosting_feat1_20251001_183629.png
2025-10-01 19:39:25 |  - Dependence plot 2 saved -> artefacts\run_20251001_183629\figures\shap_dependence_gradient_boosting_feat2_20251001_183629.png
2025-10-01 19:39:25 |  - SHAP analysis report saved -> artefacts\run_20251001_183629\meta\shap_analysis_report_gradient_boosting_20251001_183629.json
2025-10-01 19:39:25 | SHAP visualisations complete for Gradient Boosting
2025-10-01 19:39:25 | SHAP analysis complete for Gradient Boosting
2025-10-01 19:39:25 | Running comparative SHAP analysis for XGBoost (CV: 0.453)...
2025-10-01 19:39:25 |   Generating SHAP analysis for XGBoost...
2025-10-01 19:39:25 | 
  0%|                                                                                                                                                                                                                                                          | 0/40 [00:00<?, ?it/s]2025-10-01 19:39:26 | 
  2%|######                                                                                                                                                                                                                                            | 1/40 [00:00<00:09,  3.96it/s]2025-10-01 19:39:26 | 
  5%|############1                                                                                                                                                                                                                                     | 2/40 [00:00<00:09,  3.87it/s]2025-10-01 19:39:26 | 
  8%|##################1                                                                                                                                                                                                                               | 3/40 [00:00<00:10,  3.52it/s]2025-10-01 19:39:27 | 
 10%|########################2                                                                                                                                                                                                                         | 4/40 [00:01<00:10,  3.47it/s]2025-10-01 19:39:27 | 
 12%|##############################2                                                                                                                                                                                                                   | 5/40 [00:01<00:09,  3.69it/s]2025-10-01 19:39:27 | 
 15%|####################################3                                                                                                                                                                                                             | 6/40 [00:01<00:08,  3.81it/s]2025-10-01 19:39:27 | 
 18%|##########################################3                                                                                                                                                                                                       | 7/40 [00:01<00:08,  3.94it/s]2025-10-01 19:39:27 | 
 20%|################################################4                                                                                                                                                                                                 | 8/40 [00:02<00:08,  3.89it/s]2025-10-01 19:39:28 | 
 22%|######################################################4                                                                                                                                                                                           | 9/40 [00:02<00:07,  4.00it/s]2025-10-01 19:39:28 | 
 25%|############################################################2                                                                                                                                                                                    | 10/40 [00:02<00:07,  4.04it/s]2025-10-01 19:39:28 | 
 28%|##################################################################2                                                                                                                                                                              | 11/40 [00:02<00:07,  4.14it/s]2025-10-01 19:39:28 | 
 30%|########################################################################3                                                                                                                                                                        | 12/40 [00:03<00:06,  4.01it/s]2025-10-01 19:39:29 | 
 32%|##############################################################################3                                                                                                                                                                  | 13/40 [00:03<00:06,  4.04it/s]2025-10-01 19:39:29 | 
 35%|####################################################################################3                                                                                                                                                            | 14/40 [00:03<00:06,  4.02it/s]2025-10-01 19:39:29 | 
 38%|##########################################################################################3                                                                                                                                                      | 15/40 [00:03<00:06,  4.04it/s]2025-10-01 19:39:29 | 
 40%|################################################################################################4                                                                                                                                                | 16/40 [00:04<00:06,  3.93it/s]2025-10-01 19:39:30 | 
 42%|######################################################################################################4                                                                                                                                          | 17/40 [00:04<00:05,  3.98it/s]2025-10-01 19:39:30 | 
 45%|############################################################################################################4                                                                                                                                    | 18/40 [00:04<00:05,  3.96it/s]2025-10-01 19:39:30 | 
 48%|##################################################################################################################4                                                                                                                              | 19/40 [00:04<00:05,  3.92it/s]2025-10-01 19:39:30 | 
 50%|########################################################################################################################5                                                                                                                        | 20/40 [00:05<00:05,  3.91it/s]2025-10-01 19:39:31 | 
 52%|##############################################################################################################################5                                                                                                                  | 21/40 [00:05<00:04,  3.86it/s]2025-10-01 19:39:31 | 
 55%|####################################################################################################################################5                                                                                                            | 22/40 [00:05<00:04,  3.84it/s]2025-10-01 19:39:31 | 
 57%|##########################################################################################################################################5                                                                                                      | 23/40 [00:05<00:04,  3.80it/s]2025-10-01 19:39:32 | 
 60%|################################################################################################################################################6                                                                                                | 24/40 [00:06<00:04,  3.76it/s]2025-10-01 19:39:32 | 
 62%|######################################################################################################################################################6                                                                                          | 25/40 [00:06<00:03,  3.82it/s]2025-10-01 19:39:32 | 
 65%|############################################################################################################################################################6                                                                                    | 26/40 [00:06<00:03,  3.88it/s]2025-10-01 19:39:32 | 
 68%|##################################################################################################################################################################6                                                                              | 27/40 [00:06<00:03,  3.95it/s]2025-10-01 19:39:33 | 
 70%|########################################################################################################################################################################7                                                                        | 28/40 [00:07<00:03,  3.99it/s]2025-10-01 19:39:33 | 
 72%|##############################################################################################################################################################################7                                                                  | 29/40 [00:07<00:02,  3.93it/s]2025-10-01 19:39:33 | 
 75%|####################################################################################################################################################################################7                                                            | 30/40 [00:07<00:02,  3.97it/s]2025-10-01 19:39:33 | 
 78%|##########################################################################################################################################################################################7                                                      | 31/40 [00:07<00:02,  4.00it/s]2025-10-01 19:39:34 | 
 80%|################################################################################################################################################################################################8                                                | 32/40 [00:08<00:02,  3.93it/s]2025-10-01 19:39:34 | 
 82%|######################################################################################################################################################################################################8                                          | 33/40 [00:08<00:01,  4.05it/s]2025-10-01 19:39:34 | 
 85%|############################################################################################################################################################################################################8                                    | 34/40 [00:08<00:01,  3.94it/s]2025-10-01 19:39:34 | 
 88%|##################################################################################################################################################################################################################8                              | 35/40 [00:08<00:01,  3.94it/s]2025-10-01 19:39:35 | 
 90%|########################################################################################################################################################################################################################9                        | 36/40 [00:09<00:01,  3.91it/s]2025-10-01 19:39:35 | 
 92%|##############################################################################################################################################################################################################################9                  | 37/40 [00:09<00:00,  3.90it/s]2025-10-01 19:39:35 | 
 95%|####################################################################################################################################################################################################################################9            | 38/40 [00:09<00:00,  3.94it/s]2025-10-01 19:39:35 | 
 98%|##########################################################################################################################################################################################################################################9      | 39/40 [00:09<00:00,  3.87it/s]2025-10-01 19:39:36 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [00:10<00:00,  3.81it/s]2025-10-01 19:39:36 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [00:10<00:00,  3.90it/s]
2025-10-01 19:39:36 |   Generating SHAP visualisations for XGBoost...
2025-10-01 19:39:36 |     SHAP values shape: (40, 71, 13), Feature names: 71
2025-10-01 19:39:36 |  - Summary plot saved -> artefacts\run_20251001_183629\figures\shap_summary_xgboost_20251001_183629.png
2025-10-01 19:39:36 |  - Bar plot saved -> artefacts\run_20251001_183629\figures\shap_bar_xgboost_20251001_183629.png
2025-10-01 19:39:36 |  - Waterfall plot 1 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_xgboost_sample1_20251001_183629.png
2025-10-01 19:39:36 |  - Waterfall plot 2 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_xgboost_sample2_20251001_183629.png
2025-10-01 19:39:37 |  - Waterfall plot 3 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_xgboost_sample3_20251001_183629.png
2025-10-01 19:39:37 |  - Dependence plot 1 saved -> artefacts\run_20251001_183629\figures\shap_dependence_xgboost_feat1_20251001_183629.png
2025-10-01 19:39:37 |  - Dependence plot 2 saved -> artefacts\run_20251001_183629\figures\shap_dependence_xgboost_feat2_20251001_183629.png
2025-10-01 19:39:37 |  - SHAP analysis report saved -> artefacts\run_20251001_183629\meta\shap_analysis_report_xgboost_20251001_183629.json
2025-10-01 19:39:37 | SHAP visualisations complete for XGBoost
2025-10-01 19:39:37 | SHAP analysis complete for XGBoost
2025-10-01 19:39:37 | Running comparative SHAP analysis for HistGradient Boosting (CV: 0.446)...
2025-10-01 19:39:37 |   Generating SHAP analysis for HistGradient Boosting...
2025-10-01 19:39:37 |     TreeExplainer failed (This 'Pipeline' has no attribute 'transform'), falling back to KernelExplainer...
2025-10-01 19:39:37 | 
  0%|                                                                                                                                                                                                                                                          | 0/40 [00:00<?, ?it/s]2025-10-01 19:39:38 | 
  2%|######                                                                                                                                                                                                                                            | 1/40 [00:00<00:21,  1.79it/s]2025-10-01 19:39:38 | 
  5%|############1                                                                                                                                                                                                                                     | 2/40 [00:01<00:20,  1.81it/s]2025-10-01 19:39:39 | 
  8%|##################1                                                                                                                                                                                                                               | 3/40 [00:01<00:19,  1.87it/s]2025-10-01 19:39:39 | 
 10%|########################2                                                                                                                                                                                                                         | 4/40 [00:02<00:19,  1.85it/s]2025-10-01 19:39:41 | 
 12%|##############################2                                                                                                                                                                                                                   | 5/40 [00:03<00:28,  1.23it/s]2025-10-01 19:39:41 | 
 15%|####################################3                                                                                                                                                                                                             | 6/40 [00:04<00:25,  1.33it/s]2025-10-01 19:39:42 | 
 18%|##########################################3                                                                                                                                                                                                       | 7/40 [00:04<00:23,  1.42it/s]2025-10-01 19:39:42 | 
 20%|################################################4                                                                                                                                                                                                 | 8/40 [00:05<00:21,  1.51it/s]2025-10-01 19:39:43 | 
 22%|######################################################4                                                                                                                                                                                           | 9/40 [00:05<00:19,  1.58it/s]2025-10-01 19:39:43 | 
 25%|############################################################2                                                                                                                                                                                    | 10/40 [00:06<00:18,  1.66it/s]2025-10-01 19:39:44 | 
 28%|##################################################################2                                                                                                                                                                              | 11/40 [00:06<00:16,  1.71it/s]2025-10-01 19:39:45 | 
 30%|########################################################################3                                                                                                                                                                        | 12/40 [00:07<00:16,  1.73it/s]2025-10-01 19:39:45 | 
 32%|##############################################################################3                                                                                                                                                                  | 13/40 [00:08<00:15,  1.73it/s]2025-10-01 19:39:46 | 
 35%|####################################################################################3                                                                                                                                                            | 14/40 [00:08<00:14,  1.75it/s]2025-10-01 19:39:46 | 
 38%|##########################################################################################3                                                                                                                                                      | 15/40 [00:09<00:14,  1.77it/s]2025-10-01 19:39:47 | 
 40%|################################################################################################4                                                                                                                                                | 16/40 [00:09<00:13,  1.76it/s]2025-10-01 19:39:47 | 
 42%|######################################################################################################4                                                                                                                                          | 17/40 [00:10<00:13,  1.74it/s]2025-10-01 19:39:48 | 
 45%|############################################################################################################4                                                                                                                                    | 18/40 [00:10<00:13,  1.69it/s]2025-10-01 19:39:49 | 
 48%|##################################################################################################################4                                                                                                                              | 19/40 [00:11<00:12,  1.71it/s]2025-10-01 19:39:49 | 
 50%|########################################################################################################################5                                                                                                                        | 20/40 [00:12<00:11,  1.72it/s]2025-10-01 19:39:50 | 
 52%|##############################################################################################################################5                                                                                                                  | 21/40 [00:12<00:11,  1.68it/s]2025-10-01 19:39:50 | 
 55%|####################################################################################################################################5                                                                                                            | 22/40 [00:13<00:10,  1.70it/s]2025-10-01 19:39:51 | 
 57%|##########################################################################################################################################5                                                                                                      | 23/40 [00:13<00:09,  1.73it/s]2025-10-01 19:39:52 | 
 60%|################################################################################################################################################6                                                                                                | 24/40 [00:14<00:09,  1.69it/s]2025-10-01 19:39:52 | 
 62%|######################################################################################################################################################6                                                                                          | 25/40 [00:15<00:08,  1.72it/s]2025-10-01 19:39:53 | 
 65%|############################################################################################################################################################6                                                                                    | 26/40 [00:15<00:08,  1.75it/s]2025-10-01 19:39:53 | 
 68%|##################################################################################################################################################################6                                                                              | 27/40 [00:16<00:07,  1.72it/s]2025-10-01 19:39:54 | 
 70%|########################################################################################################################################################################7                                                                        | 28/40 [00:16<00:07,  1.62it/s]2025-10-01 19:39:55 | 
 72%|##############################################################################################################################################################################7                                                                  | 29/40 [00:17<00:06,  1.64it/s]2025-10-01 19:39:55 | 
 75%|####################################################################################################################################################################################7                                                            | 30/40 [00:18<00:05,  1.67it/s]2025-10-01 19:39:56 | 
 78%|##########################################################################################################################################################################################7                                                      | 31/40 [00:18<00:05,  1.65it/s]2025-10-01 19:39:56 | 
 80%|################################################################################################################################################################################################8                                                | 32/40 [00:19<00:04,  1.66it/s]2025-10-01 19:39:57 | 
 82%|######################################################################################################################################################################################################8                                          | 33/40 [00:19<00:04,  1.69it/s]2025-10-01 19:39:57 | 
 85%|############################################################################################################################################################################################################8                                    | 34/40 [00:20<00:03,  1.71it/s]2025-10-01 19:39:58 | 
 88%|##################################################################################################################################################################################################################8                              | 35/40 [00:21<00:02,  1.67it/s]2025-10-01 19:39:59 | 
 90%|########################################################################################################################################################################################################################9                        | 36/40 [00:21<00:02,  1.70it/s]2025-10-01 19:39:59 | 
 92%|##############################################################################################################################################################################################################################9                  | 37/40 [00:22<00:01,  1.73it/s]2025-10-01 19:40:00 | 
 95%|####################################################################################################################################################################################################################################9            | 38/40 [00:22<00:01,  1.74it/s]2025-10-01 19:40:00 | 
 98%|##########################################################################################################################################################################################################################################9      | 39/40 [00:23<00:00,  1.73it/s]2025-10-01 19:40:01 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [00:23<00:00,  1.74it/s]2025-10-01 19:40:01 | 
100%|#################################################################################################################################################################################################################################################| 40/40 [00:23<00:00,  1.67it/s]
2025-10-01 19:40:01 |   Generating SHAP visualisations for HistGradient Boosting...
2025-10-01 19:40:01 |     SHAP values shape: (40, 71, 13), Feature names: 71
2025-10-01 19:40:01 |  - Summary plot saved -> artefacts\run_20251001_183629\figures\shap_summary_histgradient_boosting_20251001_183629.png
2025-10-01 19:40:01 |  - Bar plot saved -> artefacts\run_20251001_183629\figures\shap_bar_histgradient_boosting_20251001_183629.png
2025-10-01 19:40:02 |  - Waterfall plot 1 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_histgradient_boosting_sample1_20251001_183629.png
2025-10-01 19:40:02 |  - Waterfall plot 2 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_histgradient_boosting_sample2_20251001_183629.png
2025-10-01 19:40:02 |  - Waterfall plot 3 saved -> artefacts\run_20251001_183629\figures\shap_waterfall_histgradient_boosting_sample3_20251001_183629.png
2025-10-01 19:40:02 |  - Dependence plot 1 saved -> artefacts\run_20251001_183629\figures\shap_dependence_histgradient_boosting_feat1_20251001_183629.png
2025-10-01 19:40:02 |  - Dependence plot 2 saved -> artefacts\run_20251001_183629\figures\shap_dependence_histgradient_boosting_feat2_20251001_183629.png
2025-10-01 19:40:02 |  - SHAP analysis report saved -> artefacts\run_20251001_183629\meta\shap_analysis_report_histgradient_boosting_20251001_183629.json
2025-10-01 19:40:02 | SHAP visualisations complete for HistGradient Boosting
2025-10-01 19:40:02 | SHAP analysis complete for HistGradient Boosting
2025-10-01 19:40:02 |   Comparative SHAP summary saved -> artefacts\run_20251001_183629\meta\comparative_shap_summary_20251001_183629.json
2025-10-01 19:40:02 |  - Reports found: 3
2025-10-01 19:40:02 |  - Models with visualisations: 3
2025-10-01 19:40:02 | All run outputs available in unified directory -> artefacts\run_20251001_183629

2025-10-01 19:40:02 | Gradient Boosting Feature Analysis:

2025-10-01 19:40:02 | === GRADIENT BOOSTING FEATURE ANALYSIS ===
2025-10-01 19:40:02 | Top 15 Most Important Features (Feature Importances):
2025-10-01 19:40:02 |    1. feat_25                                            0.0810
2025-10-01 19:40:02 |    2. feat_63                                            0.0672
2025-10-01 19:40:02 |    3. feat_32                                            0.0667
2025-10-01 19:40:02 |    4. feat_64                                            0.0618
2025-10-01 19:40:02 |    5. feat_31                                            0.0525
2025-10-01 19:40:02 |    6. feat_45                                            0.0474
2025-10-01 19:40:02 |    7. feat_44                                            0.0471
2025-10-01 19:40:02 |    8. feat_0                                             0.0431
2025-10-01 19:40:02 |    9. feat_15                                            0.0417
2025-10-01 19:40:02 |   10. feat_58                                            0.0402
2025-10-01 19:40:02 |   11. feat_49                                            0.0346
2025-10-01 19:40:02 |   12. feat_3                                             0.0337
2025-10-01 19:40:02 |   13. feat_8                                             0.0318
2025-10-01 19:40:02 |   14. feat_11                                            0.0298
2025-10-01 19:40:02 |   15. feat_55                                            0.0276

2025-10-01 19:40:02 | ============================================================
2025-10-01 19:40:02 | DATASET STATUS SUMMARY:
2025-10-01 19:40:02 | Current size: 199 samples
