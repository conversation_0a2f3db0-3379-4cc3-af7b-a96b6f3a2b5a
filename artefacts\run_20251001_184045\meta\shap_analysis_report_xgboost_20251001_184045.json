{"metadata": {"model_name": "XGBoost", "run_id": "20251001_184045", "timestamp": "2025-10-01T18:51:37.756258+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.03229081103837658, "std_abs_shap": 0.06644852979359683, "max_abs_shap": 0.5451069818123412, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.021567008514034598, "std_abs_shap": 0.06160752930160948, "max_abs_shap": 0.6059554652653308, "feature_index": 58}, {"rank": 3, "feature_name": "feat_line_count", "mean_abs_shap": 0.020956926936967657, "std_abs_shap": 0.042575244759639835, "max_abs_shap": 0.2846354756325236, "feature_index": 45}, {"rank": 4, "feature_name": "feat_colon_count", "mean_abs_shap": 0.013596160848398542, "std_abs_shap": 0.04526977921886484, "max_abs_shap": 0.36778368901561365, "feature_index": 15}, {"rank": 5, "feature_name": "feat_assignments", "mean_abs_shap": 0.013377164978831306, "std_abs_shap": 0.026661581880356267, "max_abs_shap": 0.24124760363965733, "feature_index": 2}, {"rank": 6, "feature_name": "feat_char_count", "mean_abs_shap": 0.012760338202493761, "std_abs_shap": 0.022525122057257322, "max_abs_shap": 0.19428900571784424, "feature_index": 13}, {"rank": 7, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.011679788709428535, "std_abs_shap": 0.024398416205702825, "max_abs_shap": 0.32833818352072175, "feature_index": 0}, {"rank": 8, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.011428638005522416, "std_abs_shap": 0.026559036756188252, "max_abs_shap": 0.19883350980230047, "feature_index": 5}, {"rank": 9, "feature_name": "feat_cyclomatic_complexity", "mean_abs_shap": 0.010667901743997819, "std_abs_shap": 0.02040009491054684, "max_abs_shap": 0.26851717438186984, "feature_index": 19}, {"rank": 10, "feature_name": "feat_if_statements", "mean_abs_shap": 0.00994533611797863, "std_abs_shap": 0.026203149197319007, "max_abs_shap": 0.26239593245051396, "feature_index": 31}, {"rank": 11, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.009378397236804814, "std_abs_shap": 0.025313821992933833, "max_abs_shap": 0.3061231617513469, "feature_index": 16}, {"rank": 12, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.008152572092859424, "std_abs_shap": 0.02702481124088782, "max_abs_shap": 0.2745277542145504, "feature_index": 34}, {"rank": 13, "feature_name": "feat_function_calls", "mean_abs_shap": 0.007106690056067107, "std_abs_shap": 0.015997220903810037, "max_abs_shap": 0.17586103839043662, "feature_index": 25}, {"rank": 14, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.0063548740056253985, "std_abs_shap": 0.014961871256511917, "max_abs_shap": 0.1576057136959562, "feature_index": 44}, {"rank": 15, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.006268512977543673, "std_abs_shap": 0.013301453237187389, "max_abs_shap": 0.12567550063248567, "feature_index": 64}, {"rank": 16, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.004912666700660893, "std_abs_shap": 0.018796563490747074, "max_abs_shap": 0.307145683827533, "feature_index": 62}, {"rank": 17, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.004708108781854943, "std_abs_shap": 0.01471393074407684, "max_abs_shap": 0.19295456648206313, "feature_index": 3}, {"rank": 18, "feature_name": "feat_paren_count", "mean_abs_shap": 0.004220872498796086, "std_abs_shap": 0.014610941826649858, "max_abs_shap": 0.12477271938331085, "feature_index": 56}, {"rank": 19, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.004057774749803672, "std_abs_shap": 0.009585691038154077, "max_abs_shap": 0.07852986166148845, "feature_index": 55}, {"rank": 20, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.0038328833807014114, "std_abs_shap": 0.008405772536038525, "max_abs_shap": 0.07089217221777341, "feature_index": 52}], "total_importance": 0.25166219623790387, "top_10_importance_ratio": 0.6288988869286206}, "feature_statistics": {"mean_importance": 0.0035445379751817448, "median_importance": 0.00040551587221326137, "std_importance": 0.005963106553557743, "max_importance": 0.03229081103837658, "min_importance": 0.0, "importance_concentration": 0.4044631010864474}, "ast_feature_analysis": {"structural_features": [{"name": "feat_leaf_nodes", "importance": 0.0063548740056253985, "rank": 44}, {"name": "feat_tree_depth", "importance": 0.006268512977543673, "rank": 64}, {"name": "feat_nodes_with_children", "importance": 0.004057774749803672, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.0038328833807014114, "rank": 52}, {"name": "feat_total_nodes", "importance": 0.0027903890816928806, "rank": 63}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.00994533611797863, "rank": 31}, {"name": "feat_keyword_if_count", "importance": 0.0035722725687244022, "rank": 37}, {"name": "feat_keyword_for_count", "importance": 0.003509359847823982, "rank": 36}, {"name": "feat_for_loops", "importance": 0.0023461430516032926, "rank": 24}, {"name": "feat_nested_loops", "importance": 0.00040551587221326137, "rank": 54}, {"name": "feat_string_formatting", "importance": 8.03422466256073e-05, "rank": 61}, {"name": "feat_if_else_chains", "importance": 7.388508947900304e-05, "rank": 30}, {"name": "feat_nested_ifs", "importance": 8.66800508043215e-06, "rank": 53}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}, {"name": "feat_try_statements", "importance": 0.0, "rank": 67}, {"name": "feat_while_loops", "importance": 0.0, "rank": 68}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.011428638005522416, "rank": 5}, {"name": "feat_cyclomatic_complexity", "importance": 0.010667901743997819, "rank": 19}, {"name": "feat_max_branching_factor", "importance": 0.0022109807146142584, "rank": 48}, {"name": "feat_avg_function_complexity", "importance": 0.0012651034834668974, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00017686291111906313, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.03229081103837658, "rank": 49}, {"name": "feat_assignments", "importance": 0.013377164978831306, "rank": 2}, {"name": "feat_function_calls", "importance": 0.007106690056067107, "rank": 25}, {"name": "feat_attribute_access", "importance": 0.004708108781854943, "rank": 3}, {"name": "feat_builtin_calls", "importance": 0.0028126213142500383, "rank": 12}, {"name": "feat_aug_assignments", "importance": 0.0006007824107499365, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.021567008514034598, "rank": 58}, {"name": "feat_line_count", "importance": 0.020956926936967657, "rank": 45}, {"name": "feat_colon_count", "importance": 0.013596160848398542, "rank": 15}, {"name": "feat_char_count", "importance": 0.012760338202493761, "rank": 13}, {"name": "feat_arithmetic_ops", "importance": 0.011679788709428535, "rank": 0}, {"name": "feat_comparison_ops", "importance": 0.009378397236804814, "rank": 16}, {"name": "feat_keyword_def_count", "importance": 0.008152572092859424, "rank": 34}, {"name": "feat_subscript_access", "importance": 0.004912666700660893, "rank": 62}, {"name": "feat_paren_count", "importance": 0.004220872498796086, "rank": 56}, {"name": "feat_word_count", "importance": 0.0032136615434097834, "rank": 70}, {"name": "feat_keyword_return_count", "importance": 0.0025318794181734515, "rank": 39}, {"name": "feat_import_statements", "importance": 0.002110929432240838, "rank": 32}, {"name": "feat_max_function_params", "importance": 0.001983880914931024, "rank": 51}, {"name": "feat_keyword_import_count", "importance": 0.0016971101503000401, "rank": 38}, {"name": "feat_equals_count", "importance": 0.0010676040962105157, "rank": 23}, {"name": "feat_bracket_count", "importance": 0.0008113690975076072, "rank": 10}, {"name": "feat_keyword_class_count", "importance": 0.0003228017962016258, "rank": 33}, {"name": "feat_boolean_ops", "importance": 0.0002918611683707776, "rank": 8}, {"name": "feat_lambda_functions", "importance": 0.00022590642669152435, "rank": 42}, {"name": "feat_function_defs", "importance": 0.00014598220600846946, "rank": 26}, {"name": "feat_lambda_usage", "importance": 8.056563024409092e-05, "rank": 43}, {"name": "feat_list_comprehensions", "importance": 3.418845131280888e-05, "rank": 46}, {"name": "feat_class_defs", "importance": 1.4773660754772604e-05, "rank": 14}, {"name": "feat_break_statements", "importance": 1.3328071356226375e-05, "rank": 11}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_brace_count", "importance": 0.0, "rank": 9}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_decorator_usage", "importance": 0.0, "rank": 20}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_generator_expressions", "importance": 0.0, "rank": 27}, {"name": "feat_generator_exps", "importance": 0.0, "rank": 28}, {"name": "feat_has_syntax_error", "importance": 0.0, "rank": 29}, {"name": "feat_list_comps", "importance": 0.0, "rank": 47}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.023304434195367037, "control_flow_features": 0.01994152279952861, "complexity_features": 0.025749486858720454, "syntactic_features": 0.06089617858012991, "other_features": 0.12177057380415786}}}, "visualisations_generated": ["shap_summary_xgboost_20251001_184045.png", "shap_bar_xgboost_20251001_184045.png", "shap_waterfall_xgboost_sample1_20251001_184045.png", "shap_waterfall_xgboost_sample2_20251001_184045.png", "shap_waterfall_xgboost_sample3_20251001_184045.png", "shap_dependence_xgboost_feat1_20251001_184045.png", "shap_dependence_xgboost_feat2_20251001_184045.png"]}