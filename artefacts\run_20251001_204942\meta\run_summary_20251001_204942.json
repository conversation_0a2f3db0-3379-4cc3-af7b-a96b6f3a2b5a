{"run_id": "20251001_204942", "best_model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "training_time_sec_by_model": {"Logistic Regression": 10.947773899999447, "Random Forest": 0.4414414999773726, "SVM (RBF)": 110.20804729999509, "SVM (Linear)": 335.38286310003605, "Naive Bayes": 0.10853069997392595, "Neural Network": 26.28638609999325, "Gradient Boosting": 30.074450699961744, "K-Nearest Neighbors": 0.05591390002518892, "Decision Tree": 0.19228479999583215, "XGBoost": 5.188429899979383, "HistGradient Boosting": 12.594091900042258}, "calibration_plots": {"pre": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting.png", "post": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting_(calibrated).png", "comparison": "artefacts\\run_20251001_204942\\figures\\calibration_reliability_curve_-_histgradient_boosting_(pre_vs_post).png"}, "metrics_json": "artefacts\\run_20251001_204942\\metrics\\metrics_20251001_204942.json"}