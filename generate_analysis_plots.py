#!/usr/bin/env python3
"""
Generate comprehensive analytical figures from the 5 experimental runs.
This script creates publication-ready plots for dissertation analysis.
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for publication-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")
plt.rcParams.update({
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16
})

def load_run_data():
    """Load data from all 5 experimental runs."""
    runs = [
        '20251001_183629',  # Run 1: 200 samples, original classes
        '20251001_184045',  # Run 2: 1K samples, grouped classes
        '20251001_190703',  # Run 3: 5K samples, grouped classes
        '20251001_204942',  # Run 4: 5K samples, grouped + NoError
        '20251001_212858'   # Run 5: 12K samples, grouped classes
    ]
    
    data = []
    for run_id in runs:
        metrics_path = f'artefacts/run_{run_id}/metrics/metrics_{run_id}.json'
        try:
            with open(metrics_path, 'r') as f:
                metrics = json.load(f)
            
            # Extract key information
            run_info = {
                'run_id': run_id,
                'dataset_size': len(metrics['selection']['cv_means']),
                'grouping': metrics['experiment']['grouping'],
                'include_no_error': metrics['experiment']['include_no_error'],
                'cv_means': metrics['selection']['cv_means'],
                'cv_stds': metrics['selection']['cv_stds'],
                'training_times': metrics.get('efficiency', {}).get('training_time_sec_by_model', {}),
                'best_model': metrics.get('best_model_name', 'Unknown')
            }
            data.append(run_info)
        except Exception as e:
            print(f"Error loading {run_id}: {e}")
    
    return data

def create_learning_curve_plot(data):
    """Create learning curve showing performance vs dataset size."""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Define dataset sizes and configurations
    configs = [
        {'size': 200, 'config': 'Original Classes\n(15 types)', 'run': 1},
        {'size': 1000, 'config': 'Grouped Classes\n(11 types)', 'run': 2},
        {'size': 5000, 'config': 'Grouped Classes\n(11 types)', 'run': 3},
        {'size': 5000, 'config': 'Grouped + NoError\n(12 types)', 'run': 4},
        {'size': 11977, 'config': 'Grouped Classes\n(11 types)', 'run': 5}
    ]
    
    # Extract performance for top 3 models across runs
    models = ['HistGradient Boosting', 'XGBoost', 'Gradient Boosting', 'Neural Network']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    for i, model in enumerate(models):
        sizes = []
        accuracies = []
        errors = []
        
        for j, run_data in enumerate(data):
            if model in run_data['cv_means']:
                sizes.append(configs[j]['size'])
                accuracies.append(run_data['cv_means'][model] * 100)
                errors.append(run_data['cv_stds'][model] * 100)
        
        if sizes:  # Only plot if model has data
            ax.errorbar(sizes, accuracies, yerr=errors, 
                       label=model, marker='o', linewidth=2.5, 
                       markersize=8, capsize=5, color=colors[i])
    
    ax.set_xlabel('Dataset Size (samples)', fontweight='bold')
    ax.set_ylabel('Cross-Validation Accuracy (%)', fontweight='bold')
    ax.set_title('Learning Curves: Performance vs Dataset Size\nAcross 5 Experimental Runs', 
                fontweight='bold', pad=20)
    ax.legend(loc='lower right', frameon=True, fancybox=True, shadow=True)
    ax.grid(True, alpha=0.3)
    ax.set_xscale('log')
    
    # Add configuration annotations
    for j, config in enumerate(configs):
        if j < len(data):
            ax.annotate(f"Run {config['run']}\n{config['config']}", 
                       xy=(config['size'], 20 + j*5), 
                       fontsize=9, ha='center', 
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('learning_curves_comprehensive.png', dpi=300, bbox_inches='tight')
    plt.savefig('learning_curves_comprehensive.pdf', bbox_inches='tight')
    print("✅ Generated: learning_curves_comprehensive.png/pdf")

def create_ablation_analysis_plot(data):
    """Create ablation study analysis plot."""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Effect of Error Type Grouping (Run 1 vs Run 2)
    run1_acc = data[0]['cv_means']['Gradient Boosting'] * 100  # Original classes
    run2_acc = data[1]['cv_means']['Gradient Boosting'] * 100  # Grouped classes
    
    ax1.bar(['Original Classes\n(15 types)', 'Grouped Classes\n(11 types)'], 
            [run1_acc, run2_acc], color=['lightcoral', 'lightblue'])
    ax1.set_ylabel('CV Accuracy (%)')
    ax1.set_title('Effect of Error Type Grouping\n(1K samples, Gradient Boosting)')
    ax1.text(0, run1_acc + 1, f'{run1_acc:.1f}%', ha='center', fontweight='bold')
    ax1.text(1, run2_acc + 1, f'{run2_acc:.1f}%', ha='center', fontweight='bold')
    improvement = run2_acc - run1_acc
    ax1.text(0.5, max(run1_acc, run2_acc) + 5, f'+{improvement:.1f}%', 
             ha='center', fontweight='bold', color='green', fontsize=14)
    
    # 2. Effect of NoError Class (Run 3 vs Run 4)
    run3_acc = data[2]['cv_means']['XGBoost'] * 100  # Without NoError
    run4_acc = data[3]['cv_means']['HistGradient Boosting'] * 100  # With NoError
    
    ax2.bar(['Errors Only\n(11 types)', 'Errors + NoError\n(12 types)'], 
            [run3_acc, run4_acc], color=['lightgreen', 'orange'])
    ax2.set_ylabel('CV Accuracy (%)')
    ax2.set_title('Effect of NoError Class Inclusion\n(5K samples)')
    ax2.text(0, run3_acc + 1, f'{run3_acc:.1f}%', ha='center', fontweight='bold')
    ax2.text(1, run4_acc + 1, f'{run4_acc:.1f}%', ha='center', fontweight='bold')
    decrease = run3_acc - run4_acc
    ax2.text(0.5, max(run3_acc, run4_acc) + 3, f'-{decrease:.1f}%', 
             ha='center', fontweight='bold', color='red', fontsize=14)
    
    # 3. Dataset Size Effect (Runs 2, 3, 5)
    sizes = [1000, 5000, 11977]
    best_accs = [
        data[1]['cv_means']['Gradient Boosting'] * 100,  # Run 2
        data[2]['cv_means']['XGBoost'] * 100,             # Run 3
        data[4]['cv_means']['HistGradient Boosting'] * 100  # Run 5
    ]
    
    ax3.plot(sizes, best_accs, 'o-', linewidth=3, markersize=10, color='purple')
    ax3.set_xlabel('Dataset Size (samples)')
    ax3.set_ylabel('Best CV Accuracy (%)')
    ax3.set_title('Dataset Size Effect\n(Grouped Classes, Errors Only)')
    ax3.set_xscale('log')
    ax3.grid(True, alpha=0.3)
    for i, (size, acc) in enumerate(zip(sizes, best_accs)):
        ax3.annotate(f'{acc:.1f}%', (size, acc), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontweight='bold')
    
    # 4. Model Performance Comparison (Run 5 - Full Dataset)
    final_run = data[4]
    models = ['HistGradient Boosting', 'XGBoost', 'Neural Network', 'Random Forest', 'Gradient Boosting']
    accuracies = [final_run['cv_means'][model] * 100 for model in models if model in final_run['cv_means']]
    model_names = [model for model in models if model in final_run['cv_means']]
    
    bars = ax4.barh(model_names, accuracies, color=sns.color_palette("viridis", len(model_names)))
    ax4.set_xlabel('CV Accuracy (%)')
    ax4.set_title('Final Model Comparison\n(11,977 samples, 11 classes)')
    for i, (bar, acc) in enumerate(zip(bars, accuracies)):
        ax4.text(acc + 0.5, i, f'{acc:.1f}%', va='center', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('ablation_analysis_comprehensive.png', dpi=300, bbox_inches='tight')
    plt.savefig('ablation_analysis_comprehensive.pdf', bbox_inches='tight')
    print("✅ Generated: ablation_analysis_comprehensive.png/pdf")

def create_computational_efficiency_plot(data):
    """Create computational efficiency analysis."""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Training time comparison for Run 5 (full dataset)
    final_run = data[4]
    if not final_run['training_times']:
        print("⚠️  No training time data available, skipping computational efficiency plot")
        return

    models = list(final_run['training_times'].keys())
    times = list(final_run['training_times'].values())
    accuracies = [final_run['cv_means'][model] * 100 for model in models if model in final_run['cv_means']]

    # Filter to only models that have both time and accuracy data
    valid_data = [(model, final_run['training_times'][model], final_run['cv_means'][model] * 100)
                  for model in models if model in final_run['cv_means']]

    if not valid_data:
        print("⚠️  No valid training time/accuracy pairs, skipping computational efficiency plot")
        return

    # Sort by accuracy for better visualization
    sorted_data = sorted(valid_data, key=lambda x: x[2], reverse=True)
    models_sorted, times_sorted, accs_sorted = zip(*sorted_data)
    
    # Training time bar plot
    bars = ax1.barh(models_sorted, times_sorted, color=plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(models_sorted))))
    ax1.set_xlabel('Training Time (seconds)')
    ax1.set_title('Training Time by Model\n(11,977 samples)')
    ax1.set_xscale('log')
    for i, (bar, time) in enumerate(zip(bars, times_sorted)):
        ax1.text(time * 1.1, i, f'{time:.1f}s', va='center', fontweight='bold')
    
    # Accuracy vs Training Time scatter
    ax2.scatter(times_sorted, accs_sorted, s=100, alpha=0.7, c=range(len(models_sorted)), cmap='viridis')
    for i, (model, time, acc) in enumerate(zip(models_sorted, times_sorted, accs_sorted)):
        ax2.annotate(model.replace(' ', '\n'), (time, acc), 
                    textcoords="offset points", xytext=(5,5), 
                    fontsize=9, ha='left')
    ax2.set_xlabel('Training Time (seconds)')
    ax2.set_ylabel('CV Accuracy (%)')
    ax2.set_title('Accuracy vs Training Time Trade-off')
    ax2.set_xscale('log')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('computational_efficiency_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('computational_efficiency_analysis.pdf', bbox_inches='tight')
    print("✅ Generated: computational_efficiency_analysis.png/pdf")

def create_model_stability_plot(data):
    """Create model stability analysis across runs."""
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Track models across runs where they appear
    models = ['HistGradient Boosting', 'XGBoost', 'Gradient Boosting', 'Neural Network', 'Random Forest']
    run_labels = ['Run 1\n(200)', 'Run 2\n(1K)', 'Run 3\n(5K)', 'Run 4\n(5K+NoErr)', 'Run 5\n(12K)']
    
    for i, model in enumerate(models):
        accuracies = []
        errors = []
        run_indices = []
        
        for j, run_data in enumerate(data):
            if model in run_data['cv_means']:
                accuracies.append(run_data['cv_means'][model] * 100)
                errors.append(run_data['cv_stds'][model] * 100)
                run_indices.append(j)
        
        if accuracies:
            ax.errorbar(run_indices, accuracies, yerr=errors, 
                       label=model, marker='o', linewidth=2, 
                       markersize=8, capsize=5)
    
    ax.set_xlabel('Experimental Run')
    ax.set_ylabel('Cross-Validation Accuracy (%)')
    ax.set_title('Model Performance Stability Across Experimental Runs', fontweight='bold', pad=20)
    ax.set_xticks(range(len(run_labels)))
    ax.set_xticklabels(run_labels)
    ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('model_stability_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('model_stability_analysis.pdf', bbox_inches='tight')
    print("✅ Generated: model_stability_analysis.png/pdf")

def main():
    """Generate all analytical plots."""
    print("🔍 Loading experimental data from 5 runs...")
    data = load_run_data()
    
    if len(data) != 5:
        print(f"⚠️  Warning: Expected 5 runs, found {len(data)}")
        return
    
    print("📊 Generating analytical plots...")
    
    # Create output directory
    Path('analysis_plots').mkdir(exist_ok=True)
    
    # Generate all plots
    create_learning_curve_plot(data)
    create_ablation_analysis_plot(data)
    create_computational_efficiency_plot(data)
    create_model_stability_plot(data)
    
    print("\n✅ All analytical plots generated successfully!")
    print("📁 Files saved in current directory:")
    print("   - learning_curves_comprehensive.png/pdf")
    print("   - ablation_analysis_comprehensive.png/pdf") 
    print("   - computational_efficiency_analysis.png/pdf")
    print("   - model_stability_analysis.png/pdf")

if __name__ == "__main__":
    main()
