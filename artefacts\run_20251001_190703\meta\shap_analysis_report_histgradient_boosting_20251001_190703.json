{"metadata": {"model_name": "<PERSON>t<PERSON><PERSON><PERSON> Boosting", "run_id": "20251001_190703", "timestamp": "2025-10-01T19:16:51.329477+00:00", "n_samples": 150, "n_features": 71, "shap_values_shape": [100, 71, 11], "analysis_type": "multi_class"}, "global_importance": {"top_20_features": [{"rank": 1, "feature_name": "feat_max_call_args", "mean_abs_shap": 0.04617316305433238, "std_abs_shap": 0.0827620510929674, "max_abs_shap": 0.5368746213388456, "feature_index": 49}, {"rank": 2, "feature_name": "feat_return_statements", "mean_abs_shap": 0.03359480253017958, "std_abs_shap": 0.09339731943651705, "max_abs_shap": 0.7707680534757001, "feature_index": 58}, {"rank": 3, "feature_name": "feat_line_count", "mean_abs_shap": 0.022384294859978896, "std_abs_shap": 0.04625847019877869, "max_abs_shap": 0.34730249842020666, "feature_index": 45}, {"rank": 4, "feature_name": "feat_keyword_def_count", "mean_abs_shap": 0.01817247034568227, "std_abs_shap": 0.04694148528122511, "max_abs_shap": 0.3848202149158353, "feature_index": 34}, {"rank": 5, "feature_name": "feat_assignments", "mean_abs_shap": 0.01458330417372964, "std_abs_shap": 0.02824353663292436, "max_abs_shap": 0.18033053119401382, "feature_index": 2}, {"rank": 6, "feature_name": "feat_avg_branching_factor", "mean_abs_shap": 0.014117797669283507, "std_abs_shap": 0.03234600028684226, "max_abs_shap": 0.2642369308486968, "feature_index": 5}, {"rank": 7, "feature_name": "feat_comparison_ops", "mean_abs_shap": 0.013804629120882778, "std_abs_shap": 0.03415872976650459, "max_abs_shap": 0.4290431666142409, "feature_index": 16}, {"rank": 8, "feature_name": "feat_if_statements", "mean_abs_shap": 0.011547986005654804, "std_abs_shap": 0.03197642324932604, "max_abs_shap": 0.29836839796852355, "feature_index": 31}, {"rank": 9, "feature_name": "feat_arithmetic_ops", "mean_abs_shap": 0.011130600058181407, "std_abs_shap": 0.03158055008532904, "max_abs_shap": 0.47563061391755285, "feature_index": 0}, {"rank": 10, "feature_name": "feat_tree_depth", "mean_abs_shap": 0.010168457572752577, "std_abs_shap": 0.021772853245814662, "max_abs_shap": 0.20801944920193283, "feature_index": 64}, {"rank": 11, "feature_name": "feat_leaf_nodes", "mean_abs_shap": 0.009664483399839949, "std_abs_shap": 0.017411891182390133, "max_abs_shap": 0.13432105030766303, "feature_index": 44}, {"rank": 12, "feature_name": "feat_subscript_access", "mean_abs_shap": 0.008443630799932815, "std_abs_shap": 0.027666155542566986, "max_abs_shap": 0.4794144133124162, "feature_index": 62}, {"rank": 13, "feature_name": "feat_colon_count", "mean_abs_shap": 0.007853431807223853, "std_abs_shap": 0.03155235803352256, "max_abs_shap": 0.39599905981492994, "feature_index": 15}, {"rank": 14, "feature_name": "feat_function_calls", "mean_abs_shap": 0.007833821386614355, "std_abs_shap": 0.020130344712441236, "max_abs_shap": 0.2122636892254864, "feature_index": 25}, {"rank": 15, "feature_name": "feat_total_nodes", "mean_abs_shap": 0.006942102921028912, "std_abs_shap": 0.016622386953300212, "max_abs_shap": 0.17027002598750665, "feature_index": 63}, {"rank": 16, "feature_name": "feat_nodes_with_children", "mean_abs_shap": 0.006498905482213821, "std_abs_shap": 0.014617183341001419, "max_abs_shap": 0.11328900107536219, "feature_index": 55}, {"rank": 17, "feature_name": "feat_builtin_calls", "mean_abs_shap": 0.005554992417496083, "std_abs_shap": 0.016678895552923012, "max_abs_shap": 0.20129114539493032, "feature_index": 12}, {"rank": 18, "feature_name": "feat_max_branching_factor", "mean_abs_shap": 0.005296546878798, "std_abs_shap": 0.012490666422520193, "max_abs_shap": 0.13568646859476738, "feature_index": 48}, {"rank": 19, "feature_name": "feat_max_nesting_depth", "mean_abs_shap": 0.00487286429190539, "std_abs_shap": 0.010462381613253703, "max_abs_shap": 0.07304503314642982, "feature_index": 52}, {"rank": 20, "feature_name": "feat_attribute_access", "mean_abs_shap": 0.00447434041757575, "std_abs_shap": 0.014419182861101961, "max_abs_shap": 0.26419010646968505, "feature_index": 3}], "total_importance": 0.28857662359636516, "top_10_importance_ratio": 0.678078158071299}, "feature_statistics": {"mean_importance": 0.0040644594872727485, "median_importance": 0.00035441330876157754, "std_importance": 0.00788709485727397, "max_importance": 0.04617316305433238, "min_importance": 0.0, "importance_concentration": 0.4674946753573495}, "ast_feature_analysis": {"structural_features": [{"name": "feat_tree_depth", "importance": 0.010168457572752577, "rank": 64}, {"name": "feat_leaf_nodes", "importance": 0.009664483399839949, "rank": 44}, {"name": "feat_total_nodes", "importance": 0.006942102921028912, "rank": 63}, {"name": "feat_nodes_with_children", "importance": 0.006498905482213821, "rank": 55}, {"name": "feat_max_nesting_depth", "importance": 0.00487286429190539, "rank": 52}], "control_flow_features": [{"name": "feat_if_statements", "importance": 0.011547986005654804, "rank": 31}, {"name": "feat_keyword_for_count", "importance": 0.0021194273643598176, "rank": 36}, {"name": "feat_for_loops", "importance": 0.0012110810747366489, "rank": 24}, {"name": "feat_if_else_chains", "importance": 0.0006838318903813076, "rank": 30}, {"name": "feat_keyword_if_count", "importance": 0.0005498288740683935, "rank": 37}, {"name": "feat_string_formatting", "importance": 0.0002654669178641631, "rank": 61}, {"name": "feat_nested_loops", "importance": 7.407267194360204e-05, "rank": 54}, {"name": "feat_nested_ifs", "importance": 4.2144259513438864e-05, "rank": 53}, {"name": "feat_while_loops", "importance": 1.756701811997381e-05, "rank": 68}, {"name": "feat_try_statements", "importance": 9.739948310943693e-07, "rank": 67}, {"name": "feat_bare_except", "importance": 0.0, "rank": 7}, {"name": "feat_keyword_except_count", "importance": 0.0, "rank": 35}, {"name": "feat_keyword_try_count", "importance": 0.0, "rank": 40}, {"name": "feat_keyword_while_count", "importance": 0.0, "rank": 41}, {"name": "feat_try_except_blocks", "importance": 0.0, "rank": 65}, {"name": "feat_try_finally_blocks", "importance": 0.0, "rank": 66}], "complexity_features": [{"name": "feat_avg_branching_factor", "importance": 0.014117797669283507, "rank": 5}, {"name": "feat_max_branching_factor", "importance": 0.005296546878798, "rank": 48}, {"name": "feat_cyclomatic_complexity", "importance": 0.002971323414454776, "rank": 19}, {"name": "feat_avg_function_complexity", "importance": 0.0021222950357015955, "rank": 6}, {"name": "feat_max_function_complexity", "importance": 0.00015463654429846916, "rank": 50}], "syntactic_features": [{"name": "feat_max_call_args", "importance": 0.04617316305433238, "rank": 49}, {"name": "feat_assignments", "importance": 0.01458330417372964, "rank": 2}, {"name": "feat_function_calls", "importance": 0.007833821386614355, "rank": 25}, {"name": "feat_builtin_calls", "importance": 0.005554992417496083, "rank": 12}, {"name": "feat_attribute_access", "importance": 0.00447434041757575, "rank": 3}, {"name": "feat_aug_assignments", "importance": 0.00035441330876157754, "rank": 4}], "other_features": [{"name": "feat_return_statements", "importance": 0.03359480253017958, "rank": 58}, {"name": "feat_line_count", "importance": 0.022384294859978896, "rank": 45}, {"name": "feat_keyword_def_count", "importance": 0.01817247034568227, "rank": 34}, {"name": "feat_comparison_ops", "importance": 0.013804629120882778, "rank": 16}, {"name": "feat_arithmetic_ops", "importance": 0.011130600058181407, "rank": 0}, {"name": "feat_subscript_access", "importance": 0.008443630799932815, "rank": 62}, {"name": "feat_colon_count", "importance": 0.007853431807223853, "rank": 15}, {"name": "feat_char_count", "importance": 0.003928586068052914, "rank": 13}, {"name": "feat_import_statements", "importance": 0.0030442792267284333, "rank": 32}, {"name": "feat_max_function_params", "importance": 0.0020877351431189727, "rank": 51}, {"name": "feat_equals_count", "importance": 0.0010772745648168713, "rank": 23}, {"name": "feat_word_count", "importance": 0.0010426404269993535, "rank": 70}, {"name": "feat_boolean_ops", "importance": 0.0009324541494029992, "rank": 8}, {"name": "feat_paren_count", "importance": 0.0007856538070469885, "rank": 56}, {"name": "feat_decorator_usage", "importance": 0.00043830132657395174, "rank": 20}, {"name": "feat_lambda_functions", "importance": 0.00042051270471808435, "rank": 42}, {"name": "feat_keyword_import_count", "importance": 0.0002461925254749499, "rank": 38}, {"name": "feat_brace_count", "importance": 0.0001742839489922894, "rank": 9}, {"name": "feat_function_defs", "importance": 0.00016118838689838607, "rank": 26}, {"name": "feat_keyword_return_count", "importance": 0.00015969764330909505, "rank": 39}, {"name": "feat_bracket_count", "importance": 0.00015489942722583805, "rank": 10}, {"name": "feat_list_comprehensions", "importance": 0.00014537820114217742, "rank": 46}, {"name": "feat_keyword_class_count", "importance": 3.5779826336759155e-05, "rank": 33}, {"name": "feat_class_defs", "importance": 2.8217059207373736e-05, "rank": 14}, {"name": "feat_generator_expressions", "importance": 1.1915920105727444e-05, "rank": 27}, {"name": "feat_has_syntax_error", "importance": 1.1001003618276301e-05, "rank": 29}, {"name": "feat_generator_exps", "importance": 1.0944674274040145e-05, "rank": 28}, {"name": "feat_assert_statements", "importance": 0.0, "rank": 1}, {"name": "feat_break_statements", "importance": 0.0, "rank": 11}, {"name": "feat_context_managers", "importance": 0.0, "rank": 17}, {"name": "feat_continue_statements", "importance": 0.0, "rank": 18}, {"name": "feat_dict_comprehensions", "importance": 0.0, "rank": 21}, {"name": "feat_dict_comps", "importance": 0.0, "rank": 22}, {"name": "feat_lambda_usage", "importance": 0.0, "rank": 43}, {"name": "feat_list_comps", "importance": 0.0, "rank": 47}, {"name": "feat_raise_statements", "importance": 0.0, "rank": 57}, {"name": "feat_semicolon_count", "importance": 0.0, "rank": 59}, {"name": "feat_set_comps", "importance": 0.0, "rank": 60}, {"name": "feat_with_statements", "importance": 0.0, "rank": 69}], "summary": {"most_important_category": "other_features", "category_importance_totals": {"structural_features": 0.03814681366774065, "control_flow_features": 0.016512380071473244, "complexity_features": 0.024662599542536346, "syntactic_features": 0.07897403475850978, "other_features": 0.13028079555610508}}}, "visualisations_generated": ["shap_summary_histgradient_boosting_20251001_190703.png", "shap_bar_histgradient_boosting_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample1_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample2_20251001_190703.png", "shap_waterfall_histgradient_boosting_sample3_20251001_190703.png", "shap_dependence_histgradient_boosting_feat1_20251001_190703.png", "shap_dependence_histgradient_boosting_feat2_20251001_190703.png"]}