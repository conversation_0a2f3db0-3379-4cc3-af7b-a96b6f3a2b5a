"""Calibration analysis utilities"""
import numpy as np
from sklearn.calibration import calibration_curve
from sklearn.metrics import brier_score_loss


def get_classes_safe(pipeline):
    """Safely extract classes from a model or pipeline."""
    try:
        if hasattr(pipeline, "classes_"):
            return pipeline.classes_
        elif hasattr(pipeline, "named_steps") and "classifier" in pipeline.named_steps:
            clf = pipeline.named_steps["classifier"]
            if hasattr(clf, "classes_"):
                return clf.classes_
        return None
    except Exception: #pylint: disable=broad-except
        return None


def calibration_analysis(pipeline, X_test, y_test, bins=10):
    """Perform comprehensive calibration analysis with ECE, MCE, and Brier decomposition."""
    try:
        if not hasattr(pipeline, "predict_proba"):
            # Try to get predict_proba from classifier step if pipeline
            if (
                hasattr(pipeline, "named_steps")
                and "classifier" in pipeline.named_steps
            ):
                clf = pipeline.named_steps["classifier"]
                if not hasattr(clf, "predict_proba"):
                    return None
                proba = clf.predict_proba(
                    pipeline.named_steps.get("tfidf").transform(X_test)
                    )
            else:
                return None
        else:
            proba = pipeline.predict_proba(X_test)

        conf = np.max(proba, axis=1)
        y_pred = np.argmax(proba, axis=1)

        # Obtain classes safely
        classes = get_classes_safe(pipeline)
        if classes is None:
            return None
        correct = classes[y_pred] == np.array(y_test)

        # Build uniform bins on confidence for ECE/MCE and Brier decomposition
        conf_arr = np.asarray(conf, dtype=float) # Confidence as float array
        corr_arr = np.asarray(correct, dtype=bool) # Correctness as bool array
        N = float(len(conf_arr)) if len(conf_arr) > 0 else 1.0 # avoid div by 0
        bin_edges = np.linspace(0.0, 1.0, int(bins) + 1)

        # assign bins; include edge case conf==1.0 to last bin
        bin_idx = np.minimum(
            np.digitize(conf_arr, bin_edges[:-1], right=False) - 1, int(bins) - 1
        )

        bin_counts = []
        bin_acc = []
        bin_conf = []
        for b in range(int(bins)):
            mask = bin_idx == b
            n_k = int(np.sum(mask))
            if n_k == 0:
                bin_counts.append(0)
                bin_acc.append(np.nan)
                bin_conf.append(np.nan)
                continue
            conf_k = float(np.mean(conf_arr[mask]))
            acc_k = float(np.mean(corr_arr[mask].astype(float)))
            bin_counts.append(n_k)
            bin_conf.append(conf_k)
            bin_acc.append(acc_k)

        # Filter non-empty bins for plotting
        non_empty = [i for i, c in enumerate(bin_counts) if c > 0]
        plot_mean_pred = [bin_conf[i] for i in non_empty]
        plot_frac_pos = [bin_acc[i] for i in non_empty]

        # ECE/MCE
        if np.sum(bin_counts) > 0:
            weights = np.array(bin_counts, dtype=float) / N
            diffs = np.abs(
                np.array(bin_acc, dtype=float) - np.array(bin_conf, dtype=float)
            )
            # treat empty bins' diffs as 0 weight via weights=0
            diffs = np.nan_to_num(diffs, nan=0.0)
            ece = float(np.sum(weights * diffs))
            mce = float(np.nanmax(diffs)) if np.any(~np.isnan(diffs)) else None
        else:
            ece, mce = None, None

        # Brier score and Murphy decomposition (reliability, resolution, uncertainty)
        frac_pos, mean_pred = calibration_curve(
            corr_arr.astype(int), conf_arr, n_bins=int(bins), strategy="uniform"
        )
        brier = brier_score_loss(corr_arr.astype(int), conf_arr)

        # Using the bins for decomposition
        o_bar = float(np.mean(corr_arr.astype(float))) if len(corr_arr) > 0 else 0.0
        reliability = 0.0
        resolution = 0.0
        for i, n_k in enumerate(bin_counts):
            if n_k <= 0:
                continue
            w = n_k / N
            o_k = bin_acc[i]
            f_k = bin_conf[i]
            reliability += w * (o_k - f_k) ** 2
            resolution += w * (o_k - o_bar) ** 2
        uncertainty = o_bar * (1.0 - o_bar)

        reliability = float(reliability)
        resolution = float(resolution)
        uncertainty = float(uncertainty)

        return {
            "calibration_frac_pos": frac_pos.tolist(),
            "calibration_mean_pred": mean_pred.tolist(),
            "brier_score": float(brier),
            "ece": (float(ece) if ece is not None else None),
            "mce": (float(mce) if mce is not None else None),
            "brier_reliability": reliability,
            "brier_resolution": resolution,
            "brier_uncertainty": uncertainty,
            "bins": int(bins),
            "bin_edges": bin_edges.tolist(),
            "bin_counts": bin_counts,
            "bin_acc": [None if np.isnan(v) else float(v) for v in bin_acc],
            "bin_conf": [None if np.isnan(v) else float(v) for v in bin_conf],
            "plot_frac_pos": plot_frac_pos,
            "plot_mean_pred": plot_mean_pred,
        }
    except (ValueError, AttributeError, RuntimeError) as e:
        return {"error": str(e)}
