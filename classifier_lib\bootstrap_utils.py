"""Bootstrap confidence interval utilities"""

import numpy as np


def bootstrap_ci(y_true, y_pred, metric_func, n_iter=200, random_state=25):
    """Compute bootstrap confidence intervals for a metric."""
    rng = np.random.default_rng(random_state)
    scores = []
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    n = len(y_true)
    for _ in range(n_iter):
        idx = rng.integers(0, n, size=n)
        scores.append(metric_func(y_true[idx], y_pred[idx]))
    return float(np.percentile(scores, 2.5)), float(np.percentile(scores, 97.5))
