"""Model evaluation utilities for comprehensive ML pipeline"""
import time
from typing import Optional, Any, Dict
from pathlib import Path
from sklearn.preprocessing import LabelBinarizer
from sklearn.metrics import precision_recall_curve, average_precision_score
from sklearn.metrics import accuracy_score, f1_score, balanced_accuracy_score
from sklearn.calibration import CalibratedClassifierCV
from classifier_lib.bootstrap_utils import bootstrap_ci
from classifier_lib.calibration_utils import calibration_analysis
from classifier_lib.output import (
    plot_per_class_pr_curves,
    plot_reliability_curve,
    plot_reliability_curve_comparison,
)
from classifier_lib.config import get_effective_n_jobs



def evaluate_model_on_test(
    best_model,
    X_test,
    y_test,
    model_name: str,
    cfg: Dict[str, Any],
    subdirs: Dict[str, Path],
) -> Dict[str, Any]:
    """Evaluate the best model on test set with inference timing and optional PR curves.

    Returns:
        dict with inference_time, per_sample_latency, y_test_pred, pr_plot_path
    """
    # Inference timing
    t_inf0 = time.perf_counter()
    y_test_pred = best_model.predict(X_test)
    inference_time = time.perf_counter() - t_inf0
    per_sample_latency = float(inference_time / max(1, len(y_test)))

    print(f"Testing {model_name} on {len(y_test)} samples..."
        f"Inference time on test ({len(y_test)}): {inference_time:.3f}s | "
        f"per-sample {per_sample_latency*1000:.2f} ms"
    )

    # Optional PR curves if proba available
    pr_plot_path = None
    if bool(
        ((cfg.get("evaluation", {}) or {}).get("pr_curves", {}) or {}).get("plot", True)
    ):
        pr_plot_path = generate_pr_curves(best_model, X_test, y_test, subdirs, model_name)

    return {
        "inference_time": inference_time,
        "per_sample_latency": per_sample_latency,
        "y_test_pred": y_test_pred,
        "pr_plot_path": pr_plot_path,
    }


def generate_pr_curves(best_model, X_test, y_test, subdirs, model_name="Best Model"):
    """Generate PR curves for the best model if predict_proba is available."""
    try:
        if hasattr(best_model, "predict_proba"):
            

            proba = best_model.predict_proba(X_test)
            classes = get_classes_safe(best_model)
            if classes is not None:
                lb = LabelBinarizer()
                lb.fit(classes)
                y_bin = lb.transform(y_test)
                pr_curves = {}
                for i, cls in enumerate(classes):
                    try:
                        precision, recall, _ = precision_recall_curve(
                            y_bin[:, i], proba[:, i]
                        )
                        ap = average_precision_score(y_bin[:, i], proba[:, i])
                        pr_curves[str(cls)] = {
                            "precision": precision,
                            "recall": recall,
                            "average_precision": float(ap),
                        }
                    except Exception:
                        continue
                return plot_per_class_pr_curves(
                    pr_curves, title=f"PR Curves - {model_name}", subdirs=subdirs
                )
        else:
            print("Best model lacks predict_proba; skipping PR curves.")
            return None
    except Exception as e: #pylint: disable=broad-except
        print(f"PR curve plotting failed: {e}")
        return None


def get_classes_safe(model):
    """Safely extract classes from a model."""
    try:
        if hasattr(model, "classes_"):
            return model.classes_
        elif hasattr(model, "named_steps") and "classifier" in model.named_steps:
            classifier = model.named_steps["classifier"]
            if hasattr(classifier, "classes_"):
                return classifier.classes_
        return None
    except Exception:
        return None


def performcalibration_analysis(
    best_model,
    X_train,
    y_train,
    X_test,
    y_test,
    model_name: str,
    cfg: Dict[str, Any],
    subdirs: Dict[str, Path],
    bins: int = 10,
) -> Dict[str, Any]:
    """Perform pre- and optional post-calibration analysis.

    Returns:
        dict with pre_cal, post_cal, and plot paths
    """
    

    pre_cal = calibration_analysis(best_model, X_test, y_test, bins=bins)
    post_cal = None
    cal_plot_pre = None
    cal_plot_post = None
    cal_plot_comp = None

    # Pre-calibration plot
    if (
        isinstance(pre_cal, dict)
        and "calibration_frac_pos" in pre_cal
        and "calibration_mean_pred" in pre_cal
    ):
        cal_plot_pre = plot_reliability_curve(
            pre_cal["calibration_frac_pos"],
            pre_cal["calibration_mean_pred"],
            title=f"Reliability Curve - {model_name}",
            subdirs=subdirs,
        )

    # Post-calibration if enabled
    calib_cfg = (
        (cfg.get("evaluation", {}) or {}).get("calibration_posthoc", {})
        if isinstance(cfg, dict)
        else {}
    )

    if bool(calib_cfg.get("enabled", False)):
        post_cal, cal_plot_post, cal_plot_comp = perform_post_calibration(
            best_model,
            X_train,
            y_train,
            X_test,
            y_test,
            model_name,
            pre_cal,
            calib_cfg,
            cfg,
            subdirs,
            bins,
        )

    return {
        "pre_cal": pre_cal,
        "post_cal": post_cal,
        "plots": {
            "pre": cal_plot_pre,
            "post": cal_plot_post,
            "comparison": cal_plot_comp,
        },
    }


def perform_post_calibration(
    best_model,
    X_train,
    y_train,
    X_test,
    y_test,
    model_name,
    pre_cal,
    calib_cfg,
    cfg,
    subdirs,
    bins,
):
    """Perform post-hoc calibration analysis."""
    try:
        method = str(calib_cfg.get("method", "isotonic"))
        cv_folds = int(calib_cfg.get("cv", 3))
        allow_nested = bool(calib_cfg.get("allow_in_nested", False))

        # Use centralized n_jobs logic if not explicitly set in calibration config
        calib_n_jobs = calib_cfg.get("n_jobs", None)
        if calib_n_jobs is not None:
            n_jobs = calib_n_jobs
        else:
            n_jobs = get_effective_n_jobs(cfg)

        # Respect nested-CV guard if in progress
        if (
            bool(
                (cfg.get("evaluation", {}) or {})
                .get("nested_cv", {})
                .get("in_progress", False)
            )
            and not allow_nested
        ):
            raise RuntimeError(
                "Skipping post-hoc calibration inside nested CV (allow_in_nested=False)."
            )

        # Build calibrator on the full training data to avoid test leakage
        cal_est = CalibratedClassifierCV(
            best_model, method=method, cv=cv_folds, n_jobs=n_jobs
        )
        cal_est.fit(X_train, y_train)
        post_cal = calibration_analysis(cal_est, X_test, y_test, bins=bins)

        cal_plot_post = None
        cal_plot_comp = None

        if (
            isinstance(post_cal, dict)
            and pre_cal
            and "calibration_frac_pos" in post_cal
            and "calibration_mean_pred" in post_cal
        ):
            cal_plot_post = plot_reliability_curve(
                post_cal["calibration_frac_pos"],
                post_cal["calibration_mean_pred"],
                title=f"Reliability Curve - {model_name} (Calibrated)",
                subdirs=subdirs,
            )
            cal_plot_comp = plot_reliability_curve_comparison(
                pre_cal.get("plot_frac_pos", pre_cal.get("calibration_frac_pos")),
                pre_cal.get("plot_mean_pred", pre_cal.get("calibration_mean_pred")),
                post_cal.get("plot_frac_pos", post_cal.get("calibration_frac_pos")),
                post_cal.get("plot_mean_pred", post_cal.get("calibration_mean_pred")),
                title=f"Reliability Curve - {model_name} (Pre vs Post)",
                subdirs=subdirs,
            )

        return post_cal, cal_plot_post, cal_plot_comp

    except (ValueError, AttributeError, RuntimeError, TypeError) as e:
        print(f"Post-hoc calibration failed/skipped: {e}")
        return None, None, None


def computebootstrap_ci(
    y_test, y_test_pred, cfg: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Compute bootstrap confidence intervals for key metrics."""
    try:
 

        n_iter = int(cfg.get("evaluation", {}).get("bootstrap_iterations", 1000))
        random_state = int(cfg.get("evaluation", {}).get("bootstrap_random_state", 25))

        acc_l, acc_u = bootstrap_ci(
            y_test,
            y_test_pred,
            accuracy_score,
            n_iter=n_iter,
            random_state=random_state,
        )
        f1_l, f1_u = bootstrap_ci(
            y_test,
            y_test_pred,
            lambda yt, yp: f1_score(yt, yp, average="macro", zero_division=0),
            n_iter=n_iter,
            random_state=random_state,
        )
        ba_l, ba_u = bootstrap_ci(
            y_test,
            y_test_pred,
            balanced_accuracy_score,
            n_iter=n_iter,
            random_state=random_state,
        )

        return {
            "accuracy_ci95": [acc_l, acc_u],
            "macro_f1_ci95": [f1_l, f1_u],
            "balanced_accuracy_ci95": [ba_l, ba_u],
        }
    except Exception:
        return None
